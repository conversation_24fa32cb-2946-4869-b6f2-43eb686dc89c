# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
###> hslavich/oneloginsaml-bundle ###
IDP_ENTITYID=mmw
IDP_SINGLESIGNONSERVICE_URL=https://idpf-preprod.mpsa.com:443/am/SSORedirect/metaAlias/psa/mmw/idp
IDP_SINGLESIGNONSERVICE_BINDING=urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect
IDP_SINGLELOGOUTSERVICE_URL=https://idp-preprod.mpsa.com:443/am/IDPSloRedirect/metaAlias/psa/mmw/idp
IDP_SINGLELOGOUTSERVICE_BINDING=urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect
IDP_X509CERT=%kernel.project_dir%/config/saml_cert.cer
SP_ENTITYID=http://back-dev.mym.com
SP_ASSERTIONCONSUMERSERVICE_URL=http://back-dev.mym.com/saml/acs
SP_ASSERTIONCONSUMERSERVICE_BINDING=urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect
SP_SINGLELOGOUTSERVICE_URL=http://back-dev.mym.com/saml/logout
SP_SINGLELOGOUTSERVICE_BINDING=urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect
SP_PRIVATEKEY=null
###> hslavich/oneloginsaml-bundle ###