properties([
    parameters([
        validatingString(name: 'version', regex: '\\b?[v][0-9]+\\.[0-9]+\\.[0-9]+(?:\\.[0-9]+)?\\b', failedValidationMessage:  'Version :  vX.Y.Z with X = n° release ; Y = n° sprint ;  Z = n° fix', defaultValue: '0.0.0', description: 'Version :  vX.Y.Z with X = n° release ; Y = n° sprint ;  Z = n° fix'),
string(name: 'justification', defaultValue: 'Justification', description: 'The justification for deploying')
])
])

node () {
    def projectName = env.BRANCH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    def directory = pwd();
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date());
    def targetEnvironment = 'prod';
    def prodVersion = currentDate + '_' + params.version;
    def appName = 'mymSettings'

    printf('current version  %1s', [prodVersion]);
    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);

    timestamps {
    try {
            // Checkout
            stageCheckout()

            //load common jenkinsfile
            def rootDir = pwd()
            def functions = load "${rootDir}/Jenkinsfile_common"

            //Initializate environment
            functions.stageInit()

            //Create tag version
            functions.stageTagGitCreate()

            //Install
            functions.stageInstall(prodVersion, projectName, targetEnvironment, appName)

            // Push Image
            functions.stagePushImage(prodVersion, targetEnvironment, appName)

            // DEPLOY
            functions.stageDeploy(prodVersion, targetEnvironment, appName)

            // SDI PROCESSING
            functions.stageTaaS(appName, prodVersion)

            // Clean Container
            functions.stageCleanContainer(currentDate)


        } catch(e) {
            throw e;
        } finally {
            try{
                //check
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}


////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
    }
}
