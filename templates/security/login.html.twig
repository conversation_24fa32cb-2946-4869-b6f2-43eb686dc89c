{% extends '_layout/base.html.twig' %}
{% block basebody %}
<body class="bg-gradient-primary">

  <div class="container">

    <!-- Outer Row -->
    <div class="row justify-content-center">

      <div class="col-xl-5 col-lg-6 col-md-8">

        <div class="card o-hidden border-0 shadow-lg my-5">
          <div class="card-body p-0">
            <!-- Nested Row within Card Body -->
            <div class="row">
              <div class="col-lg-12">
                <div class="p-5">
                  <div class="text-center">
                    <h1 class="h4 text-gray-900 mb-4">LOGIN BO V2</h1>
                  </div>
                  {% if error %}
                      <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
                  {% endif %}
                  <form method="post" class="user">
                    <div class="form-group">
                      <input type="text" name="username" value="{{ last_username }}" class="form-control form-control-user" id="inputUsername" placeholder="Username" required>
                    </div>
                    <div class="form-group">
                      <input type="password" name="password" class="form-control form-control-user" id="inputPassword" placeholder="Password" required>
                    </div>
                    <div class="form-group">
                      <div class="custom-control custom-checkbox small">
                        <input type="checkbox" class="custom-control-input" id="rememberMeCheck" name="_remember_me">
                        <label class="custom-control-label" for="rememberMeCheck">Remember Me</label>
                      </div>
                    </div>
                    <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                    <button class="btn btn-primary btn-user btn-block" type="submit">Login</button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

    </div>

  </div>

  <!-- Bootstrap core JavaScript-->
  <script src="js/jquery.min.js"></script>
  <script src="js/bootstrap.bundle.min.js"></script>

  <!-- Core plugin JavaScript-->
  <script src="js/jquery.easing.min.js"></script>

  <!-- Custom scripts for all pages-->
  <script src="js/sb-admin-2.min.js"></script>

</body>
{% endblock %}
