{% extends '_layout/base_back.html.twig' %}
{% import _self as formMacros %}
{% macro customPrototype(consentType,language) %}
    <div class="playlist_element">
        <div class="row mb-3">
            <div class="col-md-8">
                <hr />
            </div>
        </div>
        <h5 class="font-weight-bold">{{ 'consent_type_title' | trans }}</h5>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'consent_type_enabled' | trans }}
            </div>
            <div class="col-md-5">
                {{ form_widget(consentType.enabled) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'consent_type_text' | trans }}
            </div>
            <div class="col-md-5">
                {{ form_errors(consentType.label) }}
                {{ form_widget(consentType.label) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'consent_type_customerat' | trans }}
                <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
            {% if consentType.vars.value and consentType.vars.value.id and consentType.vars.value.customerAtField not in ["PRIVACY_CONSENT_EMAIL", "PRIVACY_CONSENT_PHONE", "PRIVACY_CONSENT_SMS", "PRIVACY_CONSENT_POSTAL"] %}
                {{ form_widget(consentType.customerAtField,  { 'attr': {'class': 'consent_type'}}) }}
                <a class="btn btn-danger mr-3 mt-4 delete_playlist" data-id="{{consentType.vars.value.id}}" data-language="{{language.code}}" data-toggle="modal" style="color: white">{% trans %}Supprimer{% endtrans %}</a>
            {% else %}
                {% if consentType.vars.value and consentType.vars.value.customerAtField %}
                    <span>{{consentType.vars.value.customerAtField}}</span>
                    {% else %}
                        {{ form_widget(consentType.customerAtField,  { 'attr': {'class': 'consent_type'}}) }}
                {% endif %}
            {% endif %}
            </div>
        </div>
        {# {% if consentType.vars.value and consentType.vars.value.id and consentType.vars.value.customerAtField not in ["PRIVACY_CONSENT_EMAIL", "PRIVACY_CONSENT_PHONE", "PRIVACY_CONSENT_SMS", "PRIVACY_CONSENT_POSTAL"] %}
            <a class="btn btn-danger mr-3 delete_playlist" data-id="{{consentType.vars.value.id}}" data-language="{{language.code}}" data-toggle="modal" style="color: white">{% trans %}Supprimer{% endtrans %}</a>

        {% endif %} #}
        <div class="d-none"> {{form_rest(consentType)}} </div>
    </div>
{% endmacro %}

{% block body %}
    <div id="marketingContents">
        {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
        {% for marketingConsent in form.marketingConsents %}
            {% set language = marketingConsent.vars.data.language %}
            <div class="card">
                <div class="card-header" id="{{'heading-' ~ language.code }}">
                    <h6 class="mb-0">
                        <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                           href="#section-{{ language.code }}" role="button" aria-expanded="false"
                           aria-controls="section-{{ language.code }}">
                            <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                            {{ language.label }}
                        </a>
                    </h6>
                </div>
                <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#marketingContents">

                    <div class="card-body" id="form_container"  data-language="{{language.code}}" >
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(marketingConsent.enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-7">
                                <a class="link-help" href='#help' id='link-help' data-language="{{language.code}}">{{ "help_marketing_consent" | trans }}</a>
                                <div id='img-help-{{language.code}}' style='display: none'>
                                    <img src="/images/dcp-help-fr.png">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "text_introduction" | trans }}
                            </div>
                            <div class="col-md-5">
                                {{ form_widget(marketingConsent.labelIntro) }}
                            </div>
                        </div>
                        <div id="consents-wrapper-{{language.code}}" class="consents-wrapper"
                             data-prototype="{{ formMacros.customPrototype(marketingConsent.consentTypes.vars.prototype)|e('html_attr') }}"
                             data-index="{{ marketingConsent.consentTypes|length }}">
                            {% for consent in marketingConsent.consentTypes %}
                                {{ formMacros.customPrototype(consent,language) }}
                            {% endfor %}
                        </div>
                        <div class="row">
                            <div class="col-md-8" style="text-align: center;font-weight: bold;text-transform: uppercase;">
                                <button class="btn btn-center btn-primary jslink" data-language="{{language.code}}">{{ 'consent_type_new' |trans }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('admin_marketing_consent_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
    {{ form_end(form) }}
    {% for marketingConsent in form.marketingConsents %}
    {% set language = marketingConsent.vars.data.language %}
    <div class="modal fade" id="delete-consent-modal-{{ language.code }}" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="#" id="delete-consent-form-{{ language.code }}" method="POST">
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete-{{ language.code }}">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}video_modal_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    {% endfor %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {

            $(".jslink").on('click', function(e) {
                e.preventDefault();
                language = $(this).data('language');
                $wrapper = $('#consents-wrapper-'+language);
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                var newForm = prototype.replace(/__name__/g, index);
                $wrapper.data('index', index + 1);
                $(this).closest('.row').before(newForm);
            });

           $('#marketingContents').on('click', '.delete_playlist', function(e) {
                language = $(this).data('language');
                deleteConsent =$('#delete-consent-form-'+language);
                modal= $('#delete-consent-modal-'+language);
                index = $('#consents-wrapper-'+language).data('index');
                action = "marketingconsent/delete/"+$(this).data("id");
                deleteConsent.attr('action', action);
                modal.modal();
            });

            $('#marketingContents .consent_type input[value="consent"][checked="checked"]').trigger('click');

            $(".link-help").bind('click', function(e) {
                language = $(this).data('language');
                if ($('#img-help-'+language).is(':visible')) {
                    $('#img-help-'+language).hide();
                    history.back();
                } else {
                    $('#img-help-'+language).show();
                }
            });
        });
    </script>
{% endblock %}
