{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('assistance_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.assistance_contact_enabled) }}
                            </div>
                        </div>
                        {% for language in profile.site.languages %}
                            {{ include('admin/assistance_parameters/l_parameters.html.twig', { 'formL': formApp, 'language': language, 'codeL': language.code, 'type':  'APP' }) }}
                        {% endfor %}
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formApp.assistance_contact_form_type) }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.assistance_contact_form_type) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formApp.assistance_contact_phone) }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.assistance_contact_phone) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_brand_title" | trans({'%brand%': brand_name(profile.site.brand)}) }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.assistance_brand_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formApp.assistance_brand_phone) }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.assistance_brand_phone) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_dealer_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.assistance_dealer_enabled) }}
                            </div>
                        </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('assistance_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.assistance_contact_enabled) }}
                            </div>
                        </div>
                        {% for language in profile.site.languages %}
                            {{ include('admin/assistance_parameters/l_parameters.html.twig', { 'formL': formWeb, 'language': language, 'codeL': language.code, 'type':  'WEB' }) }}
                        {% endfor %}
                    <div class="row mb-4" style = " display :none">
                        <div class="col-md-3"  >
                            {{ form_label(formWeb.assistance_contact_form_type) }}
                        </div>
                        <div class="col-md-3" >
                            {{ form_widget(formWeb.assistance_contact_form_type) }}
                        </div>
                    </div>
                        <div  class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formWeb.assistance_contact_phone) }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.assistance_contact_phone) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_brand_title" | trans({'%brand%': brand_name(profile.site.brand)}) }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.assistance_brand_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formWeb.assistance_brand_phone) }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.assistance_brand_phone) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "assistance_dealer_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.assistance_dealer_enabled) }}
                            </div>
                        </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right">
        <button id="form_submitor" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $("#form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
        });
    </script>
{% endblock %}
