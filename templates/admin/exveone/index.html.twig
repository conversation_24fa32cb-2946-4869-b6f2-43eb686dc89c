{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%} active {% endif %}" id="wsparameters-app">
                        <div id="exveone-app">
                       {{ form_start(formApp, {'attr': { 'name':'myCustomFormName' }}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{% trans %} exve1_app_label {% endtrans %}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">Activation</div>
                            <div class="col-md-4 ">
                                {{ form_widget(formApp['enabled'], {label_attr: {class: 'checkbox-custom '}}) }}
                            </div>
                        </div>
                            <div class="text-right mt-3">
                            <button class="btn btn-success form_submitor" type="submit">{{ button_label|default('save') |trans }}</button>
                        </div>
                        {{ form_end(formApp) }}
                       </div>
                  </div>
                  <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                       <div id="exveone-web">
                          {{ form_start(formWeb, {'attr': { 'name':'myCustomFormName' }}) }}
                             <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{% trans %} exve1_label {% endtrans %}</h5>
                             </div>
                         <div class="row mb-4">
                              <div class="col-md-3">Activation</div>
                              <div class="col-md-4 ">
                            {{ form_widget(formWeb['enabled'], {label_attr: {class: 'checkbox-custom '}}) }}
                              </div>
                         </div>
                           <div class="text-right mt-3">
                             <button class="btn btn-success form_submitor" type="submit">{{ button_label|default('save') |trans }}</button>
                          </div>
                         {{ form_end(formWeb) }}
                     </div>
                  </div>
              </div>
           </div>
      </div>
  </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
<script>
    $(document).ready(function() {
        $(".form_submitor").on('click', function() {
            var id = $('.card-header-tabs .nav-item .active').attr("href");
            $(id+" form").submit();
        })
    });
    </script>
{% endblock %}