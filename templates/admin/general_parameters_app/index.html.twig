{% extends '_layout/base_back.html.twig' %}

{% form_theme formLive 'theme/app_global_parameters_theme.html.twig' %}

{% form_theme formExport 'theme/app_global_parameters_theme.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#parameters-live">
                        <h6 class="m-0 font-weight-bold text-primary">Paramètres Live</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " data-toggle="tab" href="#parameters-export">
                        <h6 class="m-0 font-weight-bold text-primary">Paramètres export</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane active" id="parameters-live">
                    {{ form_start(formLive, {'action': path('general_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                       {{form_widget(formLive)}}
                        <div class="mt-2 float-right">
                            <button id="form_live" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{form_end(formLive)}}
                </div>
                <div class="tab-pane" id="parameters-export">
                     {{ form_start(formExport, {'action': path('general_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                       {{ form_widget(formExport) }}
                        <div class="mt-2 float-right">
                            <button id="form_export" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{form_end(formExport)}}
                </div>
            </div>
        </div>
    </div>

{% endblock %}
