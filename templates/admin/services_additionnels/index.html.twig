{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <ul class="nav nav-tabs card-header-tabs">
				<li class="nav-item">
					<a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#servicesadditionnels-app">
						<h6 class="m-0 font-weight-bold text-primary">APP</h6>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#servicesadditionnels-web">
						<h6 class="m-0 font-weight-bold text-primary">WEB</h6>
					</a>
				</li>
			</ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;height: 600px">
            <div class="card-body">
            	<div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%} active {% endif %}" id="servicesadditionnels-app">
                        <div id="servicesadditionnels-app">
                            {{ form_start(formApp, { 'attr': {'class': 'mt-1'}}) }}
                                <div>
                                    {% for service in services %}
                                        <h5 class="mb-4 text-gray-800">{{ service|trans }}</h5>
                                        {% set formService = formApp['form_' ~ service ] %}
                                        {{ include('admin/services_additionnels/service_prameters.html.twig', { 'formService': formService}) }}
                                        {% if loop.last %}
                                        {% else %}
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <hr style="border-top: 3px solid #8c8b8b;">
                                                </div>
                                                <div class="col-md-7">
                                                    <hr style="border-top: 3px solid #8c8b8b;">
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="text-right mt-3">
                                    <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                                </div>
                            {{ form_end(formApp) }}
                        </div>
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%} active {% endif %}" id="servicesadditionnels-web">
                        <div id="servicesadditionnels-web">
                            {{ form_start(formWeb, { 'attr': {'class': 'mt-1'}}) }}
                                <div>
                                    {% for service in services %}
                                        <h5 class="mb-4 text-gray-800">{{ service|trans }}</h5>
                                        {% set formService = formWeb['form_' ~ service ] %}
                                        {{ include('admin/services_additionnels/service_prameters.html.twig', { 'formService': formService}) }}
                                        {% if loop.last %}
                                        {% else %}
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <hr style="border-top: 3px solid #8c8b8b;">
                                                </div>
                                                <div class="col-md-7">
                                                    <hr style="border-top: 3px solid #8c8b8b;">
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="text-right mt-3">
                                    <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                                </div>
                            {{ form_end(formWeb) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
	{{ parent() }}
	<script>
		$(document).ready(function () {
$(".form_submitor").on('click', function () {
var id = $('.card-header-tabs .nav-item .active').attr("href");
$(id + " form").submit();
})

});
	</script>
{% endblock %}

