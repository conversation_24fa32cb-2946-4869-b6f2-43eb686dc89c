{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {{ form_start(form, {'action': path('scan_my_car_index',{'profile': profile.id}), 'method': 'POST'}) }}
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'scan_my_car_title'|trans }}</h6>
        </div>
        <div class="overflow-auto card-body">
          {% for language in site.languages %}
             <div id="scan-my-car">
                <div class="card">
                    <div class="card-header" id="{{'heading-' ~ language.code }}">
                        <h6 class="mb-0">
                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                               aria-controls="section-{{ language.code }}">
                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                {{ language.label }}
                            </a>
                        </h6>
                    </div>
                    <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#scan-my-car">
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    {{ "prdv_activation" | trans }}
                                </div>
                                <div class="col-md-3">
                                    {{ form_widget(form['enabled-'~language.code]) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
             </div>
          {% endfor %}
            <div class="mt-2 float-right">
                <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
        </div>
    </div>

    {{ form_end(form) }}
{% endblock %}