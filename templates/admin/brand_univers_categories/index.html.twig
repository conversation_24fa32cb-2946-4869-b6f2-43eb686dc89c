{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">Catégories Univers de marque</h1>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary"></h6>
        </div>
        <div class="card-body">
                <div class="mt-1 mb-4 text-right">
                        <a role="button" class="btn btn-primary" href="{{ path('brand_univers_categories_new', {'profile': profile.id}) }}">{% trans %}new_categorie{% endtrans %}</a>
                </div>
                <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}title{% endtrans %}</th>
                        <th class="text-center" style="width : 20%;">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for categorie in categories %}
                        <tr>
                            <td>{{ categorie.title }}</td>
                            <td class="text-center">
                                <a href="{{ path('brand_univers_categories_edit', {profile: profile.id, categorieID: categorie.id}) }}"
                                   class="btn btn-sm btn-warning">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-categorie-modal" class="btn btn-sm btn-danger"
                                   data-categorie-id="{{ categorie.id }}">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="2" class="text-center">
                                {% trans %}categories_list_empty{% endtrans %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-categorie-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('brand_univers_categories_delete', {profile: profile.id, categorieID: ':id'}) }}" id="delete-categorie-form" method="POST">
            {# <input type="hidden" name="token" value="{{ csrf_token('delete-media') }}"/> #}
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}confirmation_delete_categorie{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
            $(document).ready(function() {$('#dataTable').DataTable();});
            var $deleteCategorieModal = $('div#delete-categorie-modal'),
                $deleteCategorieForm  = $deleteCategorieModal.find('form#delete-categorie-form'),
                deleteCategorieAction = $deleteCategorieForm.attr('action');

            $('a[href="#delete-categorie-modal"]').on('click', function (event) {
                event.preventDefault();
                 $deleteCategorieForm.attr('action', deleteCategorieAction.replace(':id', $(this).attr('data-categorie-id')));
                $deleteCategorieModal.modal('show');
            });

            $deleteCategorieModal.on('hidden.bs.modal', function () {
                $deleteCategorieForm.attr('action', deleteCategorieAction);
            });
    </script>
{% endblock %}
