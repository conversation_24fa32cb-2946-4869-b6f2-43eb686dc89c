{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{{'premium_services' |trans}} </h1>
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                {% for language in profile.site.languages %}
                    <li class="nav-item">
                        <a class="nav-link {% if loop.first %}active{% endif %} show-table" data-toggle="tab" href="#tab_pane_{{language.code}}">
                        {{  language.label }}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                {% for language in profile.site.languages %}

                    <div class="tab-pane {% if loop.first == 1 %}active{% endif %}" id="tab_pane_{{language.code}}">
                        <div class="table-responsive">
                            <table class="table table-bordered dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                                <thead>
                                    <tr>
                                        <th class="text-primary"><small class="font-weight-bold">{{ 'service_id'|trans }}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{{ 'internal_code'|trans }}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{{ 'group'|trans }}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{{ 'title'|trans }}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{{ 'label'|trans }}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{% trans %}creation_date{% endtrans %}</small></th>
                                        <th class="text-primary"><small class="font-weight-bold">{% trans %}updated{% endtrans %}</small></th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for premiumServices in premiumServices[language.code] %}
                                    <tr>
                                        <td>
                                            {{ premiumServices.idServicePremium }}
                                        </td>
                                        <td>
                                            {{ premiumServices.codeIntern }}
                                        </td>
                                        <td>
                                            {{ premiumServices.groupe }}
                                        </td>
                                        <td>
                                            {{ premiumServices.titre }}
                                        </td>
                                        <td>
                                            {{ premiumServices.label }}
                                        </td>
                                        <td>{{ premiumServices.created ? premiumServices.created|date('Y-m-d H:i') : '-' }}</td>
                                        <td>{{ premiumServices.updated ? premiumServices.updated|date('Y-m-d H:i') : '-' }}</td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="4">no records found</td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                if(!$('.tab-pane.active .dataTable').hasClass('drawn')){
                    $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
                    );
                }
            });
            $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
            );
        });

    </script>
{% endblock %}