    <h1 class="h3 mb-4 text-gray-800">{{title}} </h1>
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                {% for language in profile.site.languages %}
                    <li class="nav-item">
                        <a class="nav-link {% if loop.first %}active{% endif %} show-table" data-toggle="tab" href="#tab_pane_{{language.code}}">
                        {{  language.label }}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                {% for language in profile.site.languages %}
                    <div class="tab-pane {% if loop.first == 1 %}active{% endif %}" id="tab_pane_{{language.code}}">
	                    <div class="table-responsive">
	                        <table class="table table-bordered dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
	                            <thead>
	                                <tr>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'code'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'label'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'status'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'thermic'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'electric'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'hybrid'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'maintenance'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{{ 'security'|trans }}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{% trans %}creation_date{% endtrans %}</small></th>
	                                    <th class="text-primary"><small class="font-weight-bold">{% trans %}update_date{% endtrans %}</small></th>
	                                </tr>
	                            </thead>
	                            <tbody>
	                            {% for operation in operations[language.code] %}
	                                <tr>
	                                    <td>
											{% if showCodeFunction %}
												{{ operation.code_family }}
											{% else %}
												{{operation.intervention_id}}
											{% endif %}
	                                        
	                                    </td>
	                                    <td>
	                                        {{ operation.intervention_label }}
	                                    </td>
	                                   <td class="text-center">
	                                        {% if operation.status %}
	                                            <span class="badge badge-success">{% trans %}Activé{% endtrans %}</span>
	                                        {% else %}
	                                            <span class="badge badge-secondary">{% trans %}Désactivé{% endtrans %}</span>
	                                        {% endif %}
	                                    </td>                            
	                                    
	                                    <td>
	                                        {{ operation.thermique ? '<i class="fa fa-check"/>' : ""}}
	                                    </td>
	                                    <td>
	                                        {{ operation.electrique ? '<i class="fa fa-check"/>' : "" }}
	                                    </td>
	                                    <td>
	                                        {{ operation.hybride ? '<i class="fa fa-check"/>' : "" }}
	                                    </td>
	                                    <td>
	                                        {{ operation.maintenance ? '<i class="fa fa-check"/>' : ""}}
	                                    </td>
	                                    <td>
	                                        {{ operation.security ? '<i class="fa fa-check"/>' : ""}}
	                                    </td>
	                                    <td>{{ operation.created ? operation.created|date('Y-m-d H:i') : '-' }}</td>
	                                    <td>{{ operation.updated is defined ? operation.updated|date('Y-m-d H:i') : '-' }}</td>
	                                </tr>
	                            {% else %}
	                                <tr>
	                                    <td colspan="4">no records found</td>
	                                </tr>
	                            {% endfor %}
	                            </tbody>
	                        </table>
	                    </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>