{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
parent :{{profile.site.id}}
    {{ include('admin/prdv_operations/_operations_list.html.twig', {'title' : 'Packages Family', 'operations': packagesFamily, 'showCodeFunction':true}) }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                if(!$('.tab-pane.active .dataTable').hasClass('drawn')){
                    $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
                    );
                }
            });
            $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
            );
        });
    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-packagesFamily-modal'),
                $deleteForm  = $deletekModal.find('form#delete-packagesFamily-form'),
                deleteAction = $deleteForm.attr('action');
            $('#dataTable').on('click', 'a[href="#delete-packagesFamily-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-packagesFamily-id')));
                $deletekModal.modal('show');
            });
            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
