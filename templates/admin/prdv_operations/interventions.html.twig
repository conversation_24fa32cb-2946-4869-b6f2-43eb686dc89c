{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    {{ include('admin/prdv_operations/_operations_list.html.twig', {'title' : 'Interventions', 'operations': interventions, 'showCodeFunction':false}) }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                if(!$('.tab-pane.active .dataTable').hasClass('drawn')){
                    $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
                    );
                }
            });
            $('.tab-pane.active .dataTable').addClass('drawn').DataTable(
                        {
                            "order": [[ 3, "desc" ]],
                            'autoWidth': false
                        }
            );
        });
    </script>
{% endblock %}
