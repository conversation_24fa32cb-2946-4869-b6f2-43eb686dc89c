{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans %}ws_parameters_app{% endtrans %}</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans %}ws_parameters_web{% endtrans %}</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;height: 600px">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%}active{% endif %}" id="wsparameters-app">
                        {{ form_start(formApp, {'action': path('ws_parameters_edit',{'profile': profile.id, 'brand': brand}), 'method': 'POST'}) }}
                            {% for section, values in structure %}
                                <h5 class="mb-4 text-gray-800">{{ section|trans }}</h5>
                                {% for key, value in values %}
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            {{ form_label(formApp[section ~ '-' ~ key]) }}
                                        </div>
                                        <div class="col-md-7">
                                            {{ form_widget(formApp[section ~ '-' ~ key]) }}
                                        </div>
                                        {% if loop.last %}
                                            <div class="col-md-3">
                                                <hr style="border-top: 3px solid #8c8b8b;">
                                            </div>
                                            <div class="col-md-7">
                                                <hr style="border-top: 3px solid #8c8b8b;">
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            {% endfor %}
                        {{ form_end(formApp) }}
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                        {{ form_start(formWeb, {'action': path('ws_parameters_edit',{'profile': profile.id, 'brand': brand}), 'method': 'POST'}) }}
                        {% for section, values in structure %}
                            <h5 class="mb-4 text-gray-800">{{ section|trans }}</h5>
                            {% for key, value in values %}
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        {{ form_label(formWeb[section ~ '-' ~ key]) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formWeb[section ~ '-' ~ key]) }}
                                    </div>
                                    {% if loop.last %}
                                        <div class="col-md-3">
                                            <hr style="border-top: 3px solid #8c8b8b;">
                                        </div>
                                        <div class="col-md-7">
                                            <hr style="border-top: 3px solid #8c8b8b;">
                                        </div>
                                    {% endif %}

                                </div>
                            {% endfor %}
                        {% endfor %}
                        {{ form_end(formWeb) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right">
        <button id="form_submitor" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $("#form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
        });
    </script>
{% endblock %}
