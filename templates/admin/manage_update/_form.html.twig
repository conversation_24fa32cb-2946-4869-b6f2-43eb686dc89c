{{ form_start(form, {'method': 'POST'}) }}
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.version) }}
                {{ form_label(form.version) }}
                    <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(form.version, {'attr': {'class': 'version'}}) }}
            </div>
        </div>
    </div>  
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.os_version_min) }}
                {{ form_label(form.os_version_min) }}
                    <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(form.os_version_min, {'attr': {'class': 'os_version_min'}}) }}
            </div>
        </div>
    </div>  
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.mym_version_min) }}
                {{ form_label(form.mym_version_min) }}
                    <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(form.mym_version_min) }}
            </div>
        </div>
    </div>  
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.mym_version_online) }}
                {{ form_label(form.mym_version_online) }}
                    <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(form.mym_version_online) }}
            </div>
        </div>
    </div>  
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.start_date_nr ) }}
                {{ form_label(form.start_date_nr) }}
                <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(form.start_date_nr)  }}
            </div>
        </div>
    </div>  
    <div class="form-group">
        <div class="row">
            <div class="col-md-3">
                {{ form_errors(form.end_date_certif) }}
                {{ form_label(form.end_date_certif) }}
            </div>
            <div class="col-md-5">
                {{ form_widget(form.end_date_certif) }}
            </div>
        </div>
    </div>  
    <div class="mt-2 mb-4 float-right">
        <button class="btn btn-primary float-right" type="submit">{{ 'save'|trans }}</button>
    </div>
{{ form_end(form) }}
<br/>
    <p>Time Zone : <b> {{ boTimeZone }} (GMT {{ timeZoneHour }})</b> </p>
