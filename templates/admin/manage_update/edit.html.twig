{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <h2>{% trans %}manage_update.title{% endtrans %}</h2>
        <div class="card shadow-sm">
            <div class="card-body">
                {{ include('admin/manage_update/_form.html.twig', {'button_label': 'Modifier' | trans}) }}
            </div>
        </div>
    <div class="mt-4 d-flex">
        <a class="mr-3 btn btn-dark" role="button" href="{{ path('manage_update_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
        {{ include('admin/manage_update/_delete_form.html.twig') }}
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('.version').change(function() {
            var osSelected = $(this).val();            
            $.ajax({
                url: "{{ path('manage_update_edit' , {'profile': profile.id,  'manageUpdate': manage_update.id}) }}",
                method: 'POST',
                data: { osSelected : osSelected },
                success: function(response) {
                    var mappedList = response.mappedList;
                    var osVersionMinField = $('.os_version_min');

                    // Clear existing options
                    osVersionMinField.empty();
                        $('.os_version_min').append($('<option>', { 
                        value: '',
                        text : '-> Choisissez' 
                    }));

                    // Add new options
                    $.each(mappedList, function(value, label) {
                        osVersionMinField.append($('<option>', {
                            value: value,
                            text: label
                        }));
                    });
                },
                error: function() {
                    alert('ERROR');
                    return false;
                }
            });
        });
    });
    </script>
{% endblock %}