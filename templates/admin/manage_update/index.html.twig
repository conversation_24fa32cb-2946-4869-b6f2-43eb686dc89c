{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <div class="card shadow mb-3">
        <div class="card">
            <a href="#manage_update" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="manage_update">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'manage_update.ajout'|trans }}</h6>
            </a>
            <!-- Card Content - Collapse -->
            <div class="collapse show" id="manage_update" style="">
                <div class="card-body">
                    {{ include('admin/manage_update/_form.html.twig') }}
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'manage_update.list' | trans }}</h6>
        </div>
        <div class="card-body">
            <div class="card">
                <div class="overflow-auto" style="overflow-y:scroll;">
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                                        <thead>
                                            <tr>
                                                <th class="text-primary">{{ 'manage_update.os_version_min' | trans }}</th>
                                                <th class="text-primary">{{ 'manage_update.mym_version_min' | trans }}</th>
                                                <th class="text-primary">{{ 'manage_update.mym_version_online' | trans }}</th>
                                                <th class="text-primary">{{ 'manage_update.start_date_nr' | trans }}</th>
                                                <th class="text-primary">{{ 'manage_update.end_date_certif' | trans }}</th>
                                                <th class="text-primary">{{ 'manage_update.version' | trans }}</th>
                                                <th class="text-primary text-right" width="20%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for manage_update in manage_updates %}
                                                <tr class = "row-elements">
                                                    <td nowrap>{{ manage_update.osVersionMin}}</td>
                                                    <td nowrap>{{ manage_update.mymVersionMin }}</td>
                                                    <td nowrap>{{ manage_update.mymVersionOnline }}</td>
                                                    <td nowrap>{{ manage_update.startDateNr  | date('Y-m-d H:i', boTimeZone) }}</td>
                                                    {% if manage_update.endDateCertif is not null %}
                                                        <td nowrap>{{ manage_update.endDateCertif | date('Y-m-d H:i', boTimeZone) }}</td>
                                                    {% else %}
                                                        <td nowrap>n/d</td>
                                                    {% endif %}
                                                    <td nowrap>{{ manage_update.version.type }}</td>
                                                    <td nowrap class="text-right">
                                                        <a role="button" class="btn btn-warning mr-1" href="{{ path('manage_update_edit', {'profile': profile.id, 'manageUpdate': manage_update.id}) }}">{% trans %}Modifier{% endtrans %}</a>
                                                        {{ include('admin/manage_update/_delete_form.html.twig') }}
                                                    </td>
                                                </tr>
                                            {% else %}
                                                <tr>
                                                    <td colspan="4">no records found</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <br/>
                                <p>Time Zone : <b> {{ boTimeZone }} (GMT {{ timeZoneHour }})</b> </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
    $(document).ready(function() {
        var dataTable = $('.dataTable');
        var dataRows = dataTable.find('.row-elements');
        if (dataRows.length > 0) {
            dataTable.DataTable({
            'order': [[1, 'desc']]
        });
        }
        $('.version').change(function() {
        var selectedValue = $(this).val();            
        $.ajax({
            url: "{{ path('manage_update_index' , {'profile': profile.id}) }}",
            method: 'POST',
            data: { selectedValue : selectedValue },
            success: function(response) {
                var mappedList = response.mappedList;
                var osVersionMinField = $('.os_version_min');

                // Clear existing options
                osVersionMinField.empty();
                    $('.os_version_min').append($('<option>', { 
                    value: '',
                    text : '-> Choisissez' 
                }));

                // Add new options
                $.each(mappedList, function(value, label) {
                    osVersionMinField.append($('<option>', {
                        value: value,
                        text: label
                    }));
                });
            },
            error: function() {
                alert('ERROR');
                return false;
            }
        });
    });
    });
    </script>
{% endblock %}