{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
	{% for type,messages in app.flashes() %}
        {% for message in messages %}
            <div class="alert alert-{{type}}">
                {{ message }}
            </div>
        {% endfor %}
    {% endfor %}

    {% for error in errors %}
        <div class="alert alert-danger">
            {{error. message }}
        </div>
    {% endfor %}

	<h3 class="h3 mb-4 text-gray-800">{% trans %}trans_files_export{% endtrans %}</h3>
    <div class="card shadow-sm">
    	
    	<div class="card-header">
    		{% trans %}schedule_export{% endtrans %}
    	</div>
    	{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
        <div class="card-body">

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.brands) }}
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.brands) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.support) }}
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.support) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <button class="btn btn-primary" type="submit">{{ 'export' |trans }}</button>
        </div>
        {{ form_end(form) }}
    </div>
    
    <div class="card shadow-sm mt-4">
    	<div class="card-header">
    		{% trans %}export_list{% endtrans %}
    	</div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTableExport" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="text-primary">
                        	<small class="font-weight-bold">
                        		{% trans %}brand{% endtrans %}
                        	</small>
                        </th>
                        <th class="text-primary">
                        	<small class="font-weight-bold">
                        		{% trans %}support{% endtrans %}
                        	</small>
                        </th>
                        <th class="text-primary">
                        	<small class="font-weight-bold">
                        		{% trans %}requestedAt{% endtrans %}
                        	</small>
                        </th>
                        <th class="text-primary">
                        	<small class="font-weight-bold">
                        		{% trans %}status{% endtrans %}
                        	</small>
                        </th>
                        <th>
                            <small>Télécharger</small>
                        </th>
                    </tr>
                    </thead>
                    <tbody class="modals_triggers">
                    {% for export in exports %}
                        <tr>
                            <td>
                            	<small>{{export.brand.name}}</small>
                            </td>
                            <td>
                            	<small>{{ export.support }}</small>
                            </td>
                            <td>
                            	<small>{{ export.createdAt|date('Y-m-d H:i:s') }}</small>
                            </td>
                            <td>
                            	{% if export.status == 0 %}
                            		<span class="badge badge-warning">En attente</span>
                            	{% elseif export.status == 2 %}
                            		<span class="badge badge-success">Terminé</span>
                            		
                                {% elseif export.status == 3 %}
                                    <span class="badge badge-danger">Failed</span>
                                {% elseif export.status == 1 %}
                                    <span class="badge badge-info">Encours</span>
                            	{% endif %}
                            </td>
                            <td>
                                {% if export.status == 2 %}
                                    {% for support in export.getSupport(true) %}
                                        
                                        <a href="{{path('export_app_features_download', {'id':export.id, 'support':support, 'profile': profile.id})}}"  class="btn btn-primary btn-sm"> 
                                           <i  class="fas fa-download"></i><small> {{support}} </small>
                                        </a>
                                    {% endfor %} 
                                {% endif %}

                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">{% trans %}Aucun enregistrement trouvé{% endtrans %}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
{% endblock %}
