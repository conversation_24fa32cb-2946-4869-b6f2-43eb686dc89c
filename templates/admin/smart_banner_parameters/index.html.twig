{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {{ form_start(form, {'action': path('smart_banner_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'smart_banner_title'|trans }}</h6>
            </div>
            <div class="overflow-auto card-body" style="overflow-y:scroll;height: 600px">
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "prdv_activation" | trans }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.enabled) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "Pages" | trans }}
                    </div>
                    <div class="col">
                        {{ form_widget(form.pages) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.app_icon_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.app_icon_url) }}
                    </div>
                </div>
                <div class="mb-3">
                    <h5 class="text-gray-900 font-weight-bold">ANDROID</h5>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.play_store_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.play_store_url) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.play_store_store_id) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.play_store_store_id) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.play_store_app_id) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.play_store_app_id) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.play_store_app_name) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.play_store_app_name) }}
                    </div>
                </div>
                <div class="mb-3">
                    <h5 class="text-gray-900 font-weight-bold">IOS</h5>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.app_store_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.app_store_url) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.app_store_store_id) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.app_store_store_id) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.app_store_app_id) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.app_store_app_id) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.app_store_app_name) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.app_store_app_name) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-2 float-right">
            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
        </div>
    {{ form_end(form) }}

{% endblock %}
