{% extends '_layout/base_back.html.twig' %}

{%  if type | upper in ['ALTRAN','CEA'] %}
    {% set innerType = 'smartapps' %}
{% elseif  type | upper in ['B9E','RCC','NAC', 'AIO']%}
    {% set innerType = 'boitier' %}
{% else %}
    {% set innerType = type %}
{% endif %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-body">
            {{ form_start(form, {}) }}
            <div class="mb-3">
                <h1 class="h3 mb-4 text-gray-800">{% trans %}Eligibilité véhicule{% endtrans %} - {{innerType | upper}}  {{ type | upper }}</h1>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ "activation" | trans }}
                </div>
                <div class="col-md-7">
                    {{ form_widget(form.enabled) }}
                </div>
            </div>
            {%  if type | upper in ['B9E'] %}
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ form_label(form.url) }}
                </div>
                <div class="col-md-7">
                    {{ form_widget(form.url) }}
                </div>
            </div>
            {% endif %}

            <div class="mt-2 float-right">
                <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>

   

    <div class="card shadow mb-4">
        <div class="card-header">
            <div class="mb-4 text-right">
                <a role="button" class="btn btn-primary" href="{{ path('eligibility_'~ innerType ~'_new' , {'profile': profile.id,'type':type}) }}">{% trans %}Ajouter{% endtrans %}</a>
            </div>
        </div>
        <div class="card-body">

            <div class="table-responsive">
                <table id="dataTable" class="table table-bordered table-hover dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}Libellé{% endtrans %}</th>
                        <th>LCDV</th>
                        <th>Attribut CORVET</th>
                        <th>Marque</th>
                        <th class="text-right">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for eligibility in eligibility_vehicles %}
                        <tr>
                            <td>{{ eligibility.label }}</td>
                            <td>{{ eligibility.lcdv }}</td>
                            <td>{{ eligibility.corvet }}</td>
                            <td>{{ eligibility.brand }}</td>
                            <td class="text-right">
                                <a href="{{ path('eligibility_'~ innerType ~'_edit', {'type':type,'profile': profile.id, 'id': eligibility.id}) }}" class="btn btn-sm btn-warning"
                                   data-toggle="tooltip" data-title="">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-eligibility-modal" class="btn btn-sm btn-danger"
                                   data-eligibility-id="{{ eligibility.id }}"
                                   data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% trans %}eligibility_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            {%  if type | upper in ['ALTRAN','CEA'] %}
                <div class="mb-4 text-right">
                    <a role="button" class="btn btn-primary" href="{{ path('eligibility_smartapps_whitelist' , {'profile': profile.id,'type':type}) }}">{% trans %}Whitelist{% endtrans %}</a>
                    <a role="button" class="btn btn-primary" href="{{ path('eligibility_smartapps_blacklist' , {'profile': profile.id,'type':type}) }}">{% trans %}Blacklist{% endtrans %}</a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block modals %}
<!-- DELETE -->
<div class="modal fade" id="delete-eligibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
    <form action="{{ path('eligibility_'~ innerType ~'_delete', {'type':type,profile: profile.id, id: ':id'}) }}" id="delete-eligibility-form" method="POST">
        <input type="hidden" name="token" value="{{ csrf_token('delete-eligibility') }}"/>
        <input type="hidden" name="_method" value="DELETE">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="delete">Confirmation</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette véhicule{% endtrans %} ?</div>
                <div class="modal-footer">
                    <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            let table = $('#dataTable').DataTable({
                    stateSave: true,
                    paging: true,
                    'order': [[ 1, "desc" ]]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text);
                    }
                });
            })
        });

    </script>

    <!-- DELETE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            var $deletekModal = window.$('div#delete-eligibility-modal'),
                $deleteForm  = $deletekModal.find('form#delete-eligibility-form'),
                deleteAction = $deleteForm.attr('action');

            window.$('a[href="#delete-eligibility-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteForm.attr('action', deleteAction.replace(':id', window.$(this).attr('data-eligibility-id')));

                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
