{% extends '_layout/base_back.html.twig' %}

{%  if innerType is not defined %}
    {% set innerType = type %}
{% endif %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800">Modifier le modèle de véhicule</h3>

    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.label) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.label) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.lcdv) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        {{ form_widget(form.lcdv) }}
                    </div>
                </div>
            </div>
             {% if innerType == "altran" %}
           
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.type) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-5">
                        {{ form_widget(form.type) }}
                    </div>
                </div>
            </div>
            {% else %}
            {% do form.type.setRendered() %}
            {% endif %}
             {% if innerType == "altran" %}
           
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.modelYear) }} 
                    </div>
                    <div class="col-md-5">
                        {{ form_widget(form.modelYear) }}
                    </div>
                </div>
            </div>
            {% else %}
            {% do form.modelYear.setRendered() %}
            {% endif %}
            {% if innerType == "altran" %}
           
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.productionDate) }} 
                    </div>
                    <div class="col-md-5">
                        {{ form_widget(form.productionDate) }}
                    </div>
                </div>
            </div>
            {% else %}
            {% do form.productionDate.setRendered() %}
            {% endif %}

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.brand) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.brand) }}
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.corvet) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.corvet) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-3 btn btn-dark" role="button" href="{{ path('eligibility_'~ type,{'profile': profile.id,'type':innerType}) }}">{% trans %}Annuler{% endtrans %}</a>
            <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
        </div>
    </div>
    {{ form_end(form) }}
{% endblock %}
