{% extends '_layout/base_back.html.twig' %}

{% block body %}
<div>
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app-o2c">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web-o2c">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
       
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app-o2c">
                    {{ form_start(formApp, {'action': path('o2x_settings_index',{'profile': profile.id, 'type': type}), 'method': 'POST'}) }}
                     <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">                
                                {{ formApp.type.vars.label | trans }} 
                             </h5>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_eligibility_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_eligibility_lcdv" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.eligibility_lcdv) }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_licence_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_licence_code" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.licence_code) }}
                            </div>
                        </div>
                         <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_indicateur_valet_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                 {{ "o2x.o2x_indicateur_valet_code" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.indicateur_valet_code) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_annuaire_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_annuaire_licence_ri" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.annuaire_licence_ri) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_annuaire_licence_ercs" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.annuaire_licence_ercs) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_version_minimale_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_version_minimal_app" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.version_minimal_app) }}
                            </div>
                        </div>
                          <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.activation_controle_vehicule" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.activation_controle_vehicule" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formApp.technical_vehicle_control) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_widget(formApp.type) }}
                            </div>
                        </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web-o2c">
                    {{ form_start(formWeb, {'action': path('o2x_settings_index',{'profile': profile.id, 'type': type}), 'method': 'POST'}) }}
                     <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">
                                {% if profile.site.brand == 'OP' %} 
                                {{ "o2x.o2ov_title" | trans }}
                            {% else %}
                                {{ "o2x.o2c_title" | trans }}
                            {% endif %}  
                             </h5>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_eligibility_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_eligibility_lcdv" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.eligibility_lcdv) }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_licence_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_licence_code" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.licence_code) }}
                            </div>
                        </div>
                         <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_indicateur_valet_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                 {{ "o2x.o2x_indicateur_valet_code" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.indicateur_valet_code) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_annuaire_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_annuaire_licence_ri" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.annuaire_licence_ri) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_annuaire_licence_ercs" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.annuaire_licence_ercs) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.o2x_version_minimale_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.o2x_version_minimal_app" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.version_minimal_app) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "o2x.activation_controle_vehicule" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "o2x.activation_controle_vehicule" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(formWeb.technical_vehicle_control) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_widget(formWeb.type) }}
                            </div>
                        </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right" style="width:100%">
        <button id="form_submitor" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
</div>


{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $("#form_submitor").on('click', function() {
                var id = $('.nav-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
        });

    </script>
{% endblock %}
