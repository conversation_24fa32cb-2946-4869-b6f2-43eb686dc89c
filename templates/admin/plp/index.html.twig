{% extends '_layout/base_back.html.twig' %}
{% set medias = profile.site.medias %}
{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
        .pdf-popup-modal .thumbnails li {
            width: 200px;
        }
        .pdf-popup-modal .thumbnails li .thumbnail p {
            font-size: 12px;
        }
        .pdf-popup-modal ul.thumbnails.image_picker_selector li .thumbnail.selected {
            background: unset;
            border: 2px solid #4e73df;
        }
       .accessory-container {
            padding: 20px;
        }
        .accessory-container:nth-child(odd) {
            background-color: #FAEADA;
        }
        .accessory-container:nth-child(even) {
            background-color: #F9FDF3;
        }     
    </style>
{% endblock %}
{% block body %}
    {{ include('admin/plp/lion_loyalty_program/lion_loyalty_program.html.twig', {'form': llpForm, 'section': llpForm.vars.id, 'is_current': llpForm.vars.id == currentSection }) }}
    {% for pillarForm in pillarForms %}
        {{ include('admin/plp/pillar/pillar.html.twig', {'form': pillarForm, 'order': loop.index, 'section': pillarForm.vars.id, 'is_current': pillarForm.vars.id == currentSection }) }}
    {% endfor %}
    {{ include('admin/plp/vehicle_courtesy/vehicle_courtesy.html.twig', {'form': vcForm, 'section': vcForm.vars.id, 'is_current': vcForm.vars.id == currentSection }) }}
    {{ include('admin/plp/accessories/accessories.html.twig', {'form': accessoriesForm, 'section': accessoriesForm.vars.id, 'is_current': accessoriesForm.vars.id == currentSection}) }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script>
        $(function () {
            $(".image-picker").imagepicker({hide_select: true});
            $(".pdf-picker").imagepicker({
                hide_select: true,   
                show_label: true
            });
            $(".delete-image").on('click', function() {
                $(this).addClass('d-none');
                parent = $(this).closest('.image-container');
                parent.children(".image-link-holder").removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
                parent.find(".image-picker-selector").val(null).change();
            });
            $(document).on('click', '.preview-image-change', function(event) {
                parent = $(this).closest('.image-container');
                linkSrc = parent.find(".image-picker-selector option:selected").data("img-real-src");
                imageSrc = parent.find(".image-picker-selector option:selected").data("img-src");
                if (!linkSrc) {
                    linkSrc = imageSrc;
                }
                imageAlt = parent.find(".image-picker-selector option:selected").data("img-alt");
                parent.children(".image-link-holder").attr("href", linkSrc).removeClass('d-none')
                    .children(".image-holder").attr("src", imageSrc).attr("alt", imageAlt);
                parent.find(".delete-image").removeClass('d-none');
            });
            
            $(".adopters_enablor").on('change', function() {
                $('.' + $(this).data('disable')).attr('disabled', !this.checked);
                $(".adopters_check").trigger('change');
            });

            $(".adopters_check").on('change', function() {
                if (this.checked) {
                    $('.' + $(this).data('hide')).show();
                } else {
                    $('.' + $(this).data('hide')).hide();
                }
            });
            $(".adopters_enablor").trigger('change');

            $('.jslink').on('click', function(e) {
                var $wrapper = $(this).closest('.accessories-fields').find('.accessories-wrapper');
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                newForm = newForm.replace(/__INDEXOR__/, (index + 1));
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $wrapper.append(newForm);
                $(".image-picker").imagepicker({hide_select: true});
            });
            $('.accessories-wrapper').on('click', '.delete-accessory', function() {
                var $wrapper = $(this).closest('.accessories-fields').find('.accessories-wrapper');
                var index = $wrapper.data('index');
                $(this).closest('.accessory-container').remove();
                $wrapper.data('index', index - 1);
            });
        });
    </script>
{% endblock %}
