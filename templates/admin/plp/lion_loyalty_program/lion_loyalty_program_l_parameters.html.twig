
<div class="row">
    <div class="col-md-3">{{ form_label(formL.title) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.title) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.description_no_member) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.description_no_member) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.description_member) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.description_member) }}
    </div>
</div>
<div class="row mt-5">
    {% set imageNotExist = (formL.vars.data.image_no_member is not defined) or (not formL.vars.data.image_no_member) %}
    {% set imageModalId = section ~ '-' ~ language.code ~ '-nomember' %}
    <div class="col-3">{{ form_label(formL.image_no_member) }}</div>
    <div class="col-9 image-container">
        <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
            {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.image_no_member.path }}" {% endif %} style="text-decoration: none">
            <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                src="{{ mediaUrl ~ '/' ~ formL.vars.data.image_no_member.path }}" alt="{{ formL.vars.data.image_no_member.textAlt }}" {% endif %}>
        </a>
        <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
        <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
        {{ include('admin/plp/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': formL.image_no_member}) }}
    </div>
</div>
<div class="row mt-5">
    {% set imageNotExist = (formL.vars.data.image_member is not defined) or (not formL.vars.data.image_member) %}
    {% set imageModalId = section ~ '-' ~ language.code ~ '-member' %}
    <div class="col-3">{{ form_label(formL.image_member) }}</div>
    <div class="col-9 image-container">
        <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
            {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.image_member.path }}" {% endif %} style="text-decoration: none">
            <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                src="{{ mediaUrl ~ '/' ~ formL.vars.data.image_member.path }}" alt="{{ formL.vars.data.image_member.textAlt }}" {% endif %}>
        </a>
        <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
        <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
        {{ include('admin/plp/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': formL.image_member}) }}
    </div>
</div>