{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {{ form_start(form, {'action': path('promote_app_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'promote_app_title'|trans }}</h6>
            </div>
            <div class="overflow-auto card-body" style="overflow-y:scroll;height: 600px">
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "prdv_activation" | trans }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.enabled) }}
                    </div>
                </div>
                <div class="mb-3">
                    <h5 class="text-gray-900 font-weight-bold">IOS</h5>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.ios_icon_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.ios_icon_url) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.ios_store_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.ios_store_url) }}
                    </div>
                </div>
                <div class="mb-3">
                    <h5 class="text-gray-900 font-weight-bold">ANDROID</h5>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.android_icon_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.android_icon_url) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ form_label(form.android_store_url) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.android_store_url) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-2 float-right">
            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
        </div>
    {{ form_end(form) }}

{% endblock %}
