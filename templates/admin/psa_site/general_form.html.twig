<div class="mt-2">
    <div class="row mb-4">
        <div class="col-md-3">
            {{ form_label(form.label) }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-7">
            {{ form_widget(form.label) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {% if not site.id %}
                {{ form_label(form.code) }} <span class="mandatory">*</span>
            {% else %}
                {{ 'site_code'|trans  }} <span class="mandatory">*</span>
            {% endif %}
        </div>
        <div class="col-md-3">
            {% if not site.id %}
                {{ form_widget(form.code) }}
            {% else %}
                <input type="text" class="form-control" value="{{ site.country }}" disabled>
            {% endif %}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'online_application'|trans  }}
        </div>
        <div class="col-md-7">
            {{ form_widget(form.online_application) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'online_site'|trans  }}
        </div>
        <div class="col-md-7">
            {{ form_widget(form.onlineSite) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ form_label(form.timezone) }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            {{ form_widget(form.timezone) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'brand'|trans  }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            {% if not site.id %}
                {{ form_widget(form.brand) }}
            {% else %}
                <input type="text" class="form-control" value="{{ brand_value }}" disabled>
            {% endif %}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'ldap_code'|trans  }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            {{ form_widget(form.ldapCode, {'attr':{'value': site.ldapCode}}) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3"> 
            {{ 'domtom'|trans  }} 
        </div>
        <div class="col-md-3">
            {{ form_widget(form.domtom) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'default_civility'|trans  }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            {{ form_widget(form.civility) }}
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            {{ 'name_order'|trans  }} <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            {{ form_widget(form.isReversedNameOrder) }}
        </div>
    </div>
</div>
