<div class="mt-2">
    <div class="row">
        <div class="col-md-3 col-3">
            <label class="" for="psa_site_languages_selected_languages">{{ 'site_languages'|trans }}</label> <span class="mandatory">*</span>
        </div>
        <div class="col-md-3 col-3">
            <span>{{ 'selected_values'|trans }}</span> <span class="mandatory">*</span>
        </div>
        <div class="col-md-1">
        </div>
        <div class="col-md-3">
            <p>{{ 'unselected_values'|trans }}</p>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
        </div>
        <div class="col-md-3 mt-3">
            <select id="psa_site_languages_selected_languages" name="selected_languages[]" size="4" class="form-control {% if invalid_languages %}is-invalid{% endif %}" multiple="multiple">
                {% for language in languages_form_selected_languages %}
                    <option value="{{ language.id }}">{{ language.label }}  ({{ language.translate }})</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-1 mt-4">
            <button type="button" id="psa_site_languages_selected_languages_rightSelected"  class="btn btn-primary btn-block multiselect-btn-center">
                <i class="fa fa-chevron-right"></i>
            </button>
            <button type="button" id="psa_site_languages_selected_languages_leftSelected" class="btn btn-primary btn-block multiselect-btn-center">
                <i class="fa fa-chevron-left"></i>
            </button>
        </div>
        <div class="col-md-3">
            <select id="psa_site_languages_selected_languages_to" name="unselected_languages[]" size="6" class="form-control" multiple="multiple">
                {% for language in languages_form_unselected_languages %}
                    <option value="{{ language.id }}">{{ language.label }}  ({{ language.translate }})</option>
                {% endfor %}
            </select>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-3">
            <label class="" for="prefered_language">{{ 'reference_language'|trans }}</label> <span class="mandatory">*</span>
        </div>
        <div class="col-md-3">
            <select id="prefered_language" name="prefered_language" class="form-control">
                {% for language in languages %}
                    <option value="{{ language.id }}" {% if site.preferedLanguage and site.preferedLanguage.id == language.id %}selected{% endif %}>{{ language.label }}</option>
                {% endfor %}
            </select>
        </div>
    </div>
</div>
