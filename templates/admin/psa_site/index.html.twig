{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for error in errors %}
        <div class="alert alert-danger">
            {{error.origin.name | trans}} : {{ error.message|trans }}
        </div>
    {% endfor %}
    {{ form_start(form, { 'id': 'psa_site_form', 'action': path(action,{'profile': profile.id}), 'method': 'POST'}) }}
        <div class="card shadow mb-3">
            <div class="accordion" id="accordionParent">
                <div class="card">
                    <a href="#site_general" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_general">
                        <h6 class="m-0 font-weight-bold text-primary">{{ 'site_general'|trans }}</h6>
                    </a>
                    <!-- Card Content - Collapse -->
                    <div class="collapse show" id="site_general" style="">
                        <div class="card-body">
                            {{ include('admin/psa_site/general_form.html.twig') }}
                        </div>
                    </div>
                </div>
                <div class="card">
                    <a href="#site_language" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_language">
                        <h6 class="m-0 font-weight-bold text-primary">{{ 'languages'|trans }}</h6>
                    </a>
                    <!-- Card Content - Collapse -->
                    <div class="collapse show" id="site_language" style="">
                        <div class="card-body">
                            {{ include('admin/psa_site/language_form.html.twig') }}
                        </div>
                    </div>
                </div>
                <div class="card">
                    <a href="#site_settings" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_settings">
                        <h6 class="m-0 font-weight-bold text-primary">{{ 'site_settings'|trans }}</h6>
                    </a>
                    <!-- Card Content - Collapse -->
                    <div class="collapse show" id="site_settings" style="">
                        <div class="card-body">
                            {{ include('admin/psa_site/setting_form.html.twig') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% if not site.id %}
            <input type="hidden" name="new_site" value="1">
            <div class="mt-2 mb-4 float-right">
                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_profile',{'profile': profile.id}) }}">{{ "Annuler"|trans }}</a>
                <button id="form_submitor" class="btn btn-primary float-right" type="submit">{{ 'site_create'|trans }}</button>
            </div>
        {% else %}
            <div class="mt-2 mb-4 float-right">
                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_profile',{'profile': profile.id}) }}">{{ "Annuler"|trans }}</a>
                <button id="form_submitor" class="btn btn-primary float-right" type="submit">{{ 'save'|trans }}</button>
            </div>
        {% endif %}
    {{ form_end(form) }}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/multiselect.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#psa_site_languages_selected_languages').multiselect();
            $('#psa_site_general_currencies').multiselect();

            $("form[name = 'psa_site_general']").submit(function(e) {
                e.preventDefault();

                $(' #psa_site_languages_selected_languages option').prop("selected", true);
                $('#psa_site_general_currencies option').prop("selected", true);

                $(this).off('submit').submit();
            })
        });
    </script>
{% endblock %}
