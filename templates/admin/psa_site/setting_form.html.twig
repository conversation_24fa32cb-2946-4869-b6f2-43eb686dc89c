<div class="mt-2">

    {% for section, settings in settingsBySection %}
        <h6 class="text-gray-900 font-weight-bold">{{ section|trans|capitalize }}</h6>
        {% for settingKey, setting in settings %}
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="" for="{{ settingKey }}">{{ settingKey|trans }}</label>
                </div>
                <div class="col-md-6">
                    {% if setting.type == "select" %}
                        <select id="{{ settingKey }}" name="{{ settingKey }}" class="form-control">
                            <option value=""></option>
                            {% for optionsValue, optionsLabel in setting.values %}
                                <option value="{{ optionsValue }}" {% if optionsValue == setting.value %}selected{% endif %}>{{ optionsLabel }}</option>
                            {% endfor %}
                        </select>
                    {% elseif setting.type == "radio"%}
                        {% for optionsValue, optionsLabel in setting.values %}
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" id="{{ optionsValue }}" name="{{ settingKey }}" class="custom-control-input" value="{{ optionsValue }}"
                                       {% if optionsValue == setting.value %}checked{% endif %}>
                                <label class="custom-control-label" for="{{ optionsValue }}">{{ optionsLabel }}</label>
                            </div>
                        {% endfor %}
                    {% elseif setting.type == "psa_currency" %}
                        {{ include('admin/psa_site/currencies_form.html.twig') }}
                    {% endif %}
                </div>
            </div>
        {% endfor %}
        {% if not loop.last %}
            <div class="row mb-1">
                <div class="col-md-9">
                    <hr style="border-top: 2px solid #8c8b8b;">
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>
