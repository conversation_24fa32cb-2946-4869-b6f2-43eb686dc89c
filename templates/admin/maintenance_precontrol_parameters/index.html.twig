{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('maintenance_precontrol_settings_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "precontrol_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "precontrol_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.precontrol) }}
                            </div>
                        </div>
                        <div class="mt-2 float-right">
                            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('maintenance_precontrol_settings_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "precontrol_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "precontrol_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.precontrol) }}
                            </div>
                        </div>
                        <div class="mt-2 float-right">
                            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'technical_controls_title' | trans | upper }}</h6>
        </div>
        <div class="card-body">
                <div class="mb-4 text-right">
                    <a role="button" class="btn btn-primary" href="{{ path('maintenance_precontrol_settings_add' , {'profile': profile.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
                </div>
                <div class = "row">
                    <div class = "col-12">
                        <table id="controls" class="table table-bordered table-hover">
                            <thead>
                                <tr class="text-primary">
                                    <th></th>
                                    <th>{{ 'repetitions' | trans }}</th>
                                    <th>{{ 'interval' | trans }}</th>
                                    <th>{{ 'interval_unit' | trans }}</th>
                                    <th>{{ 'alert' | trans }}</th>
                                    <th>{{ 'alert_unit' | trans }}</th>
                                    <th class="text-right">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if controls is not empty %}
                                    {% for control in controls %}
                                        <tr data-id="{{ control.id }}" data-order="{{ control.controlOrder }}">
                                            <td class="text-center text-primary"><i class="fas fa-arrows-alt"></i></td>
                                            <td>{{ control.controlRepeat }}</td>
                                            <td>{{ control.controlInterval }}</td>
                                            <td>
                                                {% if control.intervalUnit == "Y" %}
                                                    {{ "year" | trans }}
                                                {% else %}
                                                    {{ "month" | trans }}
                                                {% endif %}
                                            </td>
                                            <td>{{ control.alert }}</td>
                                            <td>
                                                {% if control.alertUnit == "Y" %}
                                                    {{ "year" | trans }}
                                                {% else %}
                                                    {{ "month" | trans }}
                                                {% endif %}
                                            </td>
                                            <td class="text-right"><small>
                                                <a role="button" class="btn btn-sm btn-warning mr-1"
                                                href="{{ path('maintenance_precontrol_settings_edit', {'profile': profile.id, 'control': control.id}) }}"
                                                >
                                                    {% trans %}Modifier{% endtrans %}
                                                </a>
                                                <a href="#delete-control-modal" class="btn  btn-sm btn-danger mr-1"
                                                data-control-id="{{ control.id }}"
                                                data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                {% trans %}Liste Vide{% endtrans %}!
                                            </td>
                                        </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-control-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
		<form action="{{ path('maintenance_precontrol_settings_delete', {profile: profile.id, control: ':id'}) }}" id="delete-control-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-control') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}control_modal_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/sortable.min.js') }}"></script>
    <script src="{{ asset('js/axios.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            var $deletekModal = $('div#delete-control-modal'),
                $deleteForm  = $deletekModal.find('form#delete-control-form'),
                deleteAction = $deleteForm.attr('action');
            $('#controls').on('click', 'a[href="#delete-control-modal"]', function (event) {
                console.log('f');
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-control-id')));
                $deletekModal.modal('show');
            });
            {% if controls is not empty %}
                var sortable = Sortable.create(document.querySelector('table#controls tbody'), {
                    onEnd: function () {
                        sortable.option("disabled", true);

                        axios({
                            method: 'put',
                            url: "{{ path('maintenance_precontrol_settings_reorder', {profile: profile.id}) }}",
                            data: {
                                ids: sortable.toArray()
                            },
                        }).then(function () {
                            document.querySelectorAll('table#controls td.order').forEach(function (node, i) {
                                node.innerHTML = i + 1;
                            });
                        }).catch(function () {
                            alert('Error !');
                        }).finally(function () {
                            sortable.option("disabled", false);
                        });
                    },
                });
            {% endif %}
        });
    </script>
{% endblock %}
