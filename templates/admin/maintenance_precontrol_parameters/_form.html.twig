{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'repetitions' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-1 number">
                    {{ form_widget(form.controlRepeat) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'interval' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-1 number">
                    {{ form_widget(form.controlInterval) }}
                </div>
                <div class="col-md-2">
                    {{ form_widget(form.intervalUnit) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'alert' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-1 number">
                    {{ form_widget(form.alert) }}
                </div>
                <div class="col-md-2">
                    {{ form_widget(form.alertUnit) }}
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('maintenance_precontrol_settings_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
            {% if update == true %}
                <a href="#delete-control-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-control-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
            {% endif %}
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% if update == true %}
	<div class="modal fade" id="delete-control-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
		<form action="{{ path('maintenance_precontrol_settings_delete', {profile: profile.id, control: id}) }}" id="delete-control-form" method="POST">
			<input type="hidden" name="token" value="{{ csrf_token('delete-control') }}"/>
			<input type="hidden" name="_method" value="DELETE">

			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="delete">Confirmation</h5>
						<button class="close" type="button" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
					<div class="modal-body">{% trans %}control_modal_delete{% endtrans %}</div>
					<div class="modal-footer">
						<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
						<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
					</div>
				</div>
			</div>
		</form>
	</div>
{% endif %}