{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
<div class="card shadow-sm">
	<div class="card-body">

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'priority' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.priority) }}
					</span>
					{{ form_widget(form.priority) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'service' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.service) }}
					</span>
					{{ form_widget(form.service) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'online' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.online) }}
					</span>
					{{ form_widget(form.online) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'version' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.version) }}
					</span>
					{{ form_widget(form.version) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			{% set languagesLength = form.translations.count %} 
			{% for formL in form.translations  %}
				{% if languagesLength == 1 %}
					<div class="row">
						<div class="col-md-3">{{ form_label(formL.title) }}</div>
						<div class="col-md-7">
							{{ form_widget(formL.title) }}
						</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-3">{{ form_label(formL.description) }}</div>
						<div class="col-md-7">
							{{ form_widget(formL.description) }}
						</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-3">{{ form_label(formL.cta) }}</div>
						<div class="col-md-3">
							{{ form_widget(formL.cta) }}
						</div>
					</div>
				{% elseif languagesLength > 1 %}
					{% set language = formL.vars.value.language %} 
					<div class="card">
						<div class="card-header" id="{{'heading-' ~ language.code }}" style="background-color: lemonchiffon;">
							<h6 class="mb-0">
								<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{ language.code }}">
									<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
									{{ language.label }}
								</a>
							</h6>
						</div>
						<div id="{{ 'section-' ~ language.code }}" class="collapse" aria-labelledby="{{'heading-' ~ language.code }}">
							<div class="card-body" id="form_container" data-language="{{language.code}}">
								<div class="row">
									<div class="col-md-3">{{ form_label(formL.title) }}</div>
									<div class="col-md-7">
										{{ form_widget(formL.title) }}
									</div>
								</div>
								<div class="row mt-2">
									<div class="col-md-3">{{ form_label(formL.description) }}</div>
									<div class="col-md-7">
										{{ form_widget(formL.description) }}
									</div>
								</div>
								<div class="row mt-2">
									<div class="col-md-3">{{ form_label(formL.cta) }}</div>
									<div class="col-md-3">
										{{ form_widget(formL.cta) }}
									</div>
								</div>
							</div>
						</div>
					</div>
				{% endif %}

			{% endfor %}
		</div>


	</div>
	<div class="card-footer text-right">
		<a class="mr-1 btn btn-dark" role="button" href="{{ path('homepage_promotion_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
		{% if update == true %}
			<a href="#delete-homepage-promotion-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-homepage-promotion-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
		{% endif %}
		<button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
	</div>
</div>
{{ form_end(form) }}
{% block modals %}
	{% if update == true %}
		<div class="modal fade" id="delete-homepage-promotion-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
			{% set id = form.vars.value.id %}
			<form action="{{ path('homepage_promotion_delete', {profile: profile.id, promotion: id}) }}" id="delete-homepage-promotion-form" method="POST">
				<input type="hidden" name="token" value="{{ csrf_token('delete-homepage-promotion') }}"/>
				<input type="hidden" name="_method" value="DELETE">

				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5 class="modal-title" id="delete">Confirmation</h5>
							<button class="close" type="button" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true">×</span>
							</button>
						</div>
						<div class="modal-body">{% trans %}ev_routing_delete{% endtrans %}</div>
						<div class="modal-footer">
							<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
							<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	{% endif %}


{% endblock %}
