<form class="d-inline-block" method="post" action="{{ path('ds_lcdv_delete', {'id': ds_lcdv.id, 'profile' : profile.id}) }}" id="form-{{ ds_lcdv.id }}"
      onsubmit="return false;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ ds_lcdv.id) }}">
    <button class="btn btn-danger text-right" data-toggle="modal" data-target="#deleteModal{{ ds_lcdv.id }}">{% trans %}Supprimer{% endtrans %}</button>
</form>
<!-- Logout Modal-->
<div class="modal fade" id="deleteModal{{ ds_lcdv.id }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{% trans %}Suppression du homepage promotion{% endtrans %}</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-left">{% trans %}êtes-vous sûr de vouloir le supprimer{% endtrans %} ?</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                <button class="btn btn-danger" type="button" onclick="$('#form-{{ ds_lcdv.id }}').attr('onsubmit', 'return true').submit();">{% trans %}Supprimer{% endtrans %}</button>
            </div>
        </div>
    </div>
</div>