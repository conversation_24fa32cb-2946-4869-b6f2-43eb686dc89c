{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img {
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <div class="card shadow mb-4">
        <div class="card-body">
                {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                    <div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'homepage_promotion.activation' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.enabled) }}
                            </div>
                        </div>
                    </div>
                    <div class="text-right mt-3">
                        <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                    </div>
                {{ form_end(form) }}
                </div>
                </div>
    <div class="card shadow mb-4">
        <div  style="overflow-y:scroll;">
            <div class="card-body">
                <div class="tab-content">
                       <h1 class="h3 mb-4 text-gray-800">{{ 'homepage_promotion.title' | trans | upper }}</h1>
                        <div class="mb-4 text-right">
                            <a role="button" class="btn btn-primary"
                               href="{{ path('homepage_promotion_add' , {'profile': profile.id }) }}">{% trans %}Ajouter{% endtrans %}</a>
                        </div>
                        <div class="card shadow mb-4">
                            <div class="card-body">
                            
                                <div class="table-responsive">
                                    <table class="table table-bordered dataTable" id="dataTable" width="100%"
                                           cellspacing="0" role="grid" aria-describedby="dataTable_info"
                                           style="width: 100%;">
                                        <thead>
                                        <tr>
                                            <th class="text-primary">{{ 'Priority' |trans }}</th>
                                            <th class="text-primary">{{ 'Title' |trans }}</th>
                                            <th class="text-primary">
                                                CTA
                                            </th>
                                            <th class="text-primary">
                                                Service
                                            </th>
                                            <th class="text-primary">
                                                Release
                                            </th>
                                            <th class="text-primary">{% trans %}Online{% endtrans %}</th>
                                            <th class="text-primary text-right" width="20%">Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                     {% for promotion in promotions %}
                                            <tr>
                                                <td>{{ promotion.priority }}</td>
                                                <td>
                                                    <p>
                                                        {% for key, promotionTranslation in promotionTranslations %}
                                                            {% if key == promotion.id %}
                                                                {% for translation in promotionTranslation %}
                                                                    {{ translation.Title}}<br/>
                                                                {% endfor %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    </p>
                                                </td>
                                                <td>
                                                    <p>
                                                        {% for key, promotionTranslation in promotionTranslations %}
                                                            {% if key == promotion.id %}
                                                                {% for translation in promotionTranslation %}
                                                                    {{ translation.Cta}}<br/>
                                                                {% endfor %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    </p>
                                                </td>
                                                <td>{{ promotion.service.ServiceName }}</td>
                                                <td>{{ promotion.Version.version }}</td>
                                                <td class="text-center"><i class='{{ promotion.online ? "fas fa-check text-success" : "fa fa-ban text-danger"}}'></td>
                                                <td class="text-right">
                                                    <a role="button" class="btn btn-warning mr-1"
                                                       href="{{ path('homepage_promotion_edit', {'profile': profile.id, 'promotion': promotion.id}) }}"
                                                    >
                                                        {% trans %}Modifier{% endtrans %}
                                                    </a>
                                                    <a href="#delete-homepage-promotion-modal" class="btn btn-danger mr-1"
                                                       data-toggle="tooltip"
                                                       data-home-promotion-id="{{ promotion.id }}"
                                                       data-title="">{% trans %}Supprimer{% endtrans %}</a>
                                                </td>
                                              
                                            </tr>

                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}

<!-- DELETE -->
   <div class="modal fade" id="delete-homepage-promotion-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">

            <form action="{{ path('homepage_promotion_delete', {profile: profile.id, promotion: ':id'}) }}" id="delete-homepage-promotion-form" method="POST">
                <input type="hidden" name="token" value="{{ csrf_token('delete-homepage-promotion') }}"/>
                <input type="hidden" name="_method" value="DELETE">

                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="delete">Confirmation</h5>
                            <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">{% trans %}homepage_promotion_delete{% endtrans %}</div>
                        <div class="modal-footer">
                            <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                            <button href="#delete-homepage-promotion-modal" class="btn btn-danger mr-3"  data-toggle="modal" data-target="#delete-homepage-promotion-modal" data-title="" class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
   
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
    <script>
        $(document).ready(function () {
            const table = $('#dataTable').DataTable();
            table.on('draw', function () {
                $(".truncate").hide();
            });
            $(".truncate").hide();
        });
    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-homepage-promotion-modal'),
                $deleteForm = $deletekModal.find('form#delete-homepage-promotion-form'),
                deleteAction = $deleteForm.attr('action');
            $('#dataTable').on('click', 'a[href="#delete-homepage-promotion-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-home-promotion-id')));
                $deletekModal.modal('show');
            });
            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
        $(function () {
            $('[data-toggle="popover"]').popover({html: true});
        });
    </script>

{% endblock %}