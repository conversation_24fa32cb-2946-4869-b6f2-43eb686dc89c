{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/jstree/style.css') }}" rel="stylesheet">
{% endblock %}
{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'edition'|trans|capitalize }}</h6>
        </div>
        <div class="card-body" style="opacity: 0.5">
            <form name="psa_profile_directories" id="psa_profile_directories" method="post" action="{{ path('profils_manage_edit_profile_directories',{'profile': profile.id}) }}" class="mt-2">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label for="current_profile">{{ 'profil'|trans|capitalize }}</label> <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-4">
                        <p>{{ selected_profile.role.label }}</p>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label for="psa_available_sites">{{ 'site'|trans|capitalize }}</label> <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-4">
                        <select id="psa_available_sites" name="selected_site" class="form-control bg-light border-1">
                            {% for site in sites %}
                                <option value="{{ site.id }}" {% if selected_site == site.id%}selected{% endif %}>{{ site.label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label>{{ 'bo_access'|trans|capitalize }}</label>
                    </div>
                    <div class="col-md-7" style="border: solid 1px;padding: 12px;">
                        <div id="jstree_directories"></div>
                    </div>
                </div>
                <input type="hidden" name="directories" id="directories" value="" />
                <input type="hidden" name="selected_directories" id="selected_directories" value="" />
                <input type="hidden" name="selected_profile" id="selected_profile" value="{{ selected_profile.id }}" />
                <input type="hidden" name="directories_order" id="directories_order" value="" />
                <div class="mt-3 float-right">
                    <a class="mr-4 btn btn-dark" role="button" href="{{ path('profils_manage_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                    <button class="btn btn-primary float-right">{{ button_label|default('save')|trans }}</button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/jstree/jstree.min.js') }}"></script>
    <script>
        $(document).ready(function(){
            //function createJSTree(jsondata) {
            //}
            $('#psa_profile_directories').on('submit', function(e) {
                //e.preventDefault();
                var ids = [];
                //var selectedIds = [];
                $('#jstree_directories .jstree-node').each(function () {
                    if ($(this).attr('id') > 0) {
                        ids.push($(this).attr('id'));
                        /*if ($(this).attr('aria-selected')) {
                            selectedIds.push($(this).attr('id'));
                        }*/
                    }
                });
                document.getElementById('directories').value = $('#jstree_directories').jstree("get_checked").join(',');
                document.getElementById('directories_order').value = ids.join(',');

                //console.log(ids);
                //document.getElementById('directories').value = selectedIds.join(",");
                //document.getElementById('unselected_directories').value = ids.join(",");
                //document.getElementById('directories').value = $('#jstree_directories').jstree("get_selected").join(",");
            });

            $('#psa_available_sites').on('change', function() {
                var url = '{{ path('profils_manage_profils_directories',{profile : profile.id,selected_site: ':selected_site',selected_profile: ':selected_profile' }) }}';
                url = url.replace(/&amp;/g, '&');
                url = url.replace(':selected_profile', $('#selected_profile').val());
                url = url.replace(':selected_site', $('#psa_available_sites').val());
                $.ajax({
                    type: "POST",
                    url:url,
                    dataType: 'json',
                    beforeSend: function(){
                        $("#jstree_directories").css('opacity', 0.5)
                    },
                    success: function(data) {
                        //createJSTree(data.nodes);
                        var tree = $('#jstree_directories');
                        tree.jstree('destroy');
                        tree.jstree({
                            "core": {
                                "data" : data.nodes,
                                "check_callback" : function (op, node, par, pos, more) {
                                    if(more && more.dnd) {
                                        return more.pos !== "i" && par.id == node.parent;
                                    }
                                    return true;
                                }
                            },
                            'plugins': ["wholerow","checkbox", "types", "dnd", "sort"],
                            "checkbox" : {
                                "tie_selection": false,
                                //"two_state" : true,
                            },
                            'sort' : function(a, b) {
                                a = this.get_node(a);
                                b = this.get_node(b);
                                return (a.data.sort > b.data.sort) ? 1 : -1;
                            },
                        }).bind("loaded.jstree", function (event, data) {
                            $(this).jstree("open_all");
                            data.instance.sort = () => {};
                        });
                        //tree.jstree(true).refresh();
                        tree.css('opacity', 1);
                        $(".card-body").css('opacity', 1)
                    }
                });
            });
            /*
            $('#jstree_directories').on("move_node.jstree", function (e, data) {
                console.log(data);
            });*/
            $('#psa_available_sites').trigger('change');
        });
    </script>
{% endblock %}