{% for site, siteProfils in profils %}
    <tr>
        <td class="text-primary font-weight-bold" colspan="3">{{ site }} [ {{ siteProfils|length }} ]</td>
    </tr>
    {% for siteProfil in siteProfils %}
        <tr>
            <td>{{ siteProfil.profile_id }}</td>
            <td>{{ siteProfil.role_label }}</td>
            <td class="text-center" style="width: 210px;">
                <a href="{{ path('profils_manage_edit', {'profile': profile.id, 'selected_profile': siteProfil.profile_id, 'selected_site': siteProfil.site_id}) }}"
                   class="btn btn-sm btn-warning">
                    {% trans %}Modifier{% endtrans %}
                </a>
            </td>
        </tr>
    {% endfor %}
{% endfor %}