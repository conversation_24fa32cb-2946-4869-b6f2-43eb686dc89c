{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-3">
        <div class="card-body">
            {% include 'admin/profils/profils.html.twig' %}
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function(){
            var last = 1;
            var url;
            var pagesLength = '{{ pages|length }}' - 2; //Minus Previous and next pages
           
            function showPages(currentId){ 
                for(let i = 3 ; i< pagesLength ;i++){
                    $('#page_'+i).find('a')[0].textContent = i;
                }
                if (currentId > 4 && currentId < pagesLength-3){
                        $('#page_'+(currentId-2)).find('a')[0].textContent ='...';
                        $('#page_'+(currentId+2)).find('a')[0].textContent ='...';
                    }
                if(pagesLength > 7){
                    if (currentId ==1|| currentId==2 || currentId==3){
                        $('#page_'+5).find('a')[0].textContent ='...';
                    }
                    if (currentId ==4){
                        $('#page_'+6).find('a')[0].textContent ='...';
                    }
                    if (currentId ==pagesLength -2 || currentId ==pagesLength -1 || currentId ==pagesLength){
                        $('#page_'+(pagesLength-4)).find('a')[0].textContent ='...';
                    }
                    if(currentId ==pagesLength -3){
                        $('#page_'+(pagesLength-5)).find('a')[0].textContent ='...';
                    }
                }
                if(currentId == 1 || currentId == 2){
                    for (let i=1; i<6 ; i++) {
                        $('#page_'+i).show();
                        $('#page_'+pagesLength).show();
                    }
                    for (let i = 6; i < pagesLength ;i++) {
                        $('#page_'+i).hide();
                    }
                    
                }
                else if (currentId == pagesLength || currentId == pagesLength -1){
                    for (let i=pagesLength -4 ; i<=pagesLength ; i++) {
                        $('#page_'+i).show();
                    }
                    for (let i = 2; i < pagesLength-4 ;i++) {
                        $('#page_'+i).hide();
                    }
                }
                else{
                    for (let i=currentId-2; i<=currentId+2; i++) {
                        $('#page_'+i).show();
                    }
                    for (let i = 2; i<currentId-2 ;i++) {
                        $('#page_'+i).hide();
                    }
                    for (let i = currentId+3; i < pagesLength ;i++) {
                        $('#page_'+i).hide();
                    }
                }          
            }
            showPages(1);
            $('.page-link').on('click', function(e){
                e.preventDefault();
                var currentId = $(this).data('id');              
                showPages(currentId);
                $('#page_previous').on('click',function(e){
                    showPages(currentId);
                })
                $('#page_next').on('click',function(e){
                    showPages(currentId);
                })
                if (last === $(this).data('id')) {
                    return;
                }
                $('.page-item').removeClass('active');
                url = '{{ path('profils_manage_profils_datatables',{profile : profile.id, page: ':page'}) }}';
                if (currentId === "next") {
                    currentId = last + 1;
                } else if (currentId === "previous") {
                    currentId = last - 1;
                }
                if (currentId > 1) {
                    $('#page_previous').removeClass('disabled');
                } else {
                    $('#page_previous').addClass('disabled');
                }

                if (currentId < pagesLength) {
                    $('#page_next').removeClass('disabled');
                } else {
                    $('#page_next').addClass('disabled');
                }
                $('#page_'+currentId).addClass('active');
                url = url.replace(':page', currentId);
                $.ajax({
                    type: "POST",
                    url:url,
                    dataType: 'json',
                    beforeSend: function(){
                        $("#body_data").css('opacity', 0.5)
                    },
                    success: function(data) {
                        $("#body_data").empty().append(data.response).css('opacity', 1);
                        last = currentId;
                        $("#counter").html(data.currentRows)
                    }
                });
            })
        });
    </script>
{% endblock %}