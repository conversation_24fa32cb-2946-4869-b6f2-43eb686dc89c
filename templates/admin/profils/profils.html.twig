{% set numberOfProfiles = 0 %}
<div id="dataTable_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">
 <div class="row">
        <div class="col-sm-12">
            <table class="table table-bordered dataTable no-footer" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info">
                <thead>
                    <th class="text-primary sorting_desc" tabindex="0" aria-controls="dataTable"
                        rowspan="1" colspan="1" aria-sort="descending" width="10%">
                        Profile
                    </th>
                    <th class="text-primary sorting_desc" tabindex="0" aria-controls="dataTable"
                        rowspan="1" colspan="1" aria-sort="descending">
                        Label
                    </th>
                    <th class="text-primary" tabindex="0" aria-controls="dataTable"
                        rowspan="1" colspan="1" aria-sort="descending" width="10%">
                        Actions
                    </th>
                </thead>
                <tbody id="body_data">
                    {% include 'admin/profils/profils_list.html.twig' %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-5">
            <div class="dataTables_info" id="dataTable_info" role="status" aria-live="polite">Showing <span id="counter">{{ currentRows }}</span> of {{ numberOfRows }} Records</div>
        </div>
        <div class="col-sm-12 col-md-7">
            <nav aria-label="Page navigation example">
                <ul class="pagination justify-content-end row">
                    {% for label, page in pages %}
                        <li id="page_{% if page.id is defined %}{{ page.id }}{% else %}{{ label }}{% endif %}"
                            class="page-item {% if page.active == true %}active{% endif %} {% if page.disabled == true %}disabled{% endif %}">
                            <a data-id = "{% if page.id is defined %}{{ page.id }}{% else %}{{ label }}{% endif %}" class="page-link" href="#">{{ label }}</a>
                        </li>
                    {% endfor %}
                </ul>
            </nav>
        </div>
    </div>
</div>
