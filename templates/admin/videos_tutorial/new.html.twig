{% extends '_layout/base_back.html.twig' %}
{% import _self as formMacros %}
{% macro customPrototype(video) %}
    <div class="playlist_element">    
        <div class="row mb-3">
            <div class="col-md-8">
                <hr />
            </div>
        </div>
        <h5 class="font-weight-bold">{{ 'playlist_or_video' | trans }}</h5>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'videos_types' | trans }}
                <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(video.type,  { 'attr': {'class': 'video_type'}}) }}
            </div>
        </div>
        <div class="row mb-4 promote_videos" style="display:none">
            <div class="col-md-3">
                {{ 'Mettre en avant (Possible pour le type VIDEO)' | trans }}
            </div>
            <div class="col-md-5">
                {{ form_widget(video.page) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'title' | trans }}
            </div>
            <div class="col-md-5">
                {{ form_widget(video.title) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'videos_id' | trans }}
                <span class="mandatory">*</span>
            </div>
            <div class="col-md-5">
                {{ form_widget(video.youtubeId) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ 'priority' | trans }}
            </div>
            <div class="col-md-5">
                {{ form_widget(video.priority) }}
            </div>
        </div>
        <div class="col-md-8" style="text-align: right;font-weight: bold;text-transform: uppercase;">
            <button class="delete_playlist btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
        </div>
    </div>
{% endmacro %}
{% block body %}
    {% if update == true %}
        <h3 class="h3 mb-4 text-gray-800">{{ 'videos_update' | trans }}</h3>
    {% else %}
        <h3 class="h3 mb-4 text-gray-800">{{ 'videos_new' | trans }}</h3>
    {% endif %}

    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
        <div class="card shadow-sm">
            <div class="card-body" id="form_container">
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "title" | trans }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.title) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "prdv_activation" | trans }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.enabled) }}
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ 'videos_lcdv' | trans }}
                        {% include "admin/videos_tutorial/errors.html.twig" with {'errors': form.vars.errors.form.lcdvs.getErrors(true)} %}
                    </div>
                    <div class="col-md-5">
                        {{ form_widget(form.lcdvs) }}
                    </div>
                </div> 
                <div class="videos-wrapper"
                    data-prototype="{{ formMacros.customPrototype(form.videos.vars.prototype)|e('html_attr') }}"
                    data-index="{{ form.videos|length }}">
                    {% for video in form.videos %}
                        {{ formMacros.customPrototype(video) }}
                    {% endfor %}
                </div>
                <div class="row">
                    <div class="col-md-8" style="text-align: center;font-weight: bold;text-transform: uppercase;">
                        <button class="btn btn-center btn-primary jslink">{{ 'videos_new' |trans }}</button>
                    </div>
                </div>
            </div>
            
            <div class="card-footer text-right">
                <a class="mr-1 btn btn-dark" role="button" href="{{ path('video_tutorial_config_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
                {% if update == true %}
                    <a href="#delete-video-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-video-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                {% endif %}
                <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
            </div>
        </div>
    {{ form_end(form) }}
    {% if update == true %}
        <div class="modal fade" id="delete-video-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
            <form action="{{ path('video_tutorial_config_delete', {profile: profile.id, video: id}) }}" id="delete-video-form" method="POST">
                <input type="hidden" name="token" value="{{ csrf_token('delete-video') }}"/>
                <input type="hidden" name="_method" value="DELETE">

                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="delete">Confirmation</h5>
                            <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">{% trans %}video_modal_delete{% endtrans %}</div>
                        <div class="modal-footer">
                            <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                            <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            var $wrapper = $('.videos-wrapper');
            $('.jslink').on('click', function(e) {
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $(this).closest('.row').before(newForm);
            });

            $('#form_container').on('click', '.delete_playlist', function(e) {
                e.preventDefault();
                $(this).closest('.playlist_element').remove();
            });
            $('#form_container').on('click', '.video_type input[type="radio"]', function(e) {
                if (this.value == 'VIDEO') {
                    $(this).closest('.playlist_element').find('.promote_videos').show();
                } else {
                    $(this).closest('.playlist_element').find('.promote_videos').hide();
                }
            });
            $('#form_container .video_type input[value="VIDEO"][checked="checked"]').trigger('click');
        });
    </script>
    <script type="text/javascript">
/*
        if (! document.getElementById('video_tutorial_type_1').checked) {
            document.getElementById('promote_videos').style.display = 'none';
        }
        document.getElementById('video_tutorial_type_0').addEventListener('change', function() {
            if (! document.getElementById('video_tutorial_type_1').checked) {
                document.getElementById('promote_videos').style.display = 'none';
            } else {
                document.getElementById('promote_videos').style.display = 'flex';
            }
        });
        document.getElementById('video_tutorial_type_1').addEventListener('change', function() {
            if (! document.getElementById('video_tutorial_type_1').checked) {
                document.getElementById('promote_videos').style.display = 'none';
            } else {
                document.getElementById('promote_videos').style.display = 'flex';
            }
        });*/
    </script>
{% endblock %}
