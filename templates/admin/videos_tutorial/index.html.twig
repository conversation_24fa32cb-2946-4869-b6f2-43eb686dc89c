{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
     <div class="card shadow mb-4">
            <div class="card-body">
                {{ form_start(form, {}) }}
                <div class="mb-3">
                    <h1 class="h3 mb-4 text-gray-800">{{ 'videos_title' | trans }}</h1>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        {{ "activation" | trans }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.enabled) }}
                    </div>
                </div>
                <div class="mt-2 float-right">
                    <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                </div>
                {{ form_end(form) }}
            </div>
        </div>
    <div class="mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('video_tutorial_config_add' , {'profile': profile.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
    </div>
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary"><small class="font-weight-bold">{{ 'title'|trans }}</small></th>
                            <th class="text-primary"><small class="font-weight-bold">{{ 'Status'|trans }}</small></th>
                            <th class="text-primary"><small class="font-weight-bold">{{ 'videos_id'|trans }}</small></th>
                            <th class="text-primary"><small class="font-weight-bold">{{ 'lcdv_model_lcdv'|trans }}</small></th>
                            <th class="text-primary"><small class="font-weight-bold">{% trans %}Date de création{% endtrans %}</small></th>
                            <th class="text-primary text-right" width="20%"><small class="font-weight-bold">Actions</small></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for video in videos %}
                        <tr>
                            <td>
                                    <p>
                                    {{ video.title }}
                                    </p>
                            </td>
                           <td class="text-center">
                                {% if video.enabled %}
                                    <span class="badge badge-success">{% trans %}Activé{% endtrans %}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{% trans %}Désactivé{% endtrans %}</span>
                                {% endif %}
                            </td>                            
                            <td>
                                    <p>
                                        {% for videoData in video.videos %}
                                            <span>{{ videoData.youtube_id }}<br /></span>
                                        {% endfor %}
                                    </p>
                            </td>
                            <td>
                                <p>
                                    {% for key, lcdvLabel in video.lcdvs %}
                                        {% if key == 10 %}
                                            <span>........</span>
                                        {% endif %}
                                        <span class="{% if key > 9 %}truncate{% endif %}">
                                            {{ lcdvLabel.lcdv }}<br />
                                        </span>
                                    {% endfor %}
                                </p>
                            </td>

                            <td>{{ video.created_at ? video.created_at|date('Y-m-d H:i') : '-' }}</td>
                            <td class="text-right"><small>
                                <a role="button" class="btn btn-sm btn-warning mr-1"
                                   href="{{ path('video_tutorial_config_edit', {'profile': profile.id, 'video': video.id}) }}"
                                >
                                    <small>{% trans %}Modifier{% endtrans %}</small>
                                </a>
                                <a href="#delete-video-modal" class="btn  btn-sm btn-danger mr-1"
                                   data-video-id="{{ video.id }}"
                                   data-toggle="tooltip" data-title=""><small>{% trans %}Supprimer{% endtrans %}</small></a>
                            </small></td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-video-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('video_tutorial_config_delete', {profile: profile.id, video: ':id'}) }}" id="delete-video-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-video') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}video_modal_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            const table = $('#dataTable').DataTable(
                {
                    "order": [[ 3, "desc" ]],
                    'autoWidth': false,
                    /*
                    'columnDefs': [
                        {'orderable': false, targets: [0, 8]},
                        { width: "10%", targets: 0 },
                        { width: "25%", targets: 1 },
                        { width: "7%", targets: 2 },
                        { width: "10%", targets: 3 },
                        { width: "5%", targets: 4 },
                        { width: "0.5%", targets: 5 },
                        { width: "15%", targets: 6 },
                        { width: "15%", targets: 7 },
                        { width: "12.5%", targets: 8 }
                    ]*/
                }
            );
            table.on('draw', function () {
                $(".truncate").hide();
            });
            $(".truncate").hide();
        });

    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-video-modal'),
                $deleteForm  = $deletekModal.find('form#delete-video-form'),
                deleteAction = $deleteForm.attr('action');

            $('#dataTable').on('click', 'a[href="#delete-video-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-video-id')));
                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
