{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %} brand_univers_content {% endtrans %}</h1>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary"></h6>
        </div>
        <div class="card-body">
        <div class="mt-1 mb-4 text-right">
                        <a role="button" class="btn btn-primary" href="{{ path('brand_univers_new', {'profile': profile.id}) }}">{% trans %}new_brand_univer{% endtrans %}</a>
                </div>
                <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th> {% trans %} brand_univer_title {% endtrans %}</th>
                        <th> {% trans %} brand_univer_category {% endtrans %}</th>
                        <th> {% trans %} brand_univer_published {% endtrans %}</th>
                        <th> {% trans %} brand_univer_start_show {% endtrans %}</th>
                        <th> {% trans %} brand_univer_end_show {% endtrans %}</th>
                        <th> {% trans %} brand_univer_published_date {% endtrans %}</th>
                        <th> {% trans %} brand_univer_maj_date {% endtrans %}</th>
                        <th> {% trans %} brand_univer_status {% endtrans %}</th>
                        <th class="text-center">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for univer in univers %}
                        <tr>
                            <td>{{ univer.title }}</td>
                            <td>{{ univer.category }}</td>
                            <td>{% if(univer.published == 0) %} Non {% else %} Oui {% endif %}</td>
                            <td>{{ univer.start_show ? (univer.start_show | date('d/m/Y')) : '' }}</td>
                            <td>{{ univer.end_show ? (univer.end_show | date('d/m/Y')) : '' }}</td>
                            <td>{{ univer.published_date ? (univer.published_date | date('d/m/Y')) : '' }}</td>
                            <td>{{ univer.maj_date ? (univer.maj_date | date('d/m/Y')) : '' }}</td>
                            <td>{{ univer.status }}</td>                            
                            <td class="text-center">
                                <a href="{{ path('brand_univers_edit', {profile: profile.id, universID: univer.id}) }}"
                                   class="btn btn-sm btn-warning">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-univers-modal" class="btn btn-sm btn-danger"
                                   data-univers-id="{{ univer.id }}">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center">
                                {% trans %}univers_list_empty{% endtrans %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                </div>
               
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-univers-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('brand_univers_delete', {profile: profile.id, universID: ':id'}) }}" id="delete-univers-form" method="POST">
            {# <input type="hidden" name="token" value="{{ csrf_token('delete-media') }}"/> #}
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}confirmation_delete_univers{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
            $(document).ready(function() {$('#dataTable').DataTable();});
            var $deleteUniversModal = $('div#delete-univers-modal'),
                $deleteUniversForm  = $deleteUniversModal.find('form#delete-univers-form'),
                deleteUniversAction = $deleteUniversForm.attr('action');

            $('a[href="#delete-univers-modal"]').on('click', function (event) {
                event.preventDefault();
                 $deleteUniversForm.attr('action', deleteUniversAction.replace(':id', $(this).attr('data-univers-id')));
                $deleteUniversModal.modal('show');
            });

            $deleteUniversModal.on('hidden.bs.modal', function () {
                $deleteUniversForm.attr('action', deleteUniversAction);
            });
    </script>
{% endblock %}
