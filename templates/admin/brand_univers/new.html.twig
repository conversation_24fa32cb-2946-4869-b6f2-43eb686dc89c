{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800">{% trans %}brand_univers_new_title{% endtrans %}</h3>  

        <div class="card shadow mb-3">
            <div class="card-header"></div>
            <div class="card-body">
                <div class="form-group mt-3">
                    <div class="row">
                        <div class="col-md-1">
                            <label>Titre BO </label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-1">
                            <label>Titre FO <span class="mandatory">*</span></label>
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control" />
                        </div>
                    </div>
                </div>
                <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
                {# ////////////////////////////// #}
                {{ include('admin/brand_univers/ciblage_form.html.twig') }}
                {# ////////////////////////////// #}
                <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-2">
                            <label>Feuille de style <span class="mandatory">*</span></label>
                        </div>
                        <div class="col-md-3">
                            <select id="" name="" class="form-control">
                                    <option value="0">-> Choisissez</option>
                                    <option value="1">Clair</option>
                                    <option value="2">Sombre</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-2">
                            <label>Catégorie <span class="mandatory">*</span></label>
                        </div>
                        <div class="col-md-3">
                            <select id="" name="" class="form-control">
                                    <option value="0">-> Choisissez</option>
                                    {% for categorie in categories %}
                                    <option value="{{ categorie.id }}">{{ categorie.title }}</option>
                                    {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                {# ////////////////////////////// #}
                <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-1">
                            <label>Introduction</label>
                        </div>
                        <div class="col-md-8">
                            <textarea rows="5" class="form-control" ></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-1">
                            <label>Visuel</label>
                        </div>
                        <div class="col-md-3">
                            <a role="button" class="btn btn-primary" href="#">{% trans %}Ajouter{% endtrans %}</a>
                        </div>
                    </div>
                </div>
                {# ////////////////////////////// #}
                <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
                {{ include('admin/brand_univers/template.html.twig') }}
            </div>
        </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/multiselect.js') }}"></script>
    <script>
        $(document).ready(function() {
            // $('#psa_site_languages_selected_languages').multiselect();
            // $('#psa_site_general_currencies').multiselect();

            // $("form[name = 'psa_site_general']").submit(function(e) {
            //     e.preventDefault();

            //     $(' #psa_site_languages_selected_languages option').prop("selected", true);
            //     $('#psa_site_general_currencies option').prop("selected", true);

            //     $(this).off('submit').submit();
            // })
        });
    </script>
{% endblock %}
