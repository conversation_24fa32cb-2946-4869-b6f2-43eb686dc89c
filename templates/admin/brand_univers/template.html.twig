<div class="form-group">
    <div class="row">
        <div class="col-md-1">
            <label>Template <span class="mandatory">*</span></label>
        </div>
        <div class="col-md-3">
            <select id="template_select" name="" class="form-control" onchange="val()">
                    <option value="0">-> Choi<PERSON>sez</option>
                    <option value="1">Détail</option>
                    <option value="2">Lien interne</option>
                    <option value="3">Lien externe</option>
            </select>
        </div>
    </div>
</div>
<div class="form-group">
    <div class="row">
        <div class="col-md-1">
            <label>Libellé <span class="mandatory">*</span></label>
        </div>
        <div class="col-md-5">
            <input type="text" class="form-control"/>
        </div>
    </div>
</div>

<div id="template_interne" style="display: none;" class="dynamic_content">
   <hr style="border-top: 1px dotted #8c8b8b; margin: 25px 0px; width: 30%;">
    <div class="form-group">
        <div class="row">
            <div class="col-md-1">
                <label>URL interne</label>
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control"/>
            </div>
        </div>
    </div>
</div>


<div id="template_externe" style="display: none;" class="dynamic_content">
    <hr style="border-top: 1px dotted #8c8b8b; margin: 25px 0px; width: 30%;">
    <div class="form-group">
        <div class="row">
            <div class="col-md-1">
                <label>URL externe</label>
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control"/>
            </div>
        </div>
    </div>
</div>


<div id="template_detail" style="display: none;" class="dynamic_content">
    <hr style="border-top: 1px dotted #8c8b8b; margin: 25px 0px; width: 30%;">
    <div class="form-group">
        <div class="row">
            <div class="col-md-1">
                <label>Libellé</label>
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control"/>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="row">
            <div class="col-md-1">
                <label>URL</label>
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control"/>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="row">
            <div class="col-md-1">
                <label>Ouverture</label>
            </div>
            <div class="col-md-3">
                <select id="" name="" class="form-control">
                        <option value="0">-> Choisissez</option>
                        <option value="1">interne</option>
                        <option value="2">externe</option>
                </select>
            </div>
        </div>
    </div>
     <div class="row">
         <div class="col-2">
         <button class="btn btn-info" onclick="addpraph()"> Paragraphe</button>
        </div>
    </div>
    <div id="block_pragraphe" occurence= "1" class="mt-3">
         
    </div>
</div>

{% block javascripts %}
<script>

function val() {
    value = document.getElementById("template_select").value;
    $(".dynamic_content").hide();
    if(value == 1){
        $("#template_detail").show();
    }else if( value == 2){
        $("#template_interne").show();
    }else if( value == 3){
        $("#template_externe").show();
    }
}
function addpraph(){
    var target = document.getElementById("block_pragraphe");
    var occurence = target.getAttribute("occurence");
    var background ;
    if(occurence% 2 != 0) background = "#FAEADA";
    else  background = "#F9FDF3";

    target.innerHTML += `<div style="background-color:${background}; padding:25px 10px;">
                            <div class="form-group mt-2">
                                <div class="row">
                                    <div class="col-md-2">
                                        <label>Text N° ${occurence}</label>
                                    </div>
                                    <div class="col-md-7">
                                        <textarea class="form-control" rows="5"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mt-2">
                                <div class="row">
                                    <div class="col-md-2">
                                        <label>Visuel</label>
                                    </div>
                                    <div class="col-md-5">
                                        <a role="button" class="btn btn-primary" href="#">{% trans %}Ajouter{% endtrans %}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mt-2">
                                <div class="row">
                                    <div class="col-md-2">
                                        <label>Légende visuel</label>
                                    </div>
                                    <div class="col-md-5">
                                        <input class="form-control" type="text" />
                                    </div>
                                </div>
                            </div>
                         </div>`;
target.setAttribute("occurence", ++occurence);
}
</script>
{% endblock %}