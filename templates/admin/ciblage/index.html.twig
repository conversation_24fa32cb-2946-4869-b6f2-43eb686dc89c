{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">Ciblages</h1>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary"></h6>
        </div>
        <div class="card-body">
                <div class="mt-1 mb-4 text-right">
                        <a role="button" class="btn btn-primary" href="{{ path('content_ciblage_new', {'profile': profile.id}) }}">{% trans %}new_ciblage{% endtrans %}</a>
                </div>
                <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}Nom{% endtrans %}</th>
                        <th class="text-center" style="width : 30%;">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for ciblage in ciblages %}
                        <tr>
                            <td>{{ ciblage.title }}</td>
                            <td class="text-center">
                                <a href="{{ path('content_ciblage_edit', {'profile': profile.id, 'id' : ciblage.id}) }}"
                                   class="btn btn-sm btn-warning">{% trans %}Modifier{% endtrans %}</a> 
                                <a href="#delete-ciblage-modal" class="btn btn-sm btn-danger"
                                   data-ciblage-id="{{ ciblage.id }}">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="2" class="text-center">
                                {% trans %}ciblages_list_empty{% endtrans %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-ciblage-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('content_ciblage_delete', {profile: profile.id, id : ':id'}) }}" id="delete-ciblage-form" method="POST">
             <input type="hidden" name="_token" value="{{ csrf_token('delete_ciblage') }}">
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}confirmation_delete_ciblage{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
            $(document).ready(function() {$('#dataTable').DataTable();});
            var $deleteCiblageModal = $('div#delete-ciblage-modal'),
                $deleteCiblageForm  = $deleteCiblageModal.find('form#delete-ciblage-form'),
                deleteCiblageAction = $deleteCiblageForm.attr('action');

            $('a[href="#delete-ciblage-modal"]').on('click', function (event) {
                event.preventDefault();
                 $deleteCiblageForm.attr('action', deleteCiblageAction.replace(':id', $(this).attr('data-ciblage-id')));
                $deleteCiblageModal.modal('show');
            });

            $deleteCiblageModal.on('hidden.bs.modal', function () {
                $deleteCiblageForm.attr('action', deleteCiblageAction);
            });
    </script>
{% endblock %}
