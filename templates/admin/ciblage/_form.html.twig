{{ form_start(form) }}
    <div class="card shadow-sm">
        <div class="card-body">            
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                    	{{ form_errors(form.title) }}
                        {{ form_label(form.title) }}
                    </div>
                    <div class="col-md-4">
                        {{ form_widget(form.title) }}
                    </div>
        
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.lcdvs) }}
                    </div>
                    <div class="col-md-4">
                    	{{ form_errors(form.lcdvs) }}
                        {{ form_widget(form.lcdvs) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.vins) }}
                    </div>
                    <div class="col-md-4">
                    	{{ form_errors(form.vins) }}
                        {{ form_widget(form.vins) }}
                    </div>

                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
            <a class="mr-3 btn btn-dark" role="button" href="{{ path('content_ciblage_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
        </div>
    </div>
    {{ form_end(form) }}