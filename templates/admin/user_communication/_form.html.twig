    <div style="background-color:#fff;">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                <div>
                    <div class="row mb-3">
                        <div class="col-md-3">Activation</div>
                        <div class="col-md-4 ">
                            {{ form_widget(form.enabled) }}
                        </div>
                    </div>

                     <div class="row mb-3">
                        <div class="col-md-3">Category</div>
                        <div class="col-md-4 ">
                            {{ form_widget(form.category) }}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3">Version</div>
                        <div class="col-md-4 ">
                            {{ form_widget(form.version) }}
                        </div>
                    </div>

                     <div class="row mb-3">
                        <div class="col-md-3">Date Version</div>
                        <div class="col-md-4 ">
                            {{ form_widget(form.dateVersion) }}
                        </div>
                    </div>
                </div>
                {% set languagesLength = profile.site.languages.count %}
                {% for language in profile.site.languages %}
                    {% set formL = form['form-' ~ language.code ] %}
                    {% if languagesLength == 1 %}
                        {{ include('admin/user_communication/user_communication_language.html.twig', { 'formL': formL }) }}
                    {% else %}
                        <div class="card">
                            <div class="card-header" id="{{'heading-user-communication-' ~ language.code }}" style="background-color: lemonchiffon;" >
                                <h6 class="mb-0">
                                    <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                    href="#section-user-communication-{{ language.code }}" role="button" aria-expanded="false"
                                    aria-controls="section-user-communication-{{ language.code }}">
                                        <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                        {{ language.label }}
                                    </a>
                                </h6>
                            </div>
                            <div id="{{ 'section-user-communication-' ~ language.code }}" class="collapse" aria-labelledby="{{'heading-user-communication-' ~ language.code }}">
                                <div class="card-body" id="form_container" data-language="{{language.code}}" >
                                    {{ include('admin/user_communication/user_communication_language.html.twig', { 'formL': formL }) }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                <div class="text-right mt-3">
                    <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                </div>
            {{ form_end(form) }}
        </div>
    </div>