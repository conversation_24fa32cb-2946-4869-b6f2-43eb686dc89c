
<div class="row">
    <div class="col-md-3">{{ form_label(formL.title) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.title) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.short_description) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.short_description) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.description) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.description) }}
    </div>
</div>
<div class="row mt-5">
    {% set imageNotExist = (formL.vars.data.image is not defined) or (not formL.vars.data.image) %}
    {% set imageModalId =  'user-communication-' ~ language.code ~ '-image' %}
    <div class="col-3">{{ form_label(formL.image) }}</div>
    <div class="col-9 image-container">
        <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
            {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.image.path }}" {% endif %} style="text-decoration: none">
            <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                src="{{ mediaUrl ~ '/' ~ formL.vars.data.image.path }}" alt="{{ formL.vars.data.image.textAlt }}" {% endif %}>
        </a>
        <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
        <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
        {{ include('admin/user_communication/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': formL.image}) }}
    </div>
</div>