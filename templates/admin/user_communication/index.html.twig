{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img {
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}

{% block body %}
{% set categories = {
  'last_version_info': 'Last Version Info',
  'bugs_info' : 'Bugs Info',
  'did_you_know' : 'Did you know ?'
  }
%}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <div class="card shadow mb-4">
        <div class="overflow-auto" style="overflow-y:scroll;height: 600px">
            <div class="card-body">
                <div class="tab-content">
                       <h1 class="h3 mb-4 text-gray-800">{{ 'user_communications' | trans | upper }}</h1>
                        <div class="mb-4 text-right">
                            <a role="button" class="btn btn-primary"
                               href="{{ path('user_communication_add' , {'profile': profile.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
                        </div>
                        <div class="card shadow mb-4">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered dataTable" id="dataTable" width="100%"
                                           cellspacing="0" role="grid" aria-describedby="dataTable_info"
                                           style="width: 100%;">
                                        <thead>
                                        <tr>
                                            <th class="text-primary">{{ 'Category'|trans }}</th>
                                            <th class="text-primary">{{ 'title'|trans }}</th>
                                            <th class="text-primary">{% trans %}Date de création{% endtrans %}</th>
                                            <th class="text-primary">{{ 'Active'|trans }}</th>
            
                                            <th class="text-primary text-right" width="20%">Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for user_communication in user_communications %}
                                            <tr>
                                                <td>{{ categories[user_communication.category] }}</td>
                                                {% if user_communication.userCommunicationLanguages|length > 0 %}
                                                    <td>{{ user_communication.userCommunicationLanguages[0].title }}</td>
                                                {% else %}
                                                    <td>null</td>
                                                {% endif %}
                                                <td>{{ user_communication.createdAt ? user_communication.createdAt|date('Y-m-d H:i') : '-' }}</td>
                                                <td>{{ user_communication.enabled ? 'YES' : 'No' }}</td>
                                                
                                                <td class="text-right">
                                                    <a role="button" class="btn btn-warning mr-1"
                                                       href="{{ path('user_communication_edit', {'profile': profile.id, 'userCommunication': user_communication.id}) }}"
                                                    >
                                                        {% trans %}Modifier{% endtrans %}
                                                    </a>
                                                    <a href="#delete-user-communication-modal" class="btn btn-danger mr-1"
                                                       data-user-communication-id="{{ user_communication.id }}"
                                                       data-toggle="tooltip"
                                                       data-title="">{% trans %}Supprimer{% endtrans %}</a>
                                                </td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="4">no records found</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-user-communication-modal" tabindex="-1" role="dialog" aria-labelledby="delete"
         aria-hidden="true">
        <form action="{{ path('user_communication_delete', {profile: profile.id, userCommunication: ':id'}) }}"
              id="delete-user-communication-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-user-communication') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}user_communication_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button"
                                data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
    <script>
        $(document).ready(function () {
            const table = $('#dataTable').DataTable();
            table.on('draw', function () {
                $(".truncate").hide();
            });
            $(".truncate").hide();
        });
    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-user-communication-modal'),
                $deleteForm = $deletekModal.find('form#delete-user-communication-form'),
                deleteAction = $deleteForm.attr('action');
            $('#dataTable').on('click', 'a[href="#delete-user-communication-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-user-communication-id')));
                $deletekModal.modal('show');
            });
            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
        $(function () {
            $('[data-toggle="popover"]').popover({html: true});
        });
    </script>

{% endblock %}