{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <link href="{{ asset('css/bootstrap-datepicker.css') }}" rel="stylesheet" type="text/css">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800">{{ 'user_communication_new' | trans }}</h3>
    {{ include('admin/user_communication/_form.html.twig', {'button_label': 'save', 'update': false}) }}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/bootstrap-datepicker.min.js') }}"></script>
    
     <script>
     $(function () {
            $(".image-picker").imagepicker({hide_select: true});
      })

      $(".delete-image").on('click', function() {
                $(this).addClass('d-none');
                parent = $(this).closest('.image-container');
                parent.children(".image-link-holder").removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
                parent.find(".image-picker-selector").val(null).change();
            });
            $(document).on('click', '.preview-image-change', function(event) {
                parent = $(this).closest('.image-container');
                linkSrc = parent.find(".image-picker-selector option:selected").data("img-real-src");
                imageSrc = parent.find(".image-picker-selector option:selected").data("img-src");
                if (!linkSrc) {
                    linkSrc = imageSrc;
                }
                imageAlt = parent.find(".image-picker-selector option:selected").data("img-alt");
                parent.children(".image-link-holder").attr("href", linkSrc).removeClass('d-none')
                    .children(".image-holder").attr("src", imageSrc).attr("alt", imageAlt);
                parent.find(".delete-image").removeClass('d-none');
            });
             // INITIALIZE DATEPICKER PLUGIN
            $('#USER_COMMUNICATION_FORM_dateVersion').datepicker({
                clearBtn: true,
                format: "yyyy-m-d"
            });
    </script>
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
{% endblock %}

