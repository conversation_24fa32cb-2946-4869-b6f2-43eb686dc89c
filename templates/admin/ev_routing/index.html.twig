{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img {
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <div class="card shadow mb-4">
        <div class="overflow-auto" style="overflow-y:scroll;height: 600px">
            <div class="card-body">
                <div class="tab-content">
                       <h1 class="h3 mb-4 text-gray-800">{{ 'ev_routings' | trans | upper }}</h1>
                        <div class="mb-4 text-right">
                            <a role="button" class="btn btn-primary"
                               href="{{ path('ev_routing_add' , {'profile': profile.id, 'brand': brand}) }}">{% trans %}Ajouter{% endtrans %}</a>
                        </div>
                        <div class="card shadow mb-4">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered dataTable" id="dataTable" width="100%"
                                           cellspacing="0" role="grid" aria-describedby="dataTable_info"
                                           style="width: 100%;">
                                        <thead>
                                        <tr>
                                            <th class="text-primary">{{ 'label'|trans }}</th>
                                            <th class="text-primary">{{ 'lcdv_model_lcdv'|trans }}</th>
                                            {% if(brand=='OP' or brand=='VX') %}
                                                <th class="text-primary">{{ 'rpo_model_rpo'|trans }}</th>
                                            {% endif %}
                                            <th class="text-primary">
                                                DCW
                                            </th>
                                            <th class="text-primary">
                                                DVQ
                                            </th>
                                            <th class="text-primary">
                                                B0F
                                            </th>
                                            <th class="text-primary">
                                                DAR
                                            </th>
                                            <th class="text-primary">{% trans %}Date de création{% endtrans %}</th>
                                            <th class="text-primary">{% trans %}Date de mise à jour{% endtrans %}</th>
                                            <th class="text-primary text-right" width="20%">Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for ev_routing in ev_routings %}
                                            <tr>
                                                <td>{{ ev_routing.label }}</td>
                                                <td>
                                                    <p>
                                                        {% for key, lcdvLabel in ev_routing.lcdvs %}
                                                            {% if key == 10 %}
                                                                <span>........</span>
                                                            {% endif %}
                                                            <span class="{% if key > 9 %}truncate{% endif %}">
                                                                    {{ lcdvLabel.lcdv }}<br/>
                                                                </span>
                                                        {% endfor %}
                                                    </p>
                                                </td>
                                                {% if(brand=='OP'or brand=='VX') %}
                                                    <td>
                                                        <p>
                                                            {% for key, rpoLabel in ev_routing.rpos %}
                                                                {% if key == 10 %}
                                                                    <span>........</span>
                                                                {% endif %}
                                                                <span class="{% if key > 9 %}truncate{% endif %}">
                                                                    {{ rpoLabel.rpo }}<br/>
                                                                </span>
                                                            {% endfor %}
                                                        </p>
                                                    </td>
                                                {% endif %}
                                                <td> 
                                                    {{ ev_routing.dcw }}
                                                </td>
                                                <td> 
                                                    {{ ev_routing.dvq }}
                                                </td>
                                                <td> 
                                                    {{ ev_routing.b0f }}
                                                </td>
                                                <td> 
                                                    {{ ev_routing.dar }}
                                                </td>
                                                <td>{{ ev_routing.createdAt ? ev_routing.createdAt|date('Y-m-d H:i') : '-' }}</td>
                                                <td>{{ ev_routing.updatedAt ? ev_routing.updatedAt|date('Y-m-d H:i') : '-' }}</td>
                                                <td class="text-right">
                                                    <a role="button" class="btn btn-warning mr-1"
                                                       href="{{ path('ev_routing_edit', {'profile': profile.id, 'evRouting': ev_routing.id, 'brand': brand}) }}"
                                                    >
                                                        {% trans %}Modifier{% endtrans %}
                                                    </a>
                                                    <a href="#delete-ev-routing-modal" class="btn btn-danger mr-1"
                                                       data-ev-routing-id="{{ ev_routing.id }}"
                                                       data-toggle="tooltip"
                                                       data-title="">{% trans %}Supprimer{% endtrans %}</a>
                                                </td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="4">no records found</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-ev-routing-modal" tabindex="-1" role="dialog" aria-labelledby="delete"
         aria-hidden="true">
        <form action="{{ path('ev_routing_delete', {profile: profile.id, evRouting: ':id', brand: brand}) }}"
              id="delete-ev-routing-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-ev-routing') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}ev_routing_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button"
                                data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
    <script>
        $(document).ready(function () {
            const table = $('#dataTable').DataTable();
            table.on('draw', function () {
                $(".truncate").hide();
            });
            $(".truncate").hide();
        });
    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-ev-routing-modal'),
                $deleteForm = $deletekModal.find('form#delete-ev-routing-form'),
                deleteAction = $deleteForm.attr('action');
            $('#dataTable').on('click', 'a[href="#delete-ev-routing-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-ev-routing-id')));
                $deletekModal.modal('show');
            });
            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
        $(function () {
            $('[data-toggle="popover"]').popover({html: true});
        });
    </script>

{% endblock %}
