{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}
{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <h3 class="h3 mb-4 text-gray-800">{{ 'ev_routing_update' | trans }}</h3>
    {{ include('admin/ev_routing/_form.html.twig', {'button_label': 'save', 'brand': brand, 'update': true}) }}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
{% endblock %}
