{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Détails du Log{% endtrans %} </h1>


    <div class="card shadow-sm mb-4">
        <div class="card-body"> 
             <div class="mt-2">
                <div class="row mb-4">
                    <div class="col-md-3 font-weight-bold">
                        <label>Site</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.profile.site.label }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>User</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.user }}</label> 
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Role</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.profile.role.label }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Entité</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.entityName }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Id de l'entité</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.entityId }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Date de creation</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.createdAt ? log.createdAt|date('d-m-Y H:i:s') : '-' }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Action</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.action }}</label>
                    </div>

                    <div class="col-md-3 font-weight-bold">
                        <label>Indentifiant route</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ log.route }}</label>
                    </div>

                    
                    <div class="col-md-3 font-weight-bold">
                        <label>Parameters</label>
                    </div>
                    <div class="col-md-9">
                        <pre id="json" style="background: whitesmoke;">{{ log.parameters }}</pre>
                    </div>
                </div>

             </div>

        </div>

        <div class="card-footer text-right">
            <a class="mr-3 btn btn-success" role="button" onclick="history.back(-1)" style="color:#fff">{% trans %}Retour{% endtrans %}</a>
        </div>

    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        window.$(function () {
            var element = document.getElementById("json");
            var obj = JSON.parse(element.innerText);
            element.innerHTML = JSON.stringify(obj, undefined, 2);
        });
    </script>
{% endblock %}