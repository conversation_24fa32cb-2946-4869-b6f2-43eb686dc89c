{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/bootstrap-datepicker.css') }}" rel="stylesheet" type="text/css">
{% endblock %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">History Logs</h1>


    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Site{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}User{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Entité{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Action{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Creation Date{% endtrans %}</small></th>
                        <th class="text-primary" width="30%"><small class="font-weight-bold">{% trans %}route{% endtrans %}</small></th>
                        <th class="text-primary text-center"><small class="font-weight-bold">{% trans %}Actions{% endtrans %}</small></th>
                    </tr>
                    </thead>
                    <tbody class="modals_triggers">
                    {% for log in logs %}
                        <tr>
                            <td><small>{% if log.profile %}{{ log.profile.site.label }}{% else %} {% endif %}</small></td>
                            <td><small>{{ log.user }}</small></td>
                            <td><small>{{ log.entityName }}</small></td>
                            <td><small>{{ log.action }}</small></td>
                            <td><small>{{ log.createdAt | date('d-m-Y H:i:s') }}</small></td>
                            <td><small>{{ log.route }}</small></td>
                            <td class="text-center">
                                <a href="{{ path('history_show',{'profile': profile.id,'id':log.id})}}" class="btn btn-sm btn-warning">
                                    <small>{% trans %}Afficher{% endtrans %}</small>
                                </a>
                            </td>    
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="9">{% trans %}Aucun enregistrement trouvé{% endtrans %}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap-datepicker.min.js') }}"></script>
    <script>
            $(document).ready(function() {
            let table = $('#dataTable').DataTable({
                    "processing": true,
                    "serverSide": true, 
                    "ajax": {
                        "url": "{{ path('history_index_paginate', {'profile': profile.id}) }}",
                        "data": function ( d ) {
                            d.status = $('#local_translation_filterer_status').val();
                            d.functionalityName = $('#local_translation_filterer_functionalityName').val();
                        }
                    },
                    "sAjaxDataProp": "data", 
                    "pageLength": 25, 
                    "paging" : true,
                    "info" : true,
                    "searching": true,
                    "responsive": true, 
                    stateSave: true,
                    columns: [
                        { data: 'site'},
                        { data: 'user' },
                        { data: 'entite' },
                        { data: 'action' },
                        { data: 'creationDate' },
                        { data: 'route' },
                        { data: function ( row, type ) {
                            if ( type === 'display' ) {
                                myRender = '';
                                myRender += '<a href="{{ path('history_index_show', {'profile': profile.id, 'id': 'row.id'}) }}" class="btn btn-sm btn-warning"><small>{% trans %}Afficher{% endtrans %}</small></a>';
                                myRender = myRender.replace("row.id", row.id);
                                return myRender ;
                            }

                            return '' ;

                        }},
                    ],
                    'order': [[ 1, "desc" ]],
                    'autoWidth': false,
                    'columnDefs': [
                        { width: "5%", targets: 0 },
                        { width: "7%", targets: 1 },
                        { width: "7%", targets: 2 },
                        { width: "10%", targets: 3 },
                        { width: "30%", targets: 4 },
                        { width: "25%", targets: 5 },
                        { width: "5.5%", targets: 6 },
                    ]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text);
                    }
                });   
            })
            $(".truncate").each(function(){
                if($(this).text().trim().length > 100){
                    let text = $(this).text().trim().substring(0 , 100) + '...';
                    $(this).html(text);
                }
            });   
        });
    </script>
{% endblock %}
