{% extends '_layout/base_back.html.twig' %}

{% block body %}
{{ include('admin/sunset/modal/media.html.twig') }}
	<div class="card shadow mb-4">
		<div class="card-header pt-4">
			<ul class="nav nav-tabs card-header-tabs">
				<li class="nav-item">
					<a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#app">
						<h6 class="m-0 font-weight-bold text-primary">APP</h6>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#web">
						<h6 class="m-0 font-weight-bold text-primary">WEB</h6>
					</a>
				</li>
			</ul>
		</div>
		<div class="card-body">
			<div class="tab-content">
				<div class="tab-pane {% if source == "APP" %}active{% endif %}" id="app">
					{{ form_start(formMultiApp, {'attr': { 'name':'myCustomFormName' }}) }}
					<div class="overflow-auto card-body">
						<div class="mb-3">
							<h5 class="text-gray-900 font-weight-bold">{{ "sunset.label" | trans }}</h5>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ "Activation" | trans }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiApp.sunset_enabled) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ "sunset.level" | trans }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiApp.sunset_level) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ form_label(formMultiApp.sunset_redirect_uri_android) }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiApp.sunset_redirect_uri_android) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ form_label(formMultiApp.sunset_redirect_uri_ios) }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiApp.sunset_redirect_uri_ios) }}
							</div>
						</div>
						{% for language in languages %}
							{% set formApp = formMultiApp['sunset_form-' ~ language.code ] %}
							<div id="sunset_app">
								<div class="card mb-2">
									<div class="card-header" id="{{ 'heading-' ~ language.code }}">
										<h6 class="mb-0">
											<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{ language.code }}">
												<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
												{{ language.label }}
											</a>
										</h6>
									</div>
									<div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#sunset_app">
										<div class="card-body">
											<div class="row mb-4">
												<div class="col-md-3">
													{{ form_label(formApp['title-' ~ language.code]) }}
												</div>
												<div class="col-md-7">
													{{ form_widget(formApp['title-' ~ language.code]) }}
													
												</div>
											</div>
											<div class="row mb-4">
												<div class="col-md-3">
													{{ form_label(formApp['body-' ~ language.code]) }}
												</div>
												<div class="col-md-7">
													{{ form_widget(formApp['body-' ~ language.code]) }}
												</div>
											</div>

											<div class="row">
												{% set imageNotExist = (formApp.vars.data['media-' ~ language.code] is not defined) or (not formApp.vars.data['media-' ~ language.code]) %}
												{% set imageModalId = 'sunset' ~ '-' ~ language.code %}
												<div class="col-3">{{ form_label(formApp['media-' ~ language.code]) }}</div>
												<div class="col-9 image-container">
													<a target="_blank" class="image-link-holder-{{formApp['media-' ~ language.code].vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}" {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formApp.vars.data['media-' ~ language.code].path }}" {% endif %} style="text-decoration: none">
														<img class="image-holder-{{formApp['media-' ~ language.code].vars.id}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %} src="{{ mediaUrl ~ '/' ~ formApp.vars.data['media-' ~ language.code].path }}" alt="{{ formApp.vars.data['media-' ~ language.code].textAlt }}" {% endif %}>
													</a>
													<a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick="addModal('{{formApp['media-' ~ language.code].vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
													<button class="btn btn-sm btn-danger delete-image-{{formApp['media-' ~ language.code].vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick="deleteMedia('{{formApp['media-' ~ language.code].vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						{% endfor %}
					</div>
					<div class="text-right mt-3">
						<button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
					</div>
					<div class="d-none">
						{{ form_rest(formMultiApp) }}
					</div>
					{{ form_end(formMultiApp) }}

				</div>
				<div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="web">
					{{ form_start(formMultiWeb, {'method': 'POST'}) }}
					<div class="overflow-auto card-body">
						<div class="mb-3">
							<h5 class="text-gray-900 font-weight-bold">{{ "sunset.label" | trans }}</h5>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ "Activation" | trans }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiWeb.sunset_enabled) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ "sunset.level" | trans }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiWeb.sunset_level) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ form_label(formMultiWeb.sunset_redirect_uri_android) }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiWeb.sunset_redirect_uri_android) }}
							</div>
						</div>
						<div class="row mb-2">
							<div class="col-md-3">
								{{ form_label(formMultiWeb.sunset_redirect_uri_ios) }}
							</div>
							<div class="col-md-7">
								{{ form_widget(formMultiWeb.sunset_redirect_uri_ios) }}
							</div>
						</div>
						{% for language in languages %}
							{% set formWeb = formMultiWeb['sunset_form-' ~ language.code ] %}
							<div id="sunset_web">
								<div class="card mb-2">
									<div class="card-header" id="{{ 'heading-web-' ~ language.code }}">
										<h6 class="mb-0">
											<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-web-{{ language.code }}" role="button" aria-expanded="true" aria-controls="section-web-{{ language.code }}">
												<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
												{{ language.label }}
											</a>
										</h6>
									</div>
									<div id="{{ 'section-web-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-web-' ~ language.code }}" data-parent="#sunset_web">
										<div class="card-body">
											<div class="row mb-4">
												<div class="col-md-3">
													{{ form_label(formWeb['title-' ~ language.code]) }}
												</div>
												<div class="col-md-7">
													{{ form_widget(formWeb['title-' ~ language.code]) }}
												</div>
											</div>
											<div class="row mb-4">
												<div class="col-md-3">
													{{ form_label(formWeb['body-' ~ language.code]) }}
												</div>
												<div class="col-md-7">
													{{ form_widget(formWeb['body-' ~ language.code]) }}
												</div>
											</div>
											<div class="row">
												{% set imageNotExist = (formWeb.vars.data['media-' ~ language.code] is not defined) or (not formWeb.vars.data['media-' ~ language.code]) %}
												{% set imageModalId = 'sunset-web' ~ '-' ~ language.code %}
												<div class="col-3">{{ form_label(formWeb['media-' ~ language.code]) }}</div>
												<div class="col-9 image-container">
													<a target="_blank" class="image-link-holder-{{formWeb['media-' ~ language.code].vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}" {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formWeb.vars.data['media-' ~ language.code].path }}" {% endif %} style="text-decoration: none">
														<img class="image-holder-{{formWeb['media-' ~ language.code].vars.id}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %} src="{{ mediaUrl ~ '/' ~ formWeb.vars.data['media-' ~ language.code].path }}" alt="{{ formWeb.vars.data['media-' ~ language.code].textAlt }}" {% endif %}>
													</a>
													<a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick="addModal('{{formWeb['media-' ~ language.code].vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
													<button class="btn btn-sm btn-danger delete-image-{{formWeb['media-' ~ language.code].vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick="deleteMedia('{{formWeb['media-' ~ language.code].vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						{% endfor %}
					</div>
					<div class="text-right mt-3">
						<button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
					</div>
					<div class="d-none">
						{{ form_rest(formMultiWeb) }}
					</div>
					{{ form_end(formMultiWeb) }}
				</div>
			</div>
			
		</div>
	</div>
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	{{ include('admin/medias/js/js.html.twig', { 'hideBtn': 'true' }) }}
<script>
		
	function addModal(formId) {
		$('#form-img-holder').val(formId);
	}

	function validateImage() {
		if ($('.cadre-image.selected').attr('id')) {
			idMedia = $('.cadre-image.selected').attr('id');
			id = idMedia.split('-')[1];
			formId = $('#form-img-holder').val();
			$('#' + formId).val(id);
			imageSrc = $('.cadre-image.selected img').attr('src');
			imageAlt = $('.cadre-image.selected img').attr('alt');
			imageHref = $('.cadre-image.selected img').data('path');
			$(".image-holder-" + formId).attr("src", imageSrc).attr("alt", imageAlt);
			$(".image-link-holder-" + formId).attr("href", imageHref).removeClass('d-none');
			$(".delete-image-" + formId).removeClass('d-none');
		}
	}

	function deleteMedia(formId) {
		$(".delete-image-" + formId).addClass('d-none');
		parent = $(this).closest('.image-container');
		$(".image-link-holder-" + formId).removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
		$('#' + formId).val(null).change();
	}

	$(".invalid-feedback").closest(".collapse").addClass("show");
</script>
{% endblock %}
