{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Détail incident{% endtrans %} </h1>


    <div class="card shadow-sm mb-4">
        <div class="card-body">
             <div class="mt-2">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label>ID</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{logIncident.incidentid}}</label>
                    </div>

                    <div class="col-md-3">
                        <label>Email</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{logIncident.email}}</label>
                    </div>

                    <div class="col-md-3">
                        <label>VIN</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{logIncident.vin}}</label>
                    </div>

                    <div class="col-md-3">
                        <label>Titre de l'incident</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{logIncident.title}}</label>
                    </div>

                    <div class="col-md-3">
                        <label>Commentaire de l'incident</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{logIncident.comment}}</label>
                    </div>

                    <div class="col-md-3">
                        <label>Date de creation de l'incident</label>
                    </div>
                    <div class="col-md-7">
                        <label>{{ logIncident.creation_date ? logIncident.creation_date|date('d-m-Y') : '-' }}</label>
                    </div>

                    <div class="col-md-3">
                        <label>Pièce(s) joint</label>
                    </div>
                    <div class="col-md-7">
                        {% for file in logIncident.files  %}
                            <label><a href="{{ file }}" target="_blank">{{ file }}</a></label><br>
                        {% else %}
                            <label>Aucune pièce joint</label>
                        {% endfor %}
                    </div>
                </div>

             </div>

        </div>

        <div class="card-footer text-right">
            <a class="mr-3 btn btn-danger" role="button" href="#delete-log-modal"  data-log-id="{{ logIncident.incidentid }}"><small>{% trans %}Supprimer{% endtrans %}</small></a>
            <a class="mr-3 btn btn-success" role="button" onclick="history.back(-1)" style="color:#fff"><small>{% trans %}Retour{% endtrans %}</small></a>
        </div>

    </div>
{% endblock %}
{% block modals %}
    <div class="modal fade" id="delete-log-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('log_incident_delete', {profile: profile.id, id: ':id'}) }}" id="delete-log-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-log-token') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cet incident{% endtrans %} ?</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <!-- DELETE FO LABEL -->
    <script>
        window.$(function () {
            var $deleteLogModal = window.$('div#delete-log-modal'),
                $deleteLogForm  = $deleteLogModal.find('form#delete-log-form'),
                deleteLogAction = $deleteLogForm.attr('action');

            window.$('a[href="#delete-log-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteLogForm.attr('action', deleteLogAction.replace(':id', window.$(this).attr('data-log-id')));

                $deleteLogModal.modal('show');
            });

            $deleteLogModal.on('hidden.bs.modal', function () {
                $deleteLogForm.attr('action', deleteLogAction);
            });
        });
    </script>
{% endblock %}

