{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/bootstrap-datepicker.css') }}" rel="stylesheet" type="text/css">
{% endblock %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Liste des{% endtrans %} LogIncidents</h1>


    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <!-- Filters -->
            {{ form_start(form, {'attr': {'class': 'mb-2'}}) }}
                <div class="row">
                    <div class="col-md-3 col-sm-12 pt-2">
                        {{ form_label(form.vin) }}
                    </div>
                    <div class="col-md-3 col-sm-12 pt-2">
                        {{ form_label(form.email) }}
                    </div>
                    <div class="col-md-3 col-sm-12 pt-2">
                        {{ form_label(form.date) }}
                    </div>
                    <div class="col-md-3 col-sm-12 pt-2">
                        {{ form_label(form.end_date) }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_widget(form.vin) }}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_widget(form.email) }}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_widget(form.date) }}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_widget(form.end_date) }}
                        </div>
                    </div>
                    <div class="col-md-1 text-right">
                        <button class="btn btn-info" type="submit">{{ button_label|default('filter') |trans }}</button>
                    </div>
                </div>
            {{ form_end(form) }}

            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Ticket ID{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Site Code{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}VIN{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Titre{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Date{% endtrans %}</small></th>
                        <th class="text-primary text-right"><small class="font-weight-bold">{% trans %}Actions{% endtrans %}</small></th>
                    </tr>
                    </thead>
                    <tbody class="modals_triggers">
                   
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap-datepicker.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            let table = $('#dataTable').DataTable({

                "processing": true, 
                "serverSide": true, 
                "ajax": {
                    "url": "{{ path('log_incident_app_paginate', {'profile': profile.id}) }}",
                    "data": function ( d ) {
                        d.vin = $('#log_incident_filterer_vin').val();
                        d.email = $('#log_incident_filterer_email').val();
                        d.creation_date = $('#log_incident_filterer_date').val();
                        d.end_date = $('#log_incident_filterer_end_date').val();
                    }
                },
                
                "sAjaxDataProp": "data", 
                "pageLength": 25, 
                "paging" : true,
                "info" : true,
                "searching": true,
                "responsive": true,

                stateSave: true,
                columns: [
                        { data: 'incidentid'},
                        { data: 'site_code' },
                        { data: 'vin' },
                        { data: 'title' },
                        { data: 'creation_date' },
                        { data: function ( row, type ) {
                            myRender = '';
                            
                            myRender += '<a href="{{ path('log_incident_app_show', {'profile': profile.id, 'id': 'row.incidentid'}) }}" class="btn btn-sm btn-warning"><small>{% trans %}Afficher{% endtrans %}</small></a>';
                            myRender = myRender.replace("row.incidentid", row.incidentid);
                            return myRender ;
                        }},
                ],
                "rowCallback": function( row, data ) {
                        $('td:eq(0)', row).html(row.incidentid);
                        $('td:eq(0)', row).attr('style','background-color:'+data.bgcTranslationStatus);
                        $('td:eq(0)', row).attr('title',data.translationStatusText);
                       // $('td:eq(0)', row).addClass('p-0');
                    },
                'order': [[0, "desc" ]],
                'autoWidth': true,
                'columnDefs': [
                        {'orderable': false, targets: [5]},
                    ]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text); 
                    }
                });   
            })
            // INITIALIZE DATEPICKER PLUGIN
            $('#log_incident_filterer_date').datepicker({
                clearBtn: true,
                format: "dd-mm-yyyy"
            });
            $('#log_incident_filterer_end_date').datepicker({
                clearBtn: true,
                format: "dd-mm-yyyy"
            });
        });
    </script>
{% endblock %}
