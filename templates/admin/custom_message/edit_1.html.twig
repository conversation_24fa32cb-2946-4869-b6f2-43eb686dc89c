{% import _self as form<PERSON><PERSON>ros %}
{% macro customPrototype(label) %}
    <div class="mb-4">
        {{ form_widget(label.id) }}
        <div class="row mb-4">
            <div class="col-12">
                {{ form_widget(label.label_key) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "display_weight" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.weight) }}
            </div>
            <div class="col-md-3">
                {{ "priority" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.priority) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.left_time_postion) }}
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.left_days) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.right_time_postion) }}
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.right_days) }}
            </div>
        </div>
    </div>
{% endmacro %}

    <div class="row">
        <!-- Export -->
        <div class="col-6">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ message.messageKey | trans }}</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                    {{ form_start(form, {'action': path('custom_message_edit',{'profile': profile.id, 'id': message.id}), 'method': 'POST'}) }}
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(form.enabled) }}
                            </div>
                        </div>
                        <div class="labels-wrapper"
                            data-prototype="{{ formMacros.customPrototype(form.label_keys.vars.prototype)|e('html_attr') }}"
                            data-index="{{ form.label_keys|length }}">
                            {% for label in form.label_keys %}
                                {{ formMacros.customPrototype(label) }}
                            {% endfor %}
                        </div>
                    <a href="#" class="jslink">
                        Add
                    </a>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            var $wrapper = $('.labels-wrapper');
            $('.jslink').on('click', function(e) {
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $(this).before(newForm);
            });
        });
    </script>