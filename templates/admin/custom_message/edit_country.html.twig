{% extends '_layout/base_back.html.twig' %}
{% import _self as displayer %}
{% macro display(label) %}
    <div class="mb-4">
        <div class="row mb-4">
            <div class="col-12">
                <input type="text" class="form-control" value="{{ label.labelKey }}" disabled>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {% set position = '' %}
                {% if label.leftTimePosition == -1 %}
                    {% set position = 'before' %}
                {% elseif label.leftTimePosition == 1 %}
                    {% set position = 'after' %}
                {% endif %}
                <input type="text" class="form-control" value="{{ position | trans }}" disabled>
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" value="{{ label.leftDays }}" disabled>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {% set position = '' %}
                {% if label.rightTimePosition == -1 %}
                    {% set position = 'before' %}
                {% elseif label.rightTimePosition == 1 %}
                    {% set position = 'after' %}
                {% endif %}
                <input type="text" class="form-control" value="{{ position | trans }}" disabled>
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" value="{{ label.rightDays }}" disabled>
            </div>
        </div>
    </div>
{% endmacro %}
{% block body %}

    <div class="row">
        <!-- Export -->
        <div class="col-6">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ message.messageKey | trans }}</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                    {{ form_start(form, {'action': path('custom_message_edit',{'profile': profile.id, 'id': message.id}), 'method': 'POST'}) }}
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(form.enabled, {'attr' : {'class' : 'override'}}) }}
                            </div>
                        </div>
                       <div class="row mb-5">
                            <div class="col-md-2">
                                {{ "priority" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(form.priority) }}
                            </div>
                        </div>
                        <div>
                            {% for label in message.messageLabels %}
                                {{ displayer.display(label) }}
                            {% endfor %}
                        </div>
                        <div class="mt-3 float-right">
                            <a class="mr-1 btn btn-dark" role="button" href="{{ path('custom_message_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
                            <a class="mr-4 btn btn-dark" role="button" href="{{ path('custom_message_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                            {% if not message.readOnly %}
                                <button class="btn btn-primary float-right override">{{ button_label|default('save')|trans }}</button>
                            {% endif %}
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            var readOnly = "{{ message.readOnly }}";
            if (readOnly) {
                $("form :input").attr("disabled", true);
            }
        });
    </script>
{% endblock javascripts %}