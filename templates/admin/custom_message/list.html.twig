{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ "custom_messages_title" | trans }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid"
                    aria-describedby="dataTable_info" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary" width="30%">{% trans %}Date de création{% endtrans %}</th>
                            <th class="text-primary">Service</th>
                            <th class="text-primary">{{ 'priority' | trans }}</th>
                            <th class="text-primary" width="10%">Status</th>
                            <th class="text-primary" width="10%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customMessage in messages %}
                        <tr>
                            <td>{{ customMessage.createdAt | date('Y-m-d H:i:s') }}</td>
                            <td>{{ customMessage.messageKey | trans }}</td>
                            <td>{{ customMessage.priority }}</td>
                            <td class="text-center">
                                {% if ((customMessage.enabled) or customMessage.readOnly) %}
                                    <span class="badge badge-success">{% trans %}Activé{% endtrans %}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{% trans %}Désactivé{% endtrans %}</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a role="button" class="btn btn-warning mr-1" 
                                    href="{{ path('custom_message_edit', {'profile': profile.id, 'id': customMessage.id}) }}">
                                    {% trans %}Modifier{% endtrans %}
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function () {
            $('#dataTable').DataTable();
        });

    </script>
{% endblock %}