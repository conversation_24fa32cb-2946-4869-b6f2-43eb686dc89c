{% extends '_layout/base_back.html.twig' %}
{% import _self as formMacros %}
{% macro customPrototype(label) %}
    <div class="mb-4">
        {{ form_widget(label.id) }}
        <div class="row mb-4">
            <div class="col-12">
                {{ form_widget(label.label_key) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.left_time_position) }}
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.left_days) }}
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                {{ "position_in_time" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.right_time_position) }}
            </div>
            <div class="col-md-3">
                {{ "days_number" | trans }}
            </div>
            <div class="col-md-3">
                {{ form_widget(label.right_days) }}
            </div>
        </div>
    </div>
{% endmacro %}
{% block body %}

    <div class="row">
        <!-- Export -->
        <div class="col-6">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ message.messageKey | trans }}</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                    {{ form_start(form, {'action': path('custom_message_edit',{'profile': profile.id, 'id': message.id}), 'method': 'POST'}) }}
                        <div class="row mb-4">
                            <div class="col-md-2">
                                {{ "prdv_activation" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(form.enabled, {'attr' : {'class' : 'override'}}) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-2">
                                {{ "priority" | trans }}
                            </div>
                            <div class="col-md-3">
                                {{ form_widget(form.priority) }}
                            </div>
                        </div>
                        <div class="labels-wrapper"
                            data-prototype="{{ formMacros.customPrototype(form.messageLabels.vars.prototype)|e('html_attr') }}"
                            data-index="{{ form.messageLabels|length }}">
                            {% for label in form.messageLabels %}
                                {{ formMacros.customPrototype(label) }}
                            {% endfor %}
                        </div>
                        {% if profile.profileAdmin %}
                            <div style="text-align: center;font-weight: bold;text-transform: uppercase;">
                                <a href="#" class="jslink">
                                    {{ "add_new_message" | trans }}
                                </a>
                            </div>
                        {% endif %}
                        <div class="mt-3 float-right">
                            <a class="mr-1 btn btn-dark" role="button" href="{{ path('custom_message_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
                            <a class="mr-4 btn btn-dark" role="button" href="{{ path('custom_message_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                            <button class="btn btn-primary float-right override">{{ button_label|default('save')|trans }}</button>
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            var $wrapper = $('.labels-wrapper');
            var readOnly = "{{ message.readOnly }}";
            var isAdmin = "{{ profile.profileAdmin }}";
            if (readOnly) {
                $("form :input[type='checkbox']").attr("disabled", true);
            }
            
            if (! isAdmin) {
                $("form :input:not(.override)").attr("disabled", true);
            }
            $('.jslink').on('click', function(e) {
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $(this).parent().before(newForm);
            });
        });
    </script>
{% endblock javascripts %}