{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}
{% block body %}

<div class="card shadow mb-4">
    <div class="card-header">{% trans %}cbuddy_image_title{% endtrans %}</div>
    <div class="card-body">
        <div class="row">
                <div class="col-2">
                    {% trans %}cbuddy_icon_label{% endtrans %}
                </div>
                <div class="col-9">
                    {% if mediaService %}
                        <a href="{{ mediaUrl ~ '/' ~ mediaService.media.path }}" target="_blank">
                            <img style="max-height: 60px; border: 1px solid;" src="{{ mediaUrl ~ '/' ~ mediaService.media.path }}" alt="{{ mediaService.media.textAlt }}">
                        </a>
                    {% endif %}
                    <a href="#select-media-modal" class="btn btn-success ml-4 mr-3"  data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                    {% if mediaService %}
                        <a href="#delete-media-modal" data-toggle="modal" class="btn btn-danger">{% trans %}Supprimer{% endtrans %}</a>
                    {% endif %}
                </div>
            </div>
    </div>
</div>

    <div class="mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('eligibility_cbuddy_new' , {'profile': profile.id,'type':type}) }}">{% trans %}Ajouter{% endtrans %}</a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table id="dataTable" class="table table-bordered table-hover dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}Libellé{% endtrans %}</th>
                        <th>LCDV</th>
                        <th>Marque</th>
                        <th class="text-right">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for eligibility in eligibility_vehicles %}
                        <tr>
                            <td>{{ eligibility.label }}</td>
                            <td>{{ eligibility.lcdv }}</td>
                            <td>{{ eligibility.brand }}</td>
                            <td class="text-right">
                                <a href="{{ path('eligibility_cbuddy_edit', {'type':type,'profile': profile.id, 'id': eligibility.id}) }}" class="btn btn-sm btn-warning"
                                   data-toggle="tooltip" data-title="">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-eligibility-modal" class="btn btn-sm btn-danger"
                                   data-eligibility-id="{{ eligibility.id }}"
                                   data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% trans %}eligibility_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
<!-- DELETE -->
<div class="modal fade" id="delete-eligibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
    <form action="{{ path('eligibility_cbuddy_delete', {'type':type,profile: profile.id, id: ':id'}) }}" id="delete-eligibility-form" method="POST">
        <input type="hidden" name="token" value="{{ csrf_token('delete-eligibility') }}"/>
        <input type="hidden" name="_method" value="DELETE">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="delete">Confirmation</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette véhicule{% endtrans %} ?</div>
                <div class="modal-footer">
                    <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                </div>
            </div>
        </div>
    </form>
</div>
 {{ include('admin/vehicle_eligibility_cbuddy/modals/add.html.twig') }}
 {{ include('admin/vehicle_eligibility_cbuddy/modals/delete.html.twig') }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(function () {
            $(".image-picker").imagepicker()
        });
    </script>
    <script>
        $(document).ready(function() {
            let table = $('#dataTable').DataTable({
                    stateSave: true,
                    paging: false,
                    'order': [[ 1, "desc" ]]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text);
                    }
                });
            })
        });

    </script>

    <!-- DELETE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            setTimeout(function(){
                $(".alert").hide();
            }, 5000);

            var $deletekModal = window.$('div#delete-eligibility-modal'),
                $deleteForm  = $deletekModal.find('form#delete-eligibility-form'),
                deleteAction = $deleteForm.attr('action');

            window.$('a[href="#delete-eligibility-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteForm.attr('action', deleteAction.replace(':id', window.$(this).attr('data-eligibility-id')));

                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
