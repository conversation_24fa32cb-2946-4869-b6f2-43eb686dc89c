 <!-- CHOOSE MEDIA -->
    <div class="modal fade" id="select-media-modal" tabindex="-1" role="dialog" aria-labelledby="select" aria-hidden="true">
        <form action="{{ path('eligibility_cbuddy_add_image', {'profile': profile.id}) }}" method="POST">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">{{ 'regis_doc_image_select_header'|trans }}</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <select class="image-picker show-html" name="cbuddy_image" >
                            <option value=""></option>
                            {% for media in medias %}
                                {% if  media.extension != 'pdf' %}
                                    {% if mediaService is null or (mediaService and mediaService.media != media)  %}
                                        <option data-img-src="{{ mediaUrl ~ '/' ~ media.path }}" data-img-alt="{{ media.textAlt }}" value="{{ media.id }}"></option>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-primary" type="submit">{% trans %}Valider{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
