{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab"
                       href="#prdvparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab"
                       href="#prdvparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="prdvparameters-app">
                    {{ form_start(formMultiApp, {'action': path('prdv_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}

                    <div class="overflow-auto card-body">
                        {% for language in languages %}
                            {% set formApp = formMultiApp['form-' ~ language.code ] %}
                            <div id="prdv_app">
                                <div class="card">
                                    <div class="card-header" id="{{ 'heading-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark"
                                               data-toggle="collapse"
                                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}"
                                                     alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-' ~ language.code }}"
                                         class="collapse {% if loop.first %}show{% endif %}"
                                         aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#prdv_app">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <h5 class="text-gray-900 font-weight-bold">{{ "prdv_app_label" | trans }}</h5>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "prdv_activation" | trans }}
                                                </div>
                                                <div class="col-md-7">
                                                    {{ form_widget(formApp.prdv_natif_enabled) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "Type" | trans }}
                                                </div>
                                                <div class="col-md-3 prdv_natif" id="prdv_natif"
                                                     data-brand="{{ profile.site.brand }}"
                                                     data-language="{{ language.code }}">
                                                    {{ form_widget(formApp.is_prdv_natif) }}
                                                </div>
                                            </div>

                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "pickup_delivery_enabled" | trans }}
                                                </div>
                                                <div class="col-md-3">
                                                    {{ form_widget(formApp.pickup_delivery_enabled) }}
                                                </div>
                                            </div>

                                            <div id="prdv-config-{{ language.code }}">
                                                <div class="mb-3">
                                                    <h5 class="text-gray-900 font-weight-bold">{{ "prdv_label" | trans }}</h5>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ "prdv_activation" | trans }}
                                                    </div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp.enabled) }}
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ "prdv_perimeter" | trans }}
                                                    </div>
                                                    <div class="col-md-3">
                                                        {{ form_widget(formApp.solution) }}
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ form_label(formApp.url) }}
                                                    </div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp.url) }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="mb-3">
                                                    <h5 class="text-gray-900 font-weight-bold">{{ "quote_label" | trans }}</h5>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ "prdv_activation" | trans }}
                                                    </div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp.quote_enabled) }}
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ "prdv_perimeter" | trans }}
                                                    </div>
                                                    <div class="col-md-3">
                                                        {{ form_widget(formApp.quote_solution) }}
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">
                                                        {{ form_label(formApp.quote_url) }}
                                                    </div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp.quote_url) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %} </div>
                    {{ form_end(formMultiApp) }}

                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="prdvparameters-web">
                    {{ form_start(formMultiWeb, {'action': path('prdv_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                    <div class="overflow-auto card-body">
                        {% for language in languages %}
                            {% set formWeb = formMultiWeb['form-' ~ language.code ] %}
                            <div id="prdv_web">
                                <div class="card">
                                    <div class="card-header" id="{{ 'heading-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark"
                                               data-toggle="collapse"
                                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}"
                                                     alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-' ~ language.code }}"
                                         class="collapse {% if loop.first %}show{% endif %}"
                                         aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#prdv_web">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <h5 class="text-gray-900 font-weight-bold">{{ "prdv_label" | trans }}</h5>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "prdv_activation" | trans }}
                                                </div>
                                                <div class="col-md-7">
                                                    {{ form_widget(formWeb.enabled) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "pickup_delivery_enabled" | trans }}
                                                </div>
                                                <div class="col-md-3">
                                                    {{ form_widget(formWeb.pickup_delivery_enabled) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "prdv_perimeter" | trans }}
                                                </div>
                                                <div class="col-md-3">
                                                    {{ form_widget(formWeb.solution) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ form_label(formWeb.url) }}
                                                </div>
                                                <div class="col-md-7">
                                                    {{ form_widget(formWeb.url) }}
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <h5 class="text-gray-900 font-weight-bold">{{ "quote_label" | trans }}</h5>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "prdv_activation" | trans }}
                                                </div>
                                                <div class="col-md-7">
                                                    {{ form_widget(formWeb.quote_enabled) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ "prdv_perimeter" | trans }}
                                                </div>
                                                <div class="col-md-3">
                                                    {{ form_widget(formWeb.quote_solution) }}
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">
                                                    {{ form_label(formWeb.quote_url) }}
                                                </div>
                                                <div class="col-md-7">
                                                    {{ form_widget(formWeb.quote_url) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {{ form_end(formMultiWeb) }}
                </div>
            </div>
            <div class="mt-2 float-right">
                <button id="form_submitor"
                        class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function () {
            $("#form_submitor").on('click', function () {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id + " form").submit();
            })
            $(".prdv_natif").on('change', function (e) {
                language = $(this).data('language');
                if (document.getElementById('prdv_parameters_APP_form-' + language + '_is_prdv_natif_0').checked || document.getElementById('prdv_parameters_APP_form-' + language + '_is_prdv_natif_2').checked) {
                    $('#prdv-config-' + language).hide();
                } else {
                    $('#prdv-config-' + language).show();
                }
            });
            $(".prdv_natif").on('change', function (a) {
                language = $(this).data('language');
                brand = $(this).data('brand');
                if (brand != 'DS') {
                    $(document.getElementById('prdv_parameters_APP_form-' + language + '_is_prdv_natif_2')).attr('disabled', true);
                }
            });
            $(".prdv_natif").trigger('change');
        });
    </script>
{% endblock %}
