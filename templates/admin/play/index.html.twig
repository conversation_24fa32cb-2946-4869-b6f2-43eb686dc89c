{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <div class="card">
        <div class="card-header" id="header-{{ section }}">
            <h6 class="mb-0 font-weight-bold text-primary">
                <a class="float-left text-left text-decoration-none w-100" role="button" aria-expanded="true"
                    aria-controls="section-{{ section }}">
                    PLAY
                </a>
            </h6>
        </div>
        <div id="section-{{ section }}" aria-labelledby="header-{{ section }}">
            <div class="overflow-auto card-body" style="overflow-y:scroll">
                {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                    <div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'play_activation' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.enabled) }}
                            </div>
                        </div>
                        {% for language in profile.site.languages %}
                            {% set formL = form['form-' ~ language.code ] %}
                            {{ include('admin/play/l_parameters.html.twig', { 'formL': formL, 'language': language }) }}
                        {% endfor %}
                    </div>
                    <div class="text-right mt-3">
                        <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                    </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}