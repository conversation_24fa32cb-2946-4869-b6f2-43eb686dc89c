{% extends '_layout/base_back.html.twig' %}

{% block body %}
	<div class="card shadow">
		<div class="card-header">
			<ul class="nav nav-tabs card-header-tabs">
				<li class="nav-item">
					<a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
						<h6 class="m-0 font-weight-bold text-primary">APP</h6>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
						<h6 class="m-0 font-weight-bold text-primary">WEB</h6>
					</a>
				</li>
			</ul>
		</div>
		<div class="overflow-auto">
			<div class="card-body">
				<div class="tab-content">
					<div class="tab-pane {% if source == 'APP'%} active {% endif %}" id="wsparameters-app">
						<div id="cbuddy-app">
							{{ form_start(formApp, { 'attr': {'class': 'mt-1'}}) }}
							<div>
								<div class="row mb-5">
									<div class="col-md-3">{{ 'sams_url.activation' | trans }}</div>
									<div class="col-md-4 ">
										{{ form_widget(formApp.enabled) }}
									</div>
								</div>


								{% set languagesLength = profile.site.languages.count %}
								{% for language in profile.site.languages %}
									{% set formAppL = formApp['form-' ~ language.code ] %}
									{% if languagesLength == 1 %}
										{{ include('admin/sams_url/l_parameters.html.twig', { 'formL': formAppL, 'language': language }) }}
									{% else %}
										<div class="card">
											<div class="card-header" id="{{'heading-' ~ language.code }}" style="background-color: lemonchiffon;">
												<h6 class="mb-0">
													<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{  language.code }}">
														<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
														{{ language.label }}
													</a>
												</h6>
											</div>
											<div id="{{ 'section-' ~ language.code }}" class="collapse {% if not formAppL.vars.valid %} show {% endif %}" aria-labelledby="{{'heading-' ~ language.code }}">
												<div class="card-body" id="form_container" data-language="{{language.code}}">
													{{ include('admin/sams_url/l_parameters.html.twig', { 'formL': formAppL, 'language': language }) }}
												</div>
											</div>
										</div>
									{% endif %}
								{% endfor %}
							</div>
							<div class="text-right mt-3">
								<button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
							</div>
							{{ form_end(formApp) }}
						</div>
					</div>
					<div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
						<div id="cbuddy-web">
							{{ form_start(formWeb, { 'attr': {'class': 'mt-1'}}) }}
							<div>
								<div class="row mb-5">
									<div class="col-md-3">{{ 'sams_url.activation' | trans }}</div>
									<div class="col-md-4 ">
										{{ form_widget(formWeb.enabled) }}
									</div>
								</div>


								{% set languagesLength = profile.site.languages.count %}
								{% for language in profile.site.languages %}
									{% set formWebL = formWeb['form-' ~ language.code ] %}
									{% if languagesLength == 1 %}
										{{ include('admin/sams_url/l_parameters.html.twig', { 'formL': formWebL, 'language': language }) }}
									{% else %}
										<div class="card">
											<div class="card-header" id="{{'heading-' ~ language.code }}" style="background-color: lemonchiffon;">
												<h6 class="mb-0">
													<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{  language.code }}">
														<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
														{{ language.label }}
													</a>
												</h6>
											</div>
											<div id="{{ 'section-' ~ language.code }}" class="collapse {% if not formWebL.vars.valid %} show {% endif %}" aria-labelledby="{{'heading-' ~ language.code }}">
												<div class="card-body" id="form_container" data-language="{{language.code}}">
													{{ include('admin/sams_url/l_parameters.html.twig', { 'formL': formWebL, 'language': language }) }}
												</div>
											</div>
										</div>
									{% endif %}
								{% endfor %}


							</div>
							<div class="text-right mt-3">
								<button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
							</div>
							{{ form_end(formWeb) }}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}
{% block javascripts %}
	{{ parent() }}
	<script>
		$(document).ready(function () {
$(".form_submitor").on('click', function () {
var id = $('.card-header-tabs .nav-item .active').attr("href");
$(id + " form").submit();
})

});
	</script>
{% endblock %}
