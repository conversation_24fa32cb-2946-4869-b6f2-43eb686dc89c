{% extends '_layout/base_back.html.twig' %}


{% block body %}

    <div class="mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('availibility_settings_new' , {'profile': profile.id, 'brand': brand}) }}">{% trans %}Ajouter{% endtrans %}</a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">

            <div class="table-responsive">
                <table id="dataTable" class="table table-bordered table-hover dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}availibility_type{% endtrans %}</th>
                        <th>{% trans %}availibility_start_date{% endtrans %}</th>
                        <th>{% trans %}availibility_end_date{% endtrans %}</th>
                        <th>{% trans %}availibility_start_date_label{% endtrans %}</th>
                        <th>{% trans %}availibility_end_date_label{% endtrans %}</th>
                        <th>{% trans %}availibility_os{% endtrans %}</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for availibility in data %}
                        <tr>
                            <td nowrap>{{ availibility.type | trans }}</td>
                            <td nowrap>{{ availibility.startDate | date('Y-m-d H:i', boTimeZone) }}</td>
                            <td nowrap>{{ availibility.endDate | date('Y-m-d H:i', boTimeZone) }}</td>
                            <td nowrap>{{ availibility.startDateLabel | date('Y-m-d H:i', boTimeZone) }}</td>
                            <td nowrap>{{ availibility.endDateLabel | date('Y-m-d H:i', boTimeZone) }}</td>
                            <td nowrap>{{ availibility.os | trans }}</td>
                            <td nowrap class="text-right">
                                <a href="{{ path('availibility_settings_edit', {'profile': profile.id, 'availibilitySettings': availibility.id, 'brand': brand}) }}" class="btn btn-sm btn-warning"
                                   data-toggle="tooltip" data-title="">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-availibility-modal" class="btn btn-sm btn-danger"
                                   data-availibility-id="{{ availibility.id }}"
                                   data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="8" class="text-center">
                                {% trans %}availability_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            <br/>
            <p>Time Zone : <b> {{ boTimeZone }} (GMT {{ timeZoneHour }})</b> </p>
        </div>
    </div>
{% endblock %}

{% block modals %}
<!-- DELETE -->
<div class="modal fade" id="delete-availibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
    <form action="{{ path('availibility_settings_delete', {profile: profile.id, id: ':id', 'brand': brand}) }}" id="delete-availibility-form" method="POST">
        <input type="hidden" name="token" value="{{ csrf_token('delete-availability') }}"/>
        <input type="hidden" name="_method" value="DELETE">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="delete">Confirmation</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette availability settings{% endtrans %} ?</div>
                <div class="modal-footer">
                    <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            let table = $('#dataTable').DataTable({
                    stateSave: true,
                    paging: false,
                    'order': [[ 1, "desc" ]]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text);
                    }
                });
            })
        });

    </script>

    <!-- DELETE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            var $deletekModal = window.$('div#delete-availibility-modal'),
                $deleteForm  = $deletekModal.find('form#delete-availibility-form'),
                deleteAction = $deleteForm.attr('action');

            window.$('a[href="#delete-availibility-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteForm.attr('action', deleteAction.replace(':id', window.$(this).attr('data-availibility-id')));

                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
