{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
<div class="card shadow-sm">
    <div class="card-body">
        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                    {% trans %}availibility_type{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-4">
                    {{ form_widget(form.type) }}
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                    {% trans %}availibility_start_date{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.startDate) }}
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                    {% trans %}availibility_end_date{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.endDate) }}
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                     {% trans %}availibility_start_date_label{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.startDateLabel) }}
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                    {% trans %}availibility_end_date_label{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.endDateLabel) }}
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-md-3">
                    {% trans %}availibility_os{% endtrans %} <span class="mandatory">*</span>
                </div>
                <div class="col-md-4">
                    {{ form_widget(form.os) }}
                </div>
            </div>
        </div>

    <br/>
    <p>Time Zone : <b> {{ boTimeZone }} (GMT {{ timeZoneHour }})</b> </p>
    </div>
    <div class="card-footer text-right">
        <a class="mr-3 btn btn-dark" role="button" href="{{ path('availibility_settings',{'profile': profile.id, 'brand': brand}) }}">{% trans %}Annuler{% endtrans %}</a>
        <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
    </div>
</div>
{{ form_end(form) }}