{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('css/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}performed_maintenances.title{% endtrans %} </h1>

    <div class="card shadow-sm mb-4">
        <div class="card-header">
        </div>
        <div class="card-body">
            <!-- Filters -->
            {{ form_start(form, {'attr': {'class': 'mb-2'}}) }}
                <div class="row">
                    <div class="col-md-3 col-sm-12 pt-2">
                        {{ form_label(form.vin) }}
                    </div>
                    <div class="col-md-4 col-sm-12 pt-2">
                        {{ form_label(form.accountId) }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_widget(form.vin) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_widget(form.accountId) }}
                        </div>
                    </div>
                    <div class="col-md-1 text-right">
                        <button class="btn btn-info" type="submit">{{ button_label|default('filter') |trans }}</button>
                    </div>
                </div>
            {{ form_end(form) }}

             <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="p-0"></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.id{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.brand{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.type{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.age{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.distance{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.performed_date{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.created{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}performed_maintenances.updated{% endtrans %}</small></th>
                    </tr>
                    </thead>
                   
                </table>
            </div>
        

            
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.dateTime.min.js') }}"></script>
    <script src="{{ asset('js/moment.min.js') }}"></script>
    <script src="{{ asset('js/dataRender/datetime.js') }}"></script>
    
    <script>
       
    /* Formatting function for row details - modify as you need */
function format(d) {
// `d` is the original data object for the row

    // function to check if value is null or undefined it returns an empty string
     checkNullValues =(value) => {
        return value || "";
    }

return (
    '<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;">' +
    '<tr>' +
    '<td>{{ "performed_maintenances.severity"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.severity) +
    '</td>' +
    '</tr>' +
     '<tr>' +
    '<td>{{ "performed_maintenances.mileage"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.mileage) +
    '</td>' +
    '</tr>' +
     '<tr>' +
    '<td>{{ "performed_maintenances.cost"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.cost) +
    '</td>' +
    '</tr>' +
     '<tr>' +
    '<td>{{ "performed_maintenances.comments"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.comments) +
    '</td>' +
    '</tr>' +
    '<tr>' +
    '<td>{{ "performed_maintenances.source"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.source) +
    '</td>' +
    '</tr>' +
    '<tr>' +
    '<td>{{ "performed_maintenances.reference"|trans }}</td>' +
    '<td>' +
    checkNullValues(d.reference)  +
    '</td>' +
    '</tr>' +
    '</table>'
);
}

$(document).ready(function () {
var data = "{{maintenances|json_encode()|escape('js')}}" ;
var arr = new Array();
arr = JSON.parse(data);
var table = $('#dataTable').DataTable({
    "paging" : false,
    "info" : false,
    "searching": false,
    "responsive": false,
    processing: true, 
    data: arr,
    columns: [
        {
            className: 'dt-control',
            orderable: false,
            data: null,
            defaultContent: '',
        },
        { data: 'id' },
        { data: 'brand' },
        { data: 'type' },
        { data: 'age'},
        { data: 'distance'}, 
        { data: 'timestamp' , render: $.fn.dataTable.render.moment( 'X', 'Y-MM-DD HH:mm:ss' )},  
        { data: 'created' , render: $.fn.dataTable.render.moment( 'X', 'Y-MM-DD HH:mm:ss' )}, 
        { data: 'updated' ,  render: $.fn.dataTable.render.moment( 'X', 'Y-MM-DD HH:mm:ss' ) }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    ],
    order: [[1, 'asc']],
});

// Add event listener for opening and closing details
$('#dataTable tbody').on('click', 'td.dt-control', function () {
    var tr = $(this).closest('tr');
    var row = table.row(tr);

    if (row.child.isShown()) {
        // This row is already open - close it
        row.child.hide();
        tr.removeClass('shown');
    } else {
        // Open this row
        row.child(format(row.data())).show();
        tr.addClass('shown');
    }
});
});
</script>

{% endblock %}


