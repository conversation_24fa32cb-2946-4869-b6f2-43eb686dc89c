{% set detailsForm = form.contentVersionDetails %}
{% set eventsForm = form.contentVersionDetails.contentClub %}
<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            <div class="row">
                <div class="col-md-2">{{ form_label(detailsForm.boTitle) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.boTitle) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.title) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.title) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            
            <p>{{ 'content.introduction' | trans }}</p>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.shortText) }}</div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.shortText) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.filterOne) }} </div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.filterOne) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.filterTwo) }} </div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.filterTwo) }}
                </div>
            </div>


            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.eventDate) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.eventDate) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.nbParticipants) }} </div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.nbParticipants) }}
                </div>
            </div>


            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <p>{{ 'content.club.events_fields.booking' | trans }}</p>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.reservationStartDate) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.reservationStartDate) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.reservationEndDate) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.reservationEndDate) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.isComplete) }}</div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.isComplete) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <p>{{ 'content.club.events_fields.location' | trans }}</p>            

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.city) }}</div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.city) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.country) }}</div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.country) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.latitude) }}</div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.latitude) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(eventsForm.longitude) }}</div>
                <div class="col-md-3">
                    {{ form_widget(eventsForm.longitude) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

             {{ include('admin/content/club_media_paragraph.html.twig', {'form': form }) }}
        </div>
    </div>
</div>