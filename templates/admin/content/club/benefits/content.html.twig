{% set detailsForm = form.contentVersionDetails %}
{% set benefitsForm = form.contentVersionDetails.contentClub %}
<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            <div class="row">
                <div class="col-md-2">{{ form_label(detailsForm.boTitle) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.boTitle) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.title) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.title) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.contentClubCoupon.type) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.contentClubCoupon.type) }}
                </div>
            </div>
             <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            
            <p>{{ 'content.introduction' | trans }}</p>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.shortText) }}</div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.shortText) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">


            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.filterOne) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.filterOne) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.filterTwo) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.filterTwo) }}
                </div>
            </div>


            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <div id="reservationType">

               <div class="row mt-2" id="maxParticipantsType">
                <div class="col-md-2">{{ form_label(benefitsForm.contentClubCoupon.nbParticipants) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.contentClubCoupon.nbParticipants) }}
                </div>
               </div>

                <div class="row mt-2" id="codeType">
                <div class="col-md-2">{{ form_label(benefitsForm.contentClubCoupon.code) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.contentClubCoupon.code) }}
                </div>
               </div>

                <div class="row mt-2" id="urlType">
                <div class="col-md-2">{{ form_label(benefitsForm.contentClubCoupon.url) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.contentClubCoupon.url) }}
                </div>
               </div>

                <div class="row mt-2" id="labelType">
                <div class="col-md-2">{{ form_label(benefitsForm.contentClubCoupon.label) }} </div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.contentClubCoupon.label) }}
                </div>
               </div>

            
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <p>{{ 'content.club.benefits_fields.validity' | trans }}</p>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.reservationStartDate) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.reservationStartDate) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.reservationEndDate) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.reservationEndDate) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.isComplete) }}</div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.isComplete) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

            <p>{{ 'content.club.events_fields.location' | trans }}</p>            

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.city) }}</div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.city) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.country) }}</div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.country) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.latitude) }}</div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.latitude) }}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(benefitsForm.longitude) }}</div>
                <div class="col-md-3">
                    {{ form_widget(benefitsForm.longitude) }}
                </div>
            </div>

            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">

             {{ include('admin/content/club_media_paragraph.html.twig', {'form': form }) }}
        </div>
    </div>
</div>