{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
        .pdf-popup-modal .thumbnails li {
            width: 200px;
        }
        .pdf-popup-modal .thumbnails li .thumbnail p {
            font-size: 12px;
        }
        .pdf-popup-modal ul.thumbnails.image_picker_selector li .thumbnail.selected {
            background: unset;
            border: 2px solid #4e73df;
        }
        table#published-versions {
            width: 100%;
        }
        table#published-versions td, th {
            border: 1px solid #e3e6f0;
            text-align: left;
            padding: 8px;
        }
        table#published-versions tr:nth-child(even) {
            background-color: #e3e6f0;
        }
        .paragraph-container {
            padding:25px 10px;
        }
        .paragraph-container:nth-child(odd) {
            background-color: #FAEADA;
        }
        .paragraph-container:nth-child(even) {
            background-color: #F9FDF3;
        }
    </style>
{% endblock %}

{% block body %}
    {% if content is not defined %}
        <h3 class="h3 mb-4 text-gray-800">{% trans %}content.info_carburant.new{% endtrans %} ({{ language.label }} ({{ language.code }}))</h3> 
    {% else %}
        <h3 class="h3 mb-4 text-gray-800">{% trans %}content.info_carburant.edit{% endtrans %} (CID {{ content.id }})</h3> 
    {% endif %}
    {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
        {{ include('admin/content/info_carburant/content.html.twig', {'form': form, 'section': 'content', 'is_current': true }) }}
        {{ include('admin/content/publication.html.twig', {'form': form, 'section': 'publication', 'is_current': false }) }}
        {{ include('admin/content/seo.html.twig', {'form': form, 'section': 'seo', 'is_current': false }) }}
        {% if content is defined %}
            {{ include('admin/content/published_versions.html.twig',
             {'section': 'published', 'content': content, 'is_current': false, 'triggerOnlineOffline': 'info_carburant_content_trigger_online_offline', 'putAsDraft': 'info_carburant_content_draft' }) }}
        {% endif %}
        <input type="hidden" value="0" name="publish" id="publish-button-clicked" />
        <div class="text-right mt-3">
            <button id="publish-button" class="btn btn-primary mr-3" type="submit"><i class="fas fa-upload"></i> {{ "content.publish" |trans }}</button>
            <button class="btn btn-success mr-3" type="submit"><i class="fas fa-save"></i> {{ button_label|default('save') |trans }}</button>
            <a class="btn btn-dark" role="button" href="{{ path('info_carburant_index',{'profile': profile.id}) }}">
                <i class="fas fa-undo"></i> {% trans %}Annuler{% endtrans %}
            </a>
        </div>
        {{ form_row(form._token) }}
    {{ form_end(form, {'render_rest': false}) }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/multiselect.js') }}"></script>
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#' + "{{ form.contentCta.template.vars.id }}").change(function() {
                if (!this.value) {
                    $('.template-selected').hide();
                    $('.template-details').hide();
                    return;
                }
                if (this.value == 2 || this.value == 3) {
                    $('.template-selected').show();
                    $('.template-details').hide();
                } else if (this.value == 1) {
                    $('.template-selected').show();
                    $('.template-details').show();
                }
            });
            $('#' + "{{ form.contentCta.template.vars.id }}").trigger('change');
            $("form[name={{ form.vars.name }}]").submit(function(e) {
                e.preventDefault();
                if (document.activeElement.id == "publish-button") {
                    $("#publish-button-clicked").val(1);
                }
                $(this).off('submit').submit();
            });
            $('.jslink').on('click', function(e) {
                var $wrapper = $('.paragraph-wrapper');
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                newForm = newForm.replace(/__INDEXOR__/, (index + 1));
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $wrapper.append(newForm);
                $(".image-picker").imagepicker({hide_select: true});
            });
            $('.paragraph-wrapper').on('click', '.delete-paragraph', function() {
                var $wrapper = $('.paragraph-wrapper');
                var index = $wrapper.data('index');
                $(this).closest('.paragraph-container').remove();
                $wrapper.data('index', index - 1);
                $(".paragraph-indexor").each(function(index, element) {
                    $(this).text(index + 1);
                });
            });
            
            $(".image-picker").imagepicker({hide_select: true});
            $(document).on('click', ".delete-image", function() {
                $(this).addClass('d-none');
                parent = $(this).closest('.image-container');
                parent.children(".image-link-holder").removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
                parent.find(".image-picker-selector").val(null).change();
            });
            $(document).on('click', '.preview-image-change', function(event) {
                parent = $(this).closest('.image-container');
                linkSrc = parent.find(".image-picker-selector option:selected").data("img-real-src");
                imageSrc = parent.find(".image-picker-selector option:selected").data("img-src");
                if (!linkSrc) {
                    linkSrc = imageSrc;
                }
                imageAlt = parent.find(".image-picker-selector option:selected").data("img-alt");
                parent.children(".image-link-holder").attr("href", linkSrc).removeClass('d-none')
                    .children(".image-holder").attr("src", imageSrc).attr("alt", imageAlt);
                parent.find(".delete-image").removeClass('d-none');
            });
        });
    </script>
{% endblock %}