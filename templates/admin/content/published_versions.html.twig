{% set publishedV = 0 %}
{% set versions = content.contentVersions %}
{% set currentVersionId = content.currentVersion ? content.currentVersion.id : null %}
{% for c in versions %}
    {% if c.isPublished() %}
        {% set publishedV = publishedV + 1 %}
    {% endif %}
{% endfor %}
{% if publishedV > 0 %}
    <div class="card">
        <div class="card-header" id="header-{{ section }}">
            <h6 class="mb-0 font-weight-bold text-primary">
                <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                    href="#section-{{ section }}" role="button" aria-expanded="true"
                    aria-controls="section-{{ section }}">
                    {{ ("SECTION" ~ '-' ~ section) | trans }} ({{ publishedV }})
                </a>
            </h6>
        </div>
        <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
            <div class="overflow-auto card-body" style="overflow-y:scroll">
                <table id="published-versions" >
                    {% for c in versions|reverse %}
                        {% if c.isPublished() %}
                            <tr>
                                <td>
                                    {% if c.contentVersionDetails  %}
                                        {{ c.contentVersionDetails.boTitle }}
                                    {% else %}
                                        {{ 'no_details' | trans }}
                                    {% endif %}
                                </td>
                                <td>{{ c.updatedAt | date('m/d/Y  H:i')}} ({{ c.updator ?? c.creator }})</td>
                                <td>
                                    {% if currentVersionId == c.id %}
                                        <a href="{{path(triggerOnlineOffline, {'profile': profile.id, 'content': content.id })}}" class="btn btn-dark">
                                        {% if content.isOnline  %}
                                            {{ 'content.push_offline' | trans }}
                                        {% else %}
                                            {{ 'content.push_online' | trans }}
                                        {% endif %}
                                        </a>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{path(putAsDraft, {'profile': profile.id, 'contentVersion': c.id})}}" class="btn btn-dark"> 
                                        {{ 'content.put_draft' | trans }}
                                    </a>
                                </td>
                            </tr>
                        {% endif %}
                    {% endfor %}  
                </table>
            </div>
        </div>
    </div>
{% endif %}
