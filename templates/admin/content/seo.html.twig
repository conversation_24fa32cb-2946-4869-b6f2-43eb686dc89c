<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.contentVersionSeo.metaRobotsType) }}</div>
                <div class="col-md-3">
                    {{ form_widget(form.contentVersionSeo.metaRobotsType) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.contentVersionSeo.clearUrl) }}</div>
                <div class="col-md-5">
                    {{ form_widget(form.contentVersionSeo.clearUrl) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.contentVersionSeo.metaCanonicalUrl) }}</div>
                <div class="col-md-5">
                    {{ form_widget(form.contentVersionSeo.metaCanonicalUrl) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.contentVersionSeo.priority) }}</div>
                <div class="col-md-5">
                    {{ form_widget(form.contentVersionSeo.priority) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.contentVersionSeo.contentVersionSeoRewrites) }}</div>
                <div class="col-md-6">
                    {{ form_widget(form.contentVersionSeo.contentVersionSeoRewrites) }}
                </div>
            </div>
        </div>
    </div>
</div>