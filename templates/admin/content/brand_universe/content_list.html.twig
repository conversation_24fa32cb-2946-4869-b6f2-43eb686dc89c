<div class="mt-1 mb-4 text-right">
    <a role="button" class="btn btn-primary" href="{{ path('brand_universe_new', {'profile': profile.id, 'language': language.id}) }}"> <i class="fas fa-plus"></i> {% trans %}content.brand_universe.add_cta{% endtrans %}</a>
</div>
<div class="table-responsive">
    <table class="table table-bordered {% if contents %}datatable-brand-universe{% endif %}">
        <thead>
            <tr class="text-primary">
                <th> {% trans %}content.list.title{% endtrans %}</th>
                <th> {% trans %}content.list.category{% endtrans %}</th>
                <th> {% trans %}content.list.online{% endtrans %}</th>
                <th> {% trans %}content.list.date_begin{% endtrans %}</th>
                <th> {% trans %}content.list.date_end{% endtrans %}</th>
                <th> {% trans %}content.list.publish_date{% endtrans %}</th>
                <th> {% trans %}content.list.update_date{% endtrans %}</th>
                <th> {% trans %}content.list.state{% endtrans %}</th>
                <th width="15%" class="text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for content in contents %}
                {% set currentVersion = content.draftVersion ? content.draftVersion : content.currentVersion %}
                <tr>
                    <td>{{ currentVersion.contentVersionDetails.boTitle }}</td>
                    <td>{{ currentVersion.category.label }}</td>
                    <td class="text-center"><i class='{{ content.isOnline ? "fas fa-check text-success" : "fa fa-ban text-danger"}}'></i></td>
                    <td>{{ currentVersion.startDate ? currentVersion.startDate | date('d/m/Y H:i') : '' }}</td>
                    <td>{{ currentVersion.endDate ? currentVersion.endDate | date('d/m/Y H:i') : '' }}</td>
                    <td>{{ currentVersion.publicationDate ? currentVersion.publicationDate | date('d/m/Y H:i') : '' }}</td>
                    <td>{{ currentVersion.updatedAt ? currentVersion.updatedAt | date('d/m/Y  H:i') : '' }}</td>
                    <td>{{ currentVersion.state.label | trans }}</td>                            
                    <td class="text-center">
                        <a href="{{ path('brand_universe_edit', {profile: profile.id, content: content.id}) }}"
                        class="btn btn-sm btn-warning">{% trans %}Modifier{% endtrans %}</a>
                        <a href="#delete-content-modal" class="btn btn-sm btn-danger"
                        data-content-id="{{ content.id }}">{% trans %}Supprimer{% endtrans %}</a>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="9" class="text-center">
                        {% trans %}content.list.empty{% endtrans %}
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>