{% import _self as formMacros %}
{% macro customPrototype(paragraph, lIndex) %}
    <div class="paragraph-container">
        <div class="row mt-2">
            <div class="col-md-2">
                <label>{{ 'content.paragraph.text_identifier' | trans }} <span class="paragraph-indexor">{{ lIndex ?? '__INDEXOR__' }}</span></label>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.text) }}</div>
            <div class="col-md-5">
                {{ form_widget(paragraph.text) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.legend) }}</div>
            <div class="col-md-5">
                {{ form_widget(paragraph.legend) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.media) }}</div>
            {% set imageNotExist = (paragraph.vars.data.media is not defined) or (not paragraph.vars.data.media) %}
            {% set imageModalId = paragraph.vars.id %}
            <div class="col-9 image-container">
                <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
                    {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ paragraph.vars.data.media.path }}" {% endif %} style="text-decoration: none">
                    <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                        src="{{ mediaUrl ~ '/' ~ paragraph.vars.data.media.path }}" alt="{{ paragraph.vars.data.media.textAlt }}" {% endif %}>
                </a>
                <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
                {{ include('admin/content/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': paragraph.media}) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-7 text-right">
                <button class="btn btn-danger delete-paragraph" type="button">{% trans %}content.delete_paragraph{% endtrans %}</button>
            </div>
        </div>
    </div>
{% endmacro %}            
<div class="row mt-2">
    <div class="col-md-2">{{ form_label(form.contentCta.template) }} <span class="mandatory">*</span></div>
    <div class="col-md-3">
        {{ form_widget(form.contentCta.template) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-2">{{ form_label(form.contentCta.label) }} <span class="mandatory">*</span></div>
    <div class="col-md-3">
        {{ form_widget(form.contentCta.label) }}
    </div>
</div>
<div class="template-selected">
    <hr style="border-top: 1px dotted #8c8b8b; margin: 25px 0px; width: 30%;">
    <div class="row mt-2">
        <div class="col-md-2">{{ form_label(form.contentCta.url) }}</div>
        <div class="col-md-5">
            {{ form_widget(form.contentCta.url) }}
        </div>
    </div>
</div>
<div class="template-details">
    <div class="row mt-2">
        <div class="col-md-2">{{ form_label(form.contentCta.detailCtaLabel) }}</div>
        <div class="col-md-5">
            {{ form_widget(form.contentCta.detailCtaLabel) }}
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md-2">{{ form_label(form.contentCta.detailUrl) }}</div>
        <div class="col-md-5">
            {{ form_widget(form.contentCta.detailUrl) }}
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md-2">{{ form_label(form.contentCta.detailOpeningType) }}</div>
        <div class="col-md-3">
            {{ form_widget(form.contentCta.detailOpeningType) }}
        </div>
    </div>
    <div class="paragraph-wrapper mt-2"
        data-prototype="{{ formMacros.customPrototype(form.contentParagraphs.vars.prototype)|e('html_attr') }}"
        data-index="{{ form.contentParagraphs|length }}">
        {% for paragraph in form.contentParagraphs %}
            {{ formMacros.customPrototype(paragraph, loop.index) }}
        {% endfor %}
    </div>
    <div class="row mt-3">
        <div class="col-2">
            <button class="btn btn-info jslink"> {{ "content.add_paragraph" | trans }}</button>
        </div>                
    </div>
</div>