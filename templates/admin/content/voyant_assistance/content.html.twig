{% set detailsForm = form.contentVersionDetails %}
<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            <div class="row">
                <div class="col-md-2">{{ form_label(detailsForm.boTitle) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.boTitle) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.title) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.title) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(form.category) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(form.category) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.contentText) }}</div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.contentText) }}
                </div>
            </div>
            
        </div>
    </div>
</div>