<div class="mt-1 mb-4 text-right">
    <a role="button" class="btn btn-primary" href="{{ path('admin_content_category_new', {'profile': profile.id, 'language': language.id, 'type': app.request.get('type').type}) }}"> <i class="fas fa-plus"></i>
        {{ ('content.categories.add_cta.' ~ app.request.get('type').type)  | trans }}
    </a>
</div>
<table class="table table-bordered {% if categories %}dataTable{% endif %}">
    <thead>
    <tr class="text-primary">
        <th>{% trans %}content.categories.label{% endtrans %}</th>
        <th class="text-center" style="width : 20%;">Actions</th>
    </tr>
    </thead>
    <tbody>
    {% for categorie in categories %}
        <tr>
            <td>{{ categorie.label }}</td>
            <td class="text-center">
                <a href="{{ path('admin_content_category_edit', {profile: profile.id, category: categorie.id}) }}"
                    class="btn btn-sm btn-warning">{% trans %}Modifier{% endtrans %}</a>
                <a href="#delete-categorie-modal" class="btn btn-sm btn-danger"
                    data-categorie-id="{{ categorie.id }}">{% trans %}Supprimer{% endtrans %}</a>
            </td>
        </tr>
    {% else %}
        <tr>
            <td colspan="2" class="text-center">
                {% trans %}content.categories.list.empty{% endtrans %}
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>