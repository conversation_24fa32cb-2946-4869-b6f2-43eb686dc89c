{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800">
        {% if not category.id %}
            {{ ('content.categories.new.' ~ category.type.type) | trans }}
        {% else %}
            {{ ('content.categories.edit.' ~ category.type.type) | trans }} ({{ category.label }})
        {% endif %}
    </h3>

    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-1">
                            {{ form_label(form.label) }}
                        </div>
                        <div class="col-md-3">
                            {{ form_widget(form.label) }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-right">
                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_content_category_index',{'profile': profile.id, 'type': category.type.type}) }}">{% trans %}Annuler{% endtrans %}</a>
                <button class="btn btn-primary" type="submit">{{ button_label|default('save') |trans }}</button>
            </div>
        </div>
    {{ form_end(form) }}

{% endblock %} 