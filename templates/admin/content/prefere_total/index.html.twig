{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %} content.prefere_total.title {% endtrans %}</h1>

    <div class="card shadow mb-4">
        <div class="card-body">
            {{ form_start(form, {}) }}
            <div class="mb-3">
                <h1 class="h3 mb-4 text-gray-800">{% trans %}content.prefere_total.title{% endtrans %}</h1>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ "activation" | trans }}
                </div>
                <div class="col-md-7">
                    {{ form_widget(form.enabled) }}
                </div>
            </div>
            <div class="mt-2 float-right">
                <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                {% for language in profile.site.languages %}
                    <li class="nav-item">
                        <a class="nav-link {% if language.code == 'fr'%}active{% endif %}" data-toggle="tab"
                            href="#prefere_total-{{ language.code }}" style="min-width: 180px">
                            <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                            <span class="m-0 font-weight-bold text-primary">{{ language.label }}</span>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;">
            <div class="card-body">
                <div class="tab-content">
                    {% for language in profile.site.languages %}
                        <div class="tab-pane {% if language.code == 'fr'%}active{% endif %}" id="prefere_total-{{ language.code }}">
                            {{ include('admin/content/prefere_total/content_list.html.twig', {'language': language, 'contents': contents[language.id] ?? []}) }}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-content-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('prefere_total_delete', {profile: profile.id, content: ':id'}) }}" id="delete-content-form" method="POST">
            {# <input type="hidden" name="token" value="{{ csrf_token('delete-media') }}"/> #}
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}content.prefere_total.delete_text{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
            $(document).ready(function() {$('.datatable-prefere_total').DataTable();});
            var $deletePrefereModal = $('div#delete-content-modal'),
                $deletePrefereForm  = $deletePrefereModal.find('form#delete-content-form'),
                deletePrefereAction = $deletePrefereForm.attr('action');

            $('a[href="#delete-content-modal"]').on('click', function (event) {
                event.preventDefault();
                console.log()
                $deletePrefereForm.attr('action', deletePrefereAction.replace(':id', $(this).attr('data-content-id')));
                $deletePrefereModal.modal('show');
            });

            $deletePrefereModal.on('hidden.bs.modal', function () {
                $deletePrefereForm.attr('action', deletePrefereAction);
            });
    </script>
{% endblock %}