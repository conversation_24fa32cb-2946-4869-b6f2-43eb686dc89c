{% extends '_layout/base_back.html.twig' %}
{% set medias = profile.site.medias %}
{% block stylesheets %}
	<link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
	<style type="text/css">
		.thumbnails li img {
			width: 180px;
			height: 180px;
		}
		.pdf-popup-modal .thumbnails li {
			width: 200px;
		}
		.pdf-popup-modal .thumbnails li .thumbnail p {
			font-size: 12px;
		}
		.pdf-popup-modal ul.thumbnails.image_picker_selector li .thumbnail.selected {
			background: unset;
			border: 2px solid #4e73df;
		}
	</style>
{% endblock %}
{% block body %}
	{{ include('admin/content/prefere_total/media_modal.html.twig') }}
	<div class="card">
		<div class="card-header" id="header-{{ section }}">
			<h6 class="mb-0 font-weight-bold text-primary">
				<a class="float-left text-left text-decoration-none w-100" data-toggle="collapse" href="#section-{{ section }}" role="button" aria-expanded="true" aria-controls="section-{{ section }}">
					Prefere Total Logo Configuration
				</a>
			</h6>
		</div>
		<div id="section-{{ section }}" class="collapse show" aria-labelledby="header-{{ section }}">
			<div class="overflow-auto card-body" style="overflow-y:scroll">
				{{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
				<div>
					{% set languagesLength = profile.site.languages.count %}
					{% for language in profile.site.languages %}
						{% set formL = form['form-' ~ language.code ] %}
						{% if languagesLength == 1 %}
							<div class="row mt-5">
								{% set imageNotExist = (formL.vars.data.prefer_total_image is not defined) or (not formL.vars.data.prefer_total_image) %}
								{% set imageModalId = section ~ '-' ~ language.code %}
								<div class="col-3">{{ form_label(formL.prefer_total_image) }}</div>
								<div class="col-9 image-container">
									<a target="_blank" class="image-link-holder-{{formL.prefer_total_image.vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}" {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.prefer_total_image.path }}" {% endif %} style="text-decoration: none">
										<img class="image-holder-{{formL.prefer_total_image.vars.id}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %} src="{{ mediaUrl ~ '/' ~ formL.vars.data.prefer_total_image.path }}" alt="{{ formL.vars.data.prefer_total_image.textAlt }}" {% endif %}>
									</a>
									<a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick="addModal('{{formL.prefer_total_image.vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
									<button class="btn btn-sm btn-danger delete-image-{{formL.prefer_total_image.vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick="deleteMedia('{{formL.prefer_total_image.vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>
								</div>
							</div>
						{% else %}
							<div class="card">
								<div class="card-header" id="{{'heading-' ~ section ~ '-' ~ language.code }}" style="background-color: lemonchiffon;">
									<h6 class="mb-0">
										<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ section ~ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{ section ~ language.code }}">
											<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
											{{ language.label }}
										</a>
									</h6>
								</div>
								<div id="{{ 'section-' ~ section ~ language.code }}" class="collapse" aria-labelledby="{{'heading-' ~ section ~ '-' ~ language.code }}">
									<div class="card-body" id="form_container" data-language="{{language.code}}">
										<div class="row mt-5">
											{% set imageNotExist = (formL.vars.data.prefer_total_image is not defined) or (not formL.vars.data.prefer_total_image) %}
											{% set imageModalId = section ~ '-' ~ language.code %}
											<div class="col-3">{{ form_label(formL.prefer_total_image) }}</div>
											<div class="col-9 image-container">
												<a target="_blank" class="image-link-holder-{{formL.prefer_total_image.vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}" {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.prefer_total_image.path }}" {% endif %} style="text-decoration: none">
													<img class="image-holder-{{formL.prefer_total_image.vars.id}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %} src="{{ mediaUrl ~ '/' ~ formL.vars.data.prefer_total_image.path }}" alt="{{ formL.vars.data.prefer_total_image.textAlt }}" {% endif %}>
												</a>
												<a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick="addModal('{{formL.prefer_total_image.vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
												<button class="btn btn-sm btn-danger delete-image-{{formL.prefer_total_image.vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick="deleteMedia('{{formL.prefer_total_image.vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>

											</div>
										</div>
									</div>
								</div>
							</div>
						{% endif %}
					{% endfor %}
				</div>
				<div class="text-right mt-3">
					<button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
				</div>
				<div class="d-none">
					{{ form_rest(form) }}
				</div>
				{{ form_end(form) }}
			</div>
		</div>
	</div>
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	<script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
	{{ include('admin/medias/js/js.html.twig', { 'hideBtn': 'true' }) }}
	<script>
		$(function () {
$(".image-picker").imagepicker({hide_select: true});
$(".pdf-picker").imagepicker({hide_select: true, show_label: true});
});

function addModal(formId) {
$('#form-img-holder').val(formId);
}

function validateImage() {
if ($('.cadre-image.selected').attr('id')) {
idMedia = $('.cadre-image.selected').attr('id');
id = idMedia.split('-')[1];
formId = $('#form-img-holder').val();
$('#' + formId).val(id);
imageSrc = $('.cadre-image.selected img').attr('src');
imageAlt = $('.cadre-image.selected img').attr('alt');
imageHref = $('.cadre-image.selected img').data('path');
$(".image-holder-" + formId).attr("src", imageSrc).attr("alt", imageAlt);
$(".image-link-holder-" + formId).attr("href", imageHref).removeClass('d-none');
$(".delete-image-" + formId).removeClass('d-none');
}
}

function deleteMedia(formId) {
$(".delete-image-" + formId).addClass('d-none');
parent = $(this).closest('.image-container');
$(".image-link-holder-" + formId).removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
$('#' + formId).val(null).change();
}
	</script>
{% endblock %}
