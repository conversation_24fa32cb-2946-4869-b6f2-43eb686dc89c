{% set detailsForm = form.contentVersionDetails %}
<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            <div class="row">
                <div class="col-md-2">{{ form_label(detailsForm.boTitle) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.boTitle) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.title) }} <span class="mandatory">*</span></div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.title) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            {{ include('admin/content/targeting.html.twig', {'form': form }) }}
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.styleSheet) }} <span class="mandatory">*</span></div>
                <div class="col-md-3">
                    {{ form_widget(detailsForm.styleSheet) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            <p>{{ 'content.introduction' | trans }}</p>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.shortText) }}</div>
                <div class="col-md-7">
                    {{ form_widget(detailsForm.shortText) }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-2">{{ form_label(detailsForm.media) }}</div>
                {% set imageNotExist = (detailsForm.vars.data.media is not defined) or (not detailsForm.vars.data.media) %}
                {% set imageModalId = 'into-visual' %}
                <div class="col-9 image-container">
                    <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
                        {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ detailsForm.vars.data.media.path }}" {% endif %} style="text-decoration: none">
                        <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                            src="{{ mediaUrl ~ '/' ~ detailsForm.vars.data.media.path }}" alt="{{ detailsForm.vars.data.media.textAlt }}" {% endif %}>
                    </a>
                    <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                    <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
                    {{ include('admin/content/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': detailsForm.media}) }}
                </div>
            </div>
            <hr style="border-top: 1px solid #8c8b8b; margin: 35px 0px; width: 80%;">
            {{ include('admin/content/cta.html.twig', {'form': form }) }}
        </div>
    </div>
</div>