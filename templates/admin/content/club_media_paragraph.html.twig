{% import _self as formMacros %}
{% macro customPrototype(paragraph, lIndex) %}
    <div class="paragraph-container">
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.title) }}</div>
            <div class="col-md-5">
                {{ form_widget(paragraph.title) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.text) }}</div>
            <div class="col-md-5">
                {{ form_widget(paragraph.text) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(paragraph.isLink) }}</div>
            <div class="col-md-5">
                {{ form_widget(paragraph.isLink) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-7 text-right">
                <button class="btn btn-danger delete-paragraph" type="button">{% trans %}content.delete_paragraph{% endtrans %}</button>
            </div>
        </div>
    </div>
{% endmacro %} 

{% macro customPrototypeMedia(media, lIndex) %}
    <div class="paragraph-container">
        <div class="row mt-2">
            <div class="col-md-2">{{ form_label(media.media) }}</div>
            {% set imageNotExist = (media.vars.data.media is not defined) or (not media.vars.data.media) %}
            {% set imageModalId = media.vars.id %}
            <div class="col-9 image-container">
                <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}"
                    {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ media.vars.data.media.path }}" {% endif %} style="text-decoration: none">
                    <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                        src="{{ mediaUrl ~ '/' ~ media.vars.data.media.path }}" alt="{{ media.vars.data.media.textAlt }}" {% endif %}>
                </a>
                <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
                {{ include('admin/content/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': media.media}) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-7 text-right">
                <button class="btn btn-danger delete-media" type="button">{% trans %}content.delete_media{% endtrans %}</button>
            </div>
        </div>
    </div>
{% endmacro %} 

<div class="template-details">
    <div class="paragraph-wrapper mt-2"
        data-prototype="{{ formMacros.customPrototypeMedia(form.contentMedias.vars.prototype)|e('html_attr') }}"
        data-index="{{ form.contentMedias|length }}">
        {% for media in form.contentMedias %}
            {{ formMacros.customPrototypeMedia(media, loop.index) }}
        {% endfor %}
    </div>
    <div class="row mt-3">
        <div class="col-2">
            <button class="btn btn-info jslink"> {{ "content.add_media" | trans }}</button>
        </div>                
    </div>
</div>

<div class="template-details">
    <div class="paragraph-wrapper mt-2"
        data-prototype="{{ formMacros.customPrototype(form.contentParagraphs.vars.prototype)|e('html_attr') }}"
        data-index="{{ form.contentParagraphs|length }}">
        {% for paragraph in form.contentParagraphs %}
            {{ formMacros.customPrototype(paragraph, loop.index) }}
        {% endfor %}
    </div>
    <div class="row mt-3">
        <div class="col-2">
            <button class="btn btn-info jslink"> {{ "content.add_paragraph" | trans }}</button>
        </div>                
    </div>
</div>