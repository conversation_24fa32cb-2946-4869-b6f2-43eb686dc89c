{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}

	<div class="mt-5 mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('admin_encryption_key_create', {'profile': profile.id}) }}">{% trans %}Uploader fichier{% endtrans %}</a>
    </div>

    <div class="card shadow mb-4">
        
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>Target</th>
                        <th>{% trans %}Name{% endtrans %}</th>
                        {#<th>{% trans %}Download{% endtrans %}</th>#}
                    </tr>
                    </thead>
                    <tbody>
                    {% for file in files %}
                        <tr>
                            <td>{{ file.target }}</td>
							<td>{{ file.file_name }}</td>
                            {#<td class="text-right">
                                <a href="{{ file.url }}" class="btn btn-sm btn-warning">{% trans %}Download{% endtrans %}</a>
                            </td>#}
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="8" class="text-center">
                                {% trans %}files_list_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}



{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>    
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                    stateSave: true,
                    'order': [[ 1, "desc" ]],
                    'autoWidth': false,
                }
            );
        });
    </script>
{% endblock %}
