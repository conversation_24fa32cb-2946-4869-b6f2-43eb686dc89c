{% extends '_layout/base_back.html.twig' %}

{% block body %}
	{% for message in app.flashes('success') %}
		<div class="alert alert-success">
			{{ message | trans }}
		</div>
	{% endfor %}
	{% for message in app.flashes('error') %}
		<div class="alert alert-danger">
			{{ message | trans }}
		</div>
	{% endfor %}
	{% for error in errors %}
		<div class="alert alert-danger">
			{{error.origin.name | trans}}
			:
			{{ error.message|trans }}
		</div>
	{% endfor %}
	{{ form_start(form) }}
	<div class="card shadow mb-3">
		<div class="accordion" id="accordionParent">
			<div class="card">
				<a href="#encryption_key_form" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="encryption_key_form">
					<h6 class="m-0 font-weight-bold text-primary">{{ 'file_form_label'|trans }}</h6>
				</a>
				<!-- Card Content - Collapse -->
				<div class="collapse show" id="encryption_key_form" style="">
					<div class="card-body">
						<div class="mt-2">
							<div class="row mb-4">
								<div class="col-md-3">
									{{ form_label(form.target) }}
									<span class="mandatory">*</span>
								</div>
								<div class="col-md-7">
									{{ form_widget(form.target) }}
								</div>
							</div>
							<div class="row mb-4">
								<div class="col-md-3">
									{{ 'key'|trans  }}
									<span class="mandatory">*</span>
								</div>
								<div class="col-md-3">
									{{ form_widget(form.file) }}
									{{ form_errors(form.file) }}
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="mt-2 mb-4 float-right">
		<button id="form_submitor" class="btn btn-primary float-right" type="submit">{{ 'save'|trans }}</button>
	</div>
	{{ form_end(form) }}
{% endblock %}
