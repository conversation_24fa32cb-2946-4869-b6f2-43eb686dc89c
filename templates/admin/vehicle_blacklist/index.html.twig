{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %} Black list Véhicules  {% endtrans %} {{type|upper}}</h1>
    <div class="mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('eligibility_smartapps_blacklist_new' , {'type': type,  'profile': profile.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
    </div>
    <div class="card shadow mb-4">
        <div class="card-body">

            <div class="table-responsive">
                <table id="dataTable" class="table table-bordered table-hover dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}VIN{% endtrans %}</th>
                        <th>Marque</th>

                        <th class="text-right">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for vehicle_blacklist in eligibility_vehicles_blacklist %}
                        <tr>
                            <td>{{ vehicle_blacklist.vin }}</td>
                            <td>{{ vehicle_blacklist.brand }}</td>

                            <td class="text-right">
                                <a href="{{ path('eligibility_smartapps_blacklist_edit', {'type':type, profile: profile.id, id: vehicle_blacklist.id}) }}" class="btn btn-sm btn-warning"
                                   data-toggle="tooltip" data-title="">{% trans %}Modifier{% endtrans %}</a>
                                <a href="#delete-eligibility-modal" class="btn btn-sm btn-danger"
                                   data-toggle="tooltip" data-title="" data-eligibility-id="{{vehicle_blacklist.id}}">{% trans %}Supprimer{% endtrans %}</a>
                                   
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% trans %}vehicle_blacklist_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
<!-- DELETE -->
<div class="modal fade" id="delete-eligibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
    <form action="{{ path('eligibility_smartapps_blacklist_delete', {'type':type, profile: profile.id, id: ':id'}) }}" id="delete-eligibility-form" method="POST">
        <input type="hidden" name="token" value="{{ csrf_token('delete-eligibility-blacklist') }}"/>
        <input type="hidden" name="_method" value="DELETE">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="delete">Confirmation</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette véhicule{% endtrans %} ?</div>
                <div class="modal-footer">
                    <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
<script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
<script>
    $(document).ready(function() {
        let table = $('#dataTable').DataTable({
                stateSave: true,
                paging: false,
                'order': [[ 1, "desc" ]]
            }
        );
        table.on('draw', function () {
            $(".truncate").each(function(){
                if($(this).text().trim().length > 100){
                    let text = $(this).text().trim().substring(0 , 100) + '...';
                    $(this).html(text);
                }
            });
        })
   
        var $deletekModal = window.$('div#delete-eligibility-modal'),
            $deleteForm  = $deletekModal.find('form#delete-eligibility-form'),
            deleteAction = $deleteForm.attr('action');

        window.$('a[href="#delete-eligibility-modal"]').on('click', function (event) {
            event.preventDefault();

            $deleteForm.attr('action', deleteAction.replace(':id', window.$(this).attr('data-eligibility-id')));

            $deletekModal.modal('show');
        });

        $deletekModal.on('hidden.bs.modal', function () {
            $deleteForm.attr('action', deleteAction);
        });
    });
    </script>
{% endblock %}