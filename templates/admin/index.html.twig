{% extends '_layout/base.html.twig' %}

{% block title %}Mymarque BO V2{% endblock %}

{% block basebody %}
	<style>
		.form {
			margin: 1em auto;
			max-width: 330px;
			width: 100%;
			font: 18px/1.5 sans-serif;
		}

		.form code {
			background: #F5F5F5;
			padding: 2px 6px;
		}
	</style>

	<div class="d-flex w-100 h-100 p-3 mx-auto flex-column align-items-center bg-gradient-primary login-{{ app.environment }}">
        <form id="select-profile-form" action="{{ path('admin_profile_select')}} " method="POST">
            {% set providedGroups = sites_data_provider.provideGroups %}
            <div class="card" style="max-width: 30rem; min-width: 20rem; margin-top: 25vh;">
                <div class="card-header">Site</div>
                <div class="card-body">
                    <label class="font-weight-bold">{% trans %}choose_brand_title{% endtrans %}:</label>
                    <select name="brand_filter" class="form-control bg-light border-1 mb-3">
                        <option class="text-sm text-secondary" value="" selected>{% trans %}choose_brand{% endtrans %}</option>
                        {% for brand, brandLabel in providedGroups.brands %}
                            <option value="{{ brand }}" class="text-sm font-weight-bold text-secondary mb-1">
                                {{ brandLabel }}
                            </option>
                        {% endfor %}
                    </select>
                    <label class="font-weight-bold">{% trans %}choose_country_title{% endtrans %}:</label>
                    <select name="country_filter" class="form-control bg-light border-1 mb-3">
                        <option class="text-sm text-secondary" value="" selected>{% trans %}choose_country{% endtrans %}</option>
                        {% for country, countryLabel in providedGroups.countries %}
                            <option value="{{ country }}" class="text-sm font-weight-bold text-secondary mb-1 countries hide countries-all">
                                {{ countryLabel }}
                            </option>
                        {% endfor %}
                        
                        {% for code, brand in providedGroups.brandCountries %}
                            {% for country in brand %}
                                <option value="{{ country }}" class="text-sm font-weight-bold text-secondary mb-1 countries hide brand-countries-{{ code }} countries-selected-{{country}}">
                                    {{ providedGroups.countries[country] }}
                                </option>
                            {% endfor %}
                        {% endfor %}
                    </select>
                    <label class="font-weight-bold">{% trans %}choose_profile_title{% endtrans %}:</label>
                    <select name="profile" class="form-control bg-light border-1">
                        <option class="text-sm text-secondary" value="" disabled selected>{% trans %}choose_profile{% endtrans %}</option>
                        {% for site in providedGroups.sites %}
                            <optgroup label="{{ site.site_label }}" class="text-sm font-weight-bold text-primary profiles-all text-capitalize mb-1 groups-filter">
                                {% for profile in site.profiles %}
                                    <option 
                                        value="{{ profile.id }}"
                                        class="text-xs font-weight-bold text-secondary mb-1 brand-{{ profile.brand }} country-{{ profile.country }}">
                                        {{ profile.label }}
                                    </option>
                                {% endfor %}
                            </optgroup>
                        {% endfor %}
                    </select>
                </div>
                <div class="card-body">
                    <div class="form-check form-check-inline">
                        <label class="small font-weight-bold">
                            Language :
                        </label>
                    </div>
                    <div class="custom-radio small form-check form-check-inline">
                        <input id="radiofr" name="selected_language" value="fr" checked
                               type="radio" class="custom-control-input form-check-input">
                        <label class="custom-control-label form-check-label" for="radiofr">Français</label>
                    </div>
                    <div class="custom-radio small form-check form-check-inline">
                        <input id="radioen" name="selected_language" value="en"
                            type="radio" class="custom-control-input form-check-input">
                        <label class="custom-control-label form-check-label" for="radioen">English</label>
                    </div>
                </div>
            </div>
        </form>
	</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        var brandF = jQuery('select[name="brand_filter"]').val();
        function filter(brand, country) {
        jQuery('.groups-filter').hide();
            if (brand && !country) {
                brandF = brand;
                jQuery('.countries').addClass('hide');
                jQuery('.brand-countries-' + brand).removeClass('hide');
                jQuery('.brand-' + brand).parent().show();
                //jQuery('.profiles-all').addClass('hide').hide().attr('style', '');
				//return true;
            } else if (!brand && country) {
                if(brandF != brand){
                    jQuery('.countries').addClass('hide');
                    jQuery('select[name="country_filter"]').val('');
                    jQuery('select[name="profile"]').val('');
                    jQuery('.countries-all').addClass('hide');
                    jQuery('.profiles-all').addClass('hide').hide().attr('style', '');
                    brandF = brand;
                }else{
                    jQuery('.country-' + country).parent().show();
                }
            } else if (brand && country) {
                if(brandF != brand){
                    jQuery('select[name="country_filter"]').val('').change();
					//jQuery('.profiles-all').addClass('hide').hide().attr('style', '');
                    brandF = brand;
					return true;
                }
                jQuery('.brand-countries-' + brand).removeClass('hide');
                jQuery('.brand-' + brand+'.country-'+country).parent().show()
            } else {
                jQuery('.countries').addClass('hide');
            }

            jQuery('.brand-XX, .brand-CT').parent().show()
        }
        jQuery('select[name="brand_filter"], select[name="country_filter"').change(function(e) {
            filter(jQuery('select[name="brand_filter"]').val(), jQuery('select[name="country_filter"]').val())
        });
        document.querySelector('select[name="profile"]').addEventListener('change', () => {
            document.getElementById('select-profile-form').submit();
        });
        jQuery('.profiles-all').addClass('hide').hide().attr('style', '');
        jQuery('.brand-XX, .brand-CT').parent().show()
    </script>
{% endblock %}
