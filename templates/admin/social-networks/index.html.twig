{% set profile = app.request.attributes.get('profile') %}
{% extends '_layout/base_back.html.twig' %}

{% block title %}Mymarque BO V2{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Gestion des Réseaux Sociaux{% endtrans %}</h1>

    {% include "_layout/notifications.html.twig" %}

    <div class="card shadow mb-3">
        <div class="card-body">
            {{ form_start(social_network_form) }}
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        {{ form_widget(social_network_form.type) }}
                    </div>

                    {{ form_widget(social_network_form.url) }}

                    <div class="input-group-append">
                        {{ form_widget(social_network_form.submit) }}
                    </div>
                </div>

                <ul class="list-unstyled text-danger">
                    {% for error in social_network_form.vars.errors.form.getErrors(true) %}
                        <li><b>[{{ error.origin.name }}]:</b> {{ error.message }}</li>
                    {% endfor %}
                </ul>
            {{ form_end(social_network_form) }}
        </div>
        <div class="card-body">
            <table id="social-networks-table" class="table table-bordered table-hover">
                <thead>
                    <tr class="text-primary">
                        <th></th>
                        <th>Order</th>
                        <th class="th-sm">Type</th>
                        <th>Url</th>
                        <th class="text-center">Status</th>
                        <th class="text-right">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% if socialNetworks is not empty %}
                        {% for socialNetwork in socialNetworks %}
                            <tr data-id="{{ socialNetwork.id }}">
                                <td class="text-center text-primary"><i class="fas fa-arrows-alt"></i></td>
                                <td class="order">{{ loop.index }}</td>
                                <td>{{ socialNetwork.type|upper }}</td>
                                <td>{{ socialNetwork.url }}</td>
                                <td class="text-center">
                                    {% if socialNetwork.isEnabled %}
                                        <span class="badge badge-success">{% trans %}Activé{% endtrans %}</span>
                                    {% else %}
                                        <span class="badge badge-secondary">{% trans %}Désactivé{% endtrans %}</span>
                                    {% endif %}
                                </td>
                                <td class="text-right">
                                    <a href="{{ path('admin_social_edit', {profile: profile.id, id: socialNetwork.id}) }}" class="btn btn-sm btn-warning"
                                       data-toggle="tooltip" data-title="">{% trans %}Modifier{% endtrans %}</a>
                                    {% if socialNetwork.isEnabled %}
                                        <a href="#activate-social-network-modal" class="btn btn-sm btn-dark btn-custom"
                                           data-social-network-id="{{ socialNetwork.id }}"
                                           data-social-network-status="enabled"
                                           data-toggle="tooltip" data-title="">{% trans %}Désactiver{% endtrans %}</a>
                                    {% else %}
                                        <a href="#activate-social-network-modal" class="btn btn-sm btn-success btn-custom"
                                           data-social-network-id="{{ socialNetwork.id }}"
                                           data-social-network-status="disabled"
                                           data-toggle="tooltip" data-title="">{% trans %}Activer{% endtrans %}</a>
                                    {% endif %}
                                    <a href="#delete-social-network-modal" class="btn btn-sm btn-danger"
                                       data-social-network-id="{{ socialNetwork.id }}"
                                       data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                                </td>
                            </tr>
                        {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">
                                    {% trans %}La liste des réseaux sociaux est vide{% endtrans %}!
                                </td>
                            </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- ENABLE/DISABLE SOCIAL NETWORK -->
    <div class="modal fade" id="activate-social-network-modal" tabindex="-1" role="dialog" aria-labelledby="activate" aria-hidden="true">
        <form action="{{ path('admin_social_activate', {profile: profile.id, id: ':id'}) }}" id="activate-social-network-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('activate-social-network') }}"/>
            <input type="hidden" name="_method" value="PUT">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activate"></h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body"></div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-success" type="submit">{% trans %}Activer{% endtrans %}</button>
                        <button class="btn btn-dark" type="submit">{% trans %}Désactiver{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- DELETE SOCIAL NETWORK -->
    <div class="modal fade" id="delete-social-network-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('admin_social_delete', {profile: profile.id, id: ':id'}) }}" id="delete-social-network-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-social-network') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer ce réseau social{% endtrans %} ?</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script src="{{ asset('js/sortable.min.js') }}"></script>
    <script src="{{ asset('js/axios.min.js') }}"></script>

    <script>
        window.$(function () {

            {% if socialNetworks is not empty %}
            var sortable = Sortable.create(document.querySelector('table#social-networks-table tbody'), {
                onEnd: function () {
                    sortable.option("disabled", true);

                    axios({
                        method: 'put',
                        url: "{{ path('admin_social_reorder', {profile: profile.id}) }}",
                        data: {
                            ids: sortable.toArray()
                        },
                    }).then(function () {
                        document.querySelectorAll('table#social-networks-table td.order').forEach(function (node, i) {
                            node.innerHTML = i + 1;
                        });
                    }).catch(function () {
                        alert('Problème de chargement !');
                    }).finally(function () {
                        sortable.option("disabled", false);
                    });
                },
            });
            {% endif %}
        })
    </script>

    <!-- ENABLE/DISABLE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            var $activateSocialNetworkModal = window.$('div#activate-social-network-modal'),
                $activateSocialNetworkForm  = $activateSocialNetworkModal.find('form#activate-social-network-form'),
                activateSocialNetworkAction = $activateSocialNetworkForm.attr('action');

            window.$('a[href="#activate-social-network-modal"]').on('click', function (event) {
                event.preventDefault();

                $activateSocialNetworkForm.attr('action', activateSocialNetworkAction.replace(':id', window.$(this).attr('data-social-network-id')));

                $activateSocialNetworkModal.modal('show');

                var status = window.$(this).attr('data-social-network-status');

                $activateSocialNetworkForm.find('button[type="submit"].btn-success').hide();
                $activateSocialNetworkForm.find('button[type="submit"].btn-dark').hide();

                if (status === 'enabled') {
                    $activateSocialNetworkModal.find('.modal-title').html('Confirmation');
                    $activateSocialNetworkModal.find('.modal-body').html('{% trans %}Etes-vous sûr(e) de vouloir désactiver ce réseau social{% endtrans %} ?');
                    $activateSocialNetworkForm.find('button[type="submit"].btn-dark').show();
                }
                else {
                    $activateSocialNetworkModal.find('.modal-title').html('Confirmation');
                    $activateSocialNetworkModal.find('.modal-body').html('{% trans %}Etes-vous sûr(e) de vouloir activer ce réseau social{% endtrans %} ?');
                    $activateSocialNetworkForm.find('button[type="submit"].btn-success').show();
                }
            });

            $activateSocialNetworkModal.on('hidden.bs.modal', function () {
                $activateSocialNetworkForm.attr('action', activateSocialNetworkAction);
            });
        });
    </script>

    <!-- DELETE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            var $deleteSocialNetworkModal = window.$('div#delete-social-network-modal'),
                $deleteSocialNetworkForm  = $deleteSocialNetworkModal.find('form#delete-social-network-form'),
                deleteSocialNetworkAction = $deleteSocialNetworkForm.attr('action');

            window.$('a[href="#delete-social-network-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteSocialNetworkForm.attr('action', deleteSocialNetworkAction.replace(':id', window.$(this).attr('data-social-network-id')));

                $deleteSocialNetworkModal.modal('show');
            });

            $deleteSocialNetworkModal.on('hidden.bs.modal', function () {
                $deleteSocialNetworkForm.attr('action', deleteSocialNetworkAction);
            });
        });
    </script>
{% endblock %}
