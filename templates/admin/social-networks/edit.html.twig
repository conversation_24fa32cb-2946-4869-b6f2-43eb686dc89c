{% set profile = app.request.attributes.get('profile') %}
{% set socialNetworks = profile.site.socialNetworks %}
{% extends '_layout/base_back.html.twig' %}

{% block title %}Mymarque BO V2{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Modification des Réseaux Sociaux{% endtrans %}</h1>

    <div class="card">
        <div class="card-body shadow-sm">
            {{ form(social_network_form) }}
            <a class="mr-2 btn btn-dark float-right" role="button" href="{{ path('admin_social_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
        </div>
    </div>
{% endblock %}
