{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %} Services {% endtrans %}</h1>
    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%} active {% endif %}" id="wsparameters-app">
                        <div id="service-connecte-app">
                            {{ form_start(formApp, {'attr': { 'name':'myCustomFormName' }}) }}
                            <div class="card">
                                <!-- BOUTIQUE -->
                                <a href="#sams_boutique_enabled" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_language">
                                    <h6 class="m-0 font-weight-bold text-primary">{% trans %} BOUTIQUE {% endtrans %}</h6>
                                </a>
                                <!-- Card Content - Collapse -->
                                <div class="collapse show" id="sams_boutique_enabled" style="">
                                    <div class="card-body">
                                        <div class="row mb-4">
                                            <div class="col-md-3">{% trans %} SAMS_BOUTIQUE {% endtrans %}</div>
                                            <div class="col-md-7 ">
                                                {{ form_widget(formApp['SAMS_BOUTIQUE'], {label_attr: {class: 'checkbox-custom '} }) }}

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <!-- Service connectes -->
                                <a href="#services_connected" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_language">
                                    <h6 class="m-0 font-weight-bold text-primary">{% trans %} Connected_Services {% endtrans %}</h6>
                                </a>
                                <!-- Card Content - Collapse -->
                                <div class="collapse show" id="services_connected" style="">
                                    <div class="card-body">
                                        {% for service in services %}
                                          {% if service == "SAMS_FULLDIGITAL"%}
                                            <div id="full_digital_section_app">
                                          {% endif %}
                                            <div class="mb-3">
                                                <h5 class="text-gray-900 font-weight-bold">{{ service | trans }}</h5>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-3">Activation</div>
                                                <div class="col-md-7 ">
                                                    {{ form_widget(formApp[service], {label_attr: {class: 'checkbox-custom '},
                                                        attr: {class: 'adopter_disable', 'data-disable': service~'_adopters_enabled_APP'    } }) }}
                                                </div>
                                            </div>
                                            {%if formApp[service~'_in_webview'] is defined %}
                                                <div class="row mb-4" id="webview-config">
                                                    <div class="col-md-3">{% trans %} Sams_Webview {% endtrans %}</div>
                                                    <div class="col-md-7 ">
                                                        {{ form_widget(formApp[service~'_in_webview'],  {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_in_webview_APP'}})  }}
                                                    </div>
                                                </div>
                                                {% if (service != 'SAMS_LEV') %}
                                                    <div class="row mb-1">
                                                        <div class="col-md-10">
                                                            <hr style="border-top: 1px solid #8c8b8b;">
                                                        </div>
                                                    </div>
                                                {%endif%}
                                            {%endif%}
                                            {% if formApp[service~'_adopters_enabled'] is defined  %}
                                                <div class="row mb-4" id="adopters-config">
                                                    <div class="col-md-3">{% trans %} Early_Adopters {% endtrans %}</div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp[service~'_adopters_enabled'],
                                                            {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_adopters_enabled_APP'}}) }}
                                                        {{ form_widget(formApp[service~'_adopters_text']) }}
                                                    </div>
                                                </div>
                                            {% endif %}
                                            {% if formApp[service~'_demo_enabled'] is defined  %}
                                                <div class="row mb-4" id="demo-config">
                                                    <div class="col-md-3">{% trans %} Mode demo {% endtrans %}</div>
                                                    <div class="col-md-7">
                                                        {{ form_widget(formApp[service~'_demo_enabled'],
                                                            {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_adopters_enabled_APP'}}) }}
                                                        {{ form_widget(formApp[service~'_demo_text']) }}
                                                    </div>
                                                </div>
                                            {% endif %}

                                            {% if formApp[service~'_android_url'] is defined %}
                                                <div class="row mb-4">
                                                    <div class="col-md-3">{% trans %} android_url {% endtrans %}</div>
                                                    <div class="col-md-4">
                                                        {{ form_widget(formApp[service~'_android_url'], {attr: {class: 'androidAPP_url'}}) }}
                                                    </div>
                                                    <div class="col-md-12 form-error">
                                                        {{ form_errors(formApp[service~'_android_url']) }}
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-3">{% trans %} ios_url {% endtrans %}</div>
                                                    <div class="col-md-4">
                                                        {{ form_widget(formApp[service~'_ios_url'], {attr: {class: 'iosAPP_url'}}) }}
                                                    </div>
                                                    <div class="col-md-12 form-error">
                                                        {{ form_errors(formApp[service~'_ios_url']) }}
                                                    </div>
                                                </div>
                                            {% endif %}

                                             {% if formApp[service~'_phone'] is defined %}
                                                <div class="row mb-4">
                                                    <div class="col-md-3">{% trans %} phone {% endtrans %}</div>
                                                    <div class="col-md-4">
                                                        {{ form_widget(formApp[service~'_phone']) }}
                                                    </div>
                                                    <div class="col-md-12 form-error">
                                                        {{ form_errors(formApp[service~'_phone']) }}
                                                    </div>
                                                </div>

                                            {% endif %}
                                             {% if service == "SAMS_FULLDIGITAL"%}
                                                </div>
                                             {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('service_connecte_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success form_submitor" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formApp) }}
                        </div>
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                        <div id="service-connecte-web">
                            {{ form_start(formWeb, {'attr': { 'name':'myCustomFormName' }}) }}
                            <!-- Service connectes -->
                            <div class="card">
                            <a href="#services_connected" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="site_language">
                                <h6 class="m-0 font-weight-bold text-primary">{% trans %} Connected_Services {% endtrans %}</h6>
                            </a>
                            <!-- Card Content - Collapse -->
                            <div class="collapse show" id="services_connected" style="">
                                <div class="card-body">
                            {% for service in services %}

                                {% if service == "SAMS_FULLDIGITAL"%}
                                            <div id="full_digital_section_web">
                                {% endif %}
                                <div class="mb-3">
                                    <h5 class="text-gray-900 font-weight-bold">{{ service | trans }}</h5>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-md-3">Activation</div>
                                    <div class="col-md-7 ">
                                        {{ form_widget(formWeb[service], {label_attr: {class: 'checkbox-custom '},
                                            attr: {class: 'adopter_disable', 'data-disable': service~'_adopters_enabled_WEB'} }) }}
                                    </div>
                                </div>
                                {%if formWeb[service~'_adopters_enabled'] is defined  %}
                                    <div class="row mb-4" id="adopters-config">
                                        <div class="col-md-3"> {% trans %} Early_Adopters {% endtrans %}</div>
                                        <div class="col-md-7">
                                            {{ form_widget(formWeb[service~'_adopters_enabled'],
                                                {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_adopters_enabled_WEB'}}) }}
                                            {{ form_widget(formWeb[service~'_adopters_text']) }}
                                        </div>
                                    </div>
                                {%endif%}
                                {%if formWeb[service~'_demo_enabled'] is defined  %}
                                    <div class="row mb-4" id="demo-config">
                                        <div class="col-md-3"> {% trans %} Mode demo {% endtrans %}</div>
                                        <div class="col-md-7">
                                            {{ form_widget(formWeb[service~'_demo_enabled'],
                                                {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_demo_enabled_WEB'}}) }}
                                            {{ form_widget(formWeb[service~'_demo_text']) }}
                                        </div>
                                    </div>
                                {%endif%}
                                {%if formWeb[service~'_in_webview'] is defined  %}
                                <div class="col-md-7 " style="display: none">
                                    {{ form_widget(formWeb[service~'_in_webview'],  {label_attr: {class: 'checkbox-custom'}, attr: {class: service~'_in_webview'}})  }}
                                    {{ form_widget(formWeb['SAMS_BOUTIQUE'], {label_attr: {class: 'checkbox-custom '} }) }}
                                </div>{%endif%}
                                {% if service in ['SAMS_NAVCOZAR','SAMS_TMTS'] %}
                                    <div class="row mb-1">
                                        <div class="col-md-10">
                                            <hr style="border-top: 1px solid #8c8b8b;">
                                        </div>
                                    </div>
                                {%endif%}
                                {% if formWeb[service~'_android_url'] is defined %}
                                    <div class="row mb-4">
                                        <div class="col-md-3">{% trans %}
                                            android_url
                                            {% endtrans %}</div>
                                        <div class="col-md-4">
                                            {{ form_widget(formWeb[service~'_android_url'], {attr: {class: 'androidAPP_url'}}) }}
                                        </div>
                                        <div class="col-md-12 form-error">
                                            {{ form_errors(formWeb[service~'_android_url']) }}
                                        </div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class="col-md-3">{% trans %}
                                            ios_url
                                            {% endtrans %}</div>
                                        <div class="col-md-4">
                                            {{ form_widget(formWeb[service~'_ios_url'], {attr: {class: 'iosAPP_url'}}) }}
                                        </div>
                                        <div class="col-md-12 form-error">
                                            {{ form_errors(formWeb[service~'_ios_url']) }}
                                        </div>
                                    </div>
                                {% endif %}

                                {% if formApp[service~'_phone'] is defined %}
                                               <div class="row mb-4">
                                                    <div class="col-md-3">{% trans %} phone {% endtrans %}</div>
                                                    <div class="col-md-4">
                                                        {{ form_widget(formWeb[service~'_phone']) }}
                                                    </div>
                                                    <div class="col-md-12 form-error">
                                                        {{ form_errors(formWeb[service~'_phone']) }}
                                                    </div>
                                                </div>

                                {% endif %}
                                {% if service == "SAMS_FULLDIGITAL"%}
                                            </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                        </div>
                    <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('service_connecte_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success form_submitor" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formWeb) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $(".form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
            $(".adopter_disable").on('change', function() {
                $('.' + $(this).data('disable')).attr('disabled', !this.checked);
            });
            $(".adopter_disable").trigger('change');
            $(".demo_disable").on('change', function() {
                $('.' + $(this).data('disable')).attr('disabled', !this.checked);
            });
            $(".demo_disable").trigger('change');
        });
        var checkadoptersApp = function() {
            if (document.getElementById('services_APP_SAMS_LEV_adopters_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_LEV_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_LEV_adopters_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_APP_SAMS_RACCESS_adopters_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_RACCESS_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_RACCESS_adopters_text').style.visibility = 'hidden';
            }
            
            if (document.getElementById('services_APP_SAMS_DIGITALKEY_adopters_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_DIGITALKEY_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_DIGITALKEY_adopters_text').style.visibility = 'hidden';
            }
        }
        checkadoptersApp();
        $('#adopters-config input').on('change', function() {
            checkadoptersApp();
        });
        var checkadoptersWeb = function() {
            if (document.getElementById('services_WEB_SAMS_LEV_adopters_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_LEV_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_LEV_adopters_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_WEB_SAMS_RACCESS_adopters_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_RACCESS_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_RACCESS_adopters_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_WEB_SAMS_DIGITALKEY_adopters_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_DIGITALKEY_adopters_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_DIGITALKEY_adopters_text').style.visibility = 'hidden';
            }
        }
        checkadoptersWeb();
        $('#adopters-config input').on('change', function() {
            checkadoptersWeb();
        });

        var checkDemoApp = function() {
            if (document.getElementById('services_APP_SAMS_LEV_demo_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_LEV_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_LEV_demo_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_APP_SAMS_RACCESS_demo_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_RACCESS_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_RACCESS_demo_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_APP_SAMS_DIGITALKEY_demo_enabled').checked  ) {
                document.getElementById('services_APP_SAMS_DIGITALKEY_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_APP_SAMS_DIGITALKEY_demo_text').style.visibility = 'hidden';
            }
        }
        checkDemoApp();
        $('#demo-config input').on('change', function() {
            checkDemoApp();
        });
        var checkDemoWeb = function() {
            if (document.getElementById('services_WEB_SAMS_LEV_demo_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_LEV_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_LEV_demo_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_WEB_SAMS_RACCESS_demo_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_RACCESS_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_RACCESS_demo_text').style.visibility = 'hidden';
            }

            if (document.getElementById('services_WEB_SAMS_DIGITALKEY_demo_enabled').checked  ) {
                document.getElementById('services_WEB_SAMS_DIGITALKEY_demo_text').style.visibility = 'visible';
            } else {
                document.getElementById('services_WEB_SAMS_DIGITALKEY_demo_text').style.visibility = 'hidden';
            }
        }
        checkDemoWeb();
        $('#demo-config input').on('change', function() {
            checkDemoWeb();
        });



        $("#services_APP_SAMS_TMTS").on('change', function() {
            
            if (document.getElementById('services_APP_SAMS_TMTS').checked  ) {
                document.getElementById('full_digital_section_app').style.display = 'block';
            } else {
                document.getElementById('full_digital_section_app').style.display = 'none';
            }
        });

         $("#services_WEB_SAMS_TMTS").on('change', function() {
            if (document.getElementById('services_WEB_SAMS_TMTS').checked  ) {
                document.getElementById('full_digital_section_web').style.display = 'block';
            } else {
                document.getElementById('full_digital_section_web').style.display = 'none';
            }
        });




         $("#services_WEB_SAMS_TMTS").trigger('change');
         $("#services_APP_SAMS_TMTS").trigger('change');

    </script>
{% endblock %}