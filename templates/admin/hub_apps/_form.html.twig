{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
<div class="card shadow-sm">
{% if isAdmin == false %}

<div class="card-body">

<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Online' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.online) }}
					</span>
					{{ form_widget(form.online) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Name' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.name) }}
					</span>
					{{ form_widget(form.hubApp.name) }}
				</div>
			</div>
		</div>
		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'OS' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.osName) }}
					</span>
					{{ form_widget(form.hubApp.osName) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'ID' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.appId) }}
					</span>
					{{ form_widget(form.hubApp.appId) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Icon' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.icon) }}
					</span>
					{{ form_widget(form.hubApp.icon) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Url' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.url) }}
					</span>
					{{ form_widget(form.hubApp.url) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Store ID' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.hubApp.storeId) }}
					</span>
					{{ form_widget(form.hubApp.storeId) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			{% for descriptionRef in form.hubApp.descriptionRefs  %}
					<div class="row">
						<div class="col-md-3"> {{ form_label(descriptionRef.description) }}</div>
						<div class="col-md-7">
							{{ form_widget(descriptionRef.description) }}
						</div>
					</div>
			{% endfor %}
		</div>
		<div class="form-group">
			{% set languagesLength = form.hubAppSiteTranslations.count %} 
			{% for formL in form.hubAppSiteTranslations  %}
				{% if languagesLength == 1 %}
					<div class="row">
						<div class="col-md-3">{{ form_label(formL.name) }}</div>
						<div class="col-md-7">
							{{ form_widget(formL.name) }}
						</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-3">{{ form_label(formL.description) }}</div>
						<div class="col-md-7">
							{{ form_widget(formL.description) }}
						</div>
					</div>
				{% elseif languagesLength > 1 %}
					{% set language = formL.vars.value.language %} 
					<div class="card">
						<div class="card-header" id="{{'heading-' ~ language.code }}" style="background-color: lemonchiffon;">
							<h6 class="mb-0">
								<a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse" href="#section-{{ language.code }}" role="button" aria-expanded="false" aria-controls="section-{{ language.code }}">
									<img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
									{{ language.label }}
								</a>
							</h6>
						</div>
						<div id="{{ 'section-' ~ language.code }}" class="collapse" aria-labelledby="{{'heading-' ~ language.code }}">
							<div class="card-body" id="form_container" data-language="{{language.code}}">
								<div class="row">
									<div class="col-md-3">{{ form_label(formL.name) }}</div>
									<div class="col-md-7">
										{{ form_widget(formL.name) }}
									</div>
								</div>
								<div class="row mt-2">
									<div class="col-md-3">{{ form_label(formL.description) }}</div>
									<div class="col-md-7">
										{{ form_widget(formL.description) }}
									</div>
								</div>
							</div>
						</div>
					</div>
				{% endif %}

			{% endfor %}
		</div>

	</div>
	<div class="card-footer text-right">
		<a class="mr-1 btn btn-dark" role="button" href="{{ path('hub_app_configuration_index',{'profile': profile.id, 'brand': brand}) }}">{% trans %}Annuler{% endtrans %}</a>
		{% if update == true and isAdmin == true %}
			<a href="#delete-hub-app-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-hub-app-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
		{% endif %}
		<button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
	</div>

	{% else %}

	<div class="card-body">
		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'OS' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.osName) }}
					</span>
					{{ form_widget(form.osName) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'ID' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.appId) }}
					</span>
					{{ form_widget(form.appId) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Icon' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.icon) }}
					</span>
					{{ form_widget(form.icon) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Name' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.name) }}
					</span>
					{{ form_widget(form.name) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Url' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.url) }}
					</span>
					{{ form_widget(form.url) }}
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="row">
				<div class="col-md-3">
					{{ 'Store ID' | trans  }}

				</div>
				<div class="col-md-5">
					<span class="text-danger">
						{{ form_errors(form.storeId) }}
					</span>
					{{ form_widget(form.storeId) }}
				</div>
			</div>
		</div>

		<div class="form-group">

			{% for descriptionRef in form.descriptionRefs  %}
					<div class="row">
						<div class="col-md-3">{{ form_label(descriptionRef.description) }}</div>
						<div class="col-md-7">
							{{ form_widget(descriptionRef.description) }}
						</div>
					</div>
				
			{% endfor %}
		</div>

	</div>
	<div class="card-footer text-right">
		<a class="mr-1 btn btn-dark" role="button" href="{{ path('hub_app_configuration_index',{'profile': profile.id, 'brand': brand}) }}">{% trans %}Annuler{% endtrans %}</a>
		{% if update == true and isAdmin == true %}
			<a href="#delete-hub-app-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-hub-app-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
		{% endif %}
		<button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
	</div>

    {% endif %}
	
</div>
{{ form_end(form) }}
{% block modals %}
	{% if (update == true and isAdmin == true) %}
		<div class="modal fade" id="delete-hub-app-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
			{% set id = form.vars.value.id %}
			<form action="{{ path('hub_app_delete', {profile: profile.id, hubApp: id, brand: brand}) }}" id="delete-hub-app-form" method="POST">
				<input type="hidden" name="token" value="{{ csrf_token('delete-hub-app') }}"/>
				<input type="hidden" name="_method" value="DELETE">

				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5 class="modal-title" id="delete">Confirmation</h5>
							<button class="close" type="button" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true">×</span>
							</button>
						</div>
						<div class="modal-body">{% trans %}hub_app.delete{% endtrans %}</div>
						<div class="modal-footer">
							<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
							<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	{% endif %}


{% endblock %}
