{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Liste des traductions locales{% endtrans %}</h1>

    <!-- Logs Import -->
    {% set flashes = app.flashes(['success', 'warning', 'danger']) %}
    {% if (flashes.success is not empty or flashes.warning is not empty or flashes.danger is not empty) %}
        {% for label, messages in flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label }}" role="alert">{{ message }}</div>
            {% endfor %}
        {% endfor %}
    {% endif %}

    <div class="row">
        <!-- Export -->
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Export</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                    {{ form_start(form_export, {'action': path('local_translation_export', {profile: profile.id}), 'method': 'POST' }) }}
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="language">Languages</label>
                                    <select class="form-control" id="language" name="language">
                                        {% for language in languages %}
                                            <option value="{{ language.code }}">{{ language.label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="source">Sources</label>
                                    <select class="form-control" id="source" name="source">
                                        <option value="APP">APP</option>
                                        <option value="WEB">WEB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    {{ form_end(form_export) }}
                </div>
            </div>
        </div>

        <!-- Import -->
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Import</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                {{ form_start(form_import, {'action': path('local_translation_index', {profile: profile.id}), 'method': 'POST' }) }}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>{% trans %}Fichier{% endtrans %}</label>
                                {{ form_widget(form_import.file) }}
                                <ul class="list-unstyled text-danger">
                                    {% for error in form_import.vars.errors.form.getErrors(true) %}
                                        <li>{% trans %}file_not_allowed{% endtrans %}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="language">Languages</label>
                                <select class="form-control" id="language" name="language">
                                    {% for language in languages %}
                                        <option value="{{ language.code }}">{{ language.label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="source">Sources</label>
                                <select class="form-control" id="source" name="source">
                                    <option value="APP">APP</option>
                                    <option value="WEB">WEB</option>
                                </select>
                            </div>
                        </div>
                    </div>
                {{ form_end(form_import) }}
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm my-4">
        <div class="card-body">
            <!-- Filters -->
            {{ form_start(form) }}
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.functionalityName) }}
                            {{ form_widget(form.functionalityName) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.sprint) }}
                            {{ form_widget(form.sprint) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.traduction) }}
                            {{ form_widget(form.traduction) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.keyLabel) }}
                            {{ form_widget(form.keyLabel) }}
                        </div>
                    </div>
                </div>
            {{ form_end(form) }}

            <!-- Table -->
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Status{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Date de création{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Date de maj{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Sprint{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Fonctionnalité{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Device{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Clé de libellé{% endtrans %}</small></th>
                        <th class="text-primary">
                            <small class="font-weight-bold">
                               {% if prefered_language == "fr" %}
                                    {% trans %}Libellé référent{% endtrans %} - FR
                                {% else %}
                                    {% trans %}Libellé référent{% endtrans %} - EN
                                {% endif %}
                            </small>
                        </th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Traduction pays{% endtrans %}</small></th>
                        <th class="text-primary text-right"><small class="font-weight-bold">{% trans %}Actions{% endtrans %}</small></th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            const keyLabel = document.querySelector('#local_translation_filterer_keyLabel');
            const functionalityName = document.querySelector('#local_translation_filterer_functionalityName');
            const traduction = document.querySelector('#local_translation_filterer_traduction');
            const sprint = document.querySelector('#local_translation_filterer_sprint');
            let table = $('#dataTable').DataTable({
                    "processing": true,
                    "serverSide": true, 
                    "ajax": {
                        "url": "{{ path('local_translation_fo_paginate', {'profile': profile.id}) }}",
                        "data": function ( d ) {
                            d.functionalityName = functionalityName.value;
                            d.sprint = sprint.value;
                            d.traduction = traduction.value;
                            d.keyLabel = keyLabel.value;
                        }
                    },
                    "sAjaxDataProp": "data", 
                    "pageLength": 25, 
                    "paging" : true,
                    "info" : true,
                    "searching": false,
                    "responsive": true, 
                    stateSave: true,
                    columns: [
                        { data: 'bgcTranslationStatus'},
                        { data: 'creationDate' },
                        { data: 'lastUpdate' },
                        { data: 'sprint' },
                        { data: 'functionalityName' },
                        { data: 'support' },
                        { data: 'keyLabel' },
                        { data: 'libRef' },
                        { data: 'tradPays' },
                        { data: function ( row, type ) {
                            if ( type === 'display' ) {
                                myRender = '';
                                myRender += '<a href="{{ path('local_translation_edit', {'profile': profile.id, 'id': 'row.id'}) }}" class="btn btn-sm btn-warning"><small>{% trans %}Modifier{% endtrans %}</small></a>';
                                myRender = myRender.replace("row.id", row.id);
                                return myRender ;
                            }

                            return '' ;

                        }},
                    ],
                    "rowCallback": function( row, data ) {
                        $('td:eq(0)', row).html(data.translationStatusText);
                        $('td:eq(0)', row).attr('style','background-color:'+data.bgcTranslationStatus);
                        $('td:eq(0)', row).attr('title',data.translationStatusText);
                        $('td:eq(0)', row).addClass('p-0');
                    },
                    render: function(data){
                        if(data){
                            return (data.length > 200)?data.substring(0, 200)+'...':data;
                        } else {
                            return '';
                        }
                    },
                    'order': [[ 1, "desc" ]],
                    'autoWidth': false,
                    'columnDefs': [
                        {'orderable': false, targets: [0, 9]},
                        { width: "0.5%", targets: 0 },
                        { width: "7%", targets: 1 },
                        { width: "7%", targets: 2 },
                        { width: "7%", targets: 3 },
                        { width: "5%", targets: 4 },
                        { width: "5%", targets: 5 },
                        { width: "10%", targets: 6 },
                        { width: "30%", targets: 7 },
                        { width: "30%", targets: 8 },
                        { width: "5.5%", targets: 9 }
                    ]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text);
                    }
                });   
            })
            $(".truncate").each(function(){
                if($(this).text().trim().length > 100){
                    let text = $(this).text().trim().substring(0 , 100) + '...';
                    $(this).html(text);
                }
            });  
            keyLabel.addEventListener('input', function () {
                table.draw();
            });
            functionalityName.addEventListener('input', function () {
                table.draw();
            });
            traduction.addEventListener('input', function () {
                table.draw();
            });
            sprint.addEventListener('input', function () {
                table.draw();
            });
            $('body').on('click', '#locale_translation_export_submit', function(event) {
                event.preventDefault();
                var url = '{{ path('local_translation_export', {'profile': profile.id}) }}';
                const source = document.querySelector('#source');
                const language = document.querySelector('#language');
                $.ajax({
                    type: 'POST',
                    url: url,
                    dataType: 'json',
                    data: {
                        keyLabel: keyLabel.value,
                        functionalityName: functionalityName.value,
                        traduction: traduction.value,
                        sprint: sprint.value,
                        source: source.value,
                        language: language.value
                        },
                    success: function(response) {
                        window.location.href = url;
                    }
                });
            });
        }); 
    </script>
    <script>
        $('.custom-file-input').on('change', function(event) {
            var inputFile = event.currentTarget;
            $(inputFile).parent()
                .find('.custom-file-label')
                .html(inputFile.files[0].name);
        });
    </script>
{% endblock %}
