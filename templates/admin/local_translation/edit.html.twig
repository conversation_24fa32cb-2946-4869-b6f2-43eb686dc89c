{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Modification : Traduction locale{% endtrans %}</h1>
    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Clé{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" type="text" value="{{ fo_label.keyLabel}}" disabled="disabled" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Sprint de création du libellé{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" type="text" value="{{ fo_label.sprintNumber}}" disabled="disabled" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Nom de la fonctionnalité associée au libellé{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" type="text" value="{{ fo_label.functionalityName}}" disabled="disabled" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Support{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        <div class="custom-control custom-checkbox custom-control-inline">
                            <input class="custom-control-input" type="checkbox" id="fo_label_support_0" value="APP" {{ fo_label.support == 'APP' or fo_label.support == 'ALL' ? 'checked' : '' }} disabled>
                            <label class="custom-control-label" for="fo_label_support_0" >APP</label>
                        </div>
                        <div class="custom-control custom-checkbox custom-control-inline">
                            <input class="custom-control-input" type="checkbox" id="fo_label_support_1" value="WEB" {{ fo_label.support == 'WEB' or fo_label.support == 'ALL' ? 'checked' : '' }} disabled>
                            <label class="custom-control-label" for="fo_label_support_1">WEB</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Libellé référent{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        {% if prefered_language == "fr" %}
                            <input class="form-control" type="text" value="{{ fo_label.frLabel }}" disabled="disabled" />
                        {% else %}
                            <input class="form-control" type="text" value="{{ fo_label.enLabel }}" disabled="disabled" />
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {% trans %}Valeur du paramètre{% endtrans %}
                    </div>
                    <div class="col-md-9">
                        <input class="form-control" type="text" value="{{ fo_label.parameterValue}}" disabled="disabled" />
                    </div>
                </div>
            </div>
            {% for translation in form.translations %}
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-3">
                            {{ form_label(translation.defaultTranslation) }}

                            {% set language = translation.vars.data.language %}
                            <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                        </div>
                        <div class="col-md-9">
                            {{ form_widget(translation.defaultTranslation) }}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <div class="card-footer text-right">
            <a class="mr-3 btn btn-dark" role="button" href="{{ path('local_translation_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
            <button class="btn btn-success">{{ button_label|default('save') |trans }}</button>
        </div>
    </div>
    {{ form_end(form) }}
{% endblock %}
