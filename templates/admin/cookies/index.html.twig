{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">Cookie Policy</h1>

    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;height: 800px">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%}active{% endif %}" id="wsparameters-app">
                        <div id="cookies-app">
                            {{ form_start(formApp, {'attr': { 'name':'myCustomFormName' }}) }}
                            {% for cookies in formApp.cookies %}
                                {% set language = cookies.vars.data.language %}
                                <div class="card">
                                    <div class="card-header" id="{{'heading-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#cookies-app">
                                        <div class="card-body">
                                            {{ form_widget(cookies.content) }}
                                            <li style ="list-style-type: none;" class="m-2">
                                                <h7>Url web:</h7>
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 1rem;">
                                                {% set url = url('cookies_web_view', {'source': 'APP', 'brand': profile.site.brand, 'culture': language.code ~ '-'~ profile.site.country }) %}
                                                <a href="{{url}}" target="_blank">{{ url }}</a>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_cookies_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formApp) }}
                        </div>
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                        <div id="cookies-web">
                            {{ form_start(formWeb) }}
                            {% for cookies in formWeb.cookies %}
                                {% set language = cookies.vars.data.language %}
                                <div class="card">
                                    <div class="card-header" id="{{'heading-web-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                               href="#section-web-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-web-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-web-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-web-' ~ language.code }}" data-parent="#cookies-web">
                                        <div class="card-body">
                                            {{ form_widget(cookies.content) }}
                                            <li style ="list-style-type: none;" class="m-2">
                                                <h7>Url web:</h7>
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 1rem;">
                                                {% set url = url('cookies_web_view', {'source': 'WEB', 'brand': profile.site.brand, 'culture': language.code ~ '-'~ profile.site.country }) %}
                                                <a href="{{url}}" target="_blank">{{ url }}</a>
                                            </li>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_cookies_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formWeb) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
