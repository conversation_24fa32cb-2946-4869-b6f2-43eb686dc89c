{% extends '_layout/base_back.html.twig' %}

{% block javascripts %}
	{{ parent() }}
	<script src="{{ asset('js/multiselect.js') }}"></script>
	<script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
	<script>
		$(document).ready(function () {
			$value = $('input[type=radio][name="create_fo_label_form[type]"]');
		});
		$('input[type=radio][name="create_fo_label_form[type]"]').change(function () {
		if ("Simple" === this.value) {
		$('#rich_labels').hide();
		$('#simple_labels').show();
		$('#rich_labels').prop('required', false);
		$('#simple_labels').prop('required', true);
		}
		if ("Rich" === this.value) {
		$('#rich_labels').show();
		$('#simple_labels').hide();
		$('#rich_labels').prop('required', true);
		$('#simple_labels').prop('required', false);
}


});
	</script>
{% endblock %}

{% block stylesheets %}
     <style>
                .hidden { display: none; }
    </style>
{% endblock %}

{% block body %}
	<h3 class="h3 mb-4 text-gray-800">{% trans %}add_key_referent{% endtrans %}</h3>

	{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
	<div class="card shadow-sm">
		<div class="card-body">
			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						<div class="d-flex">
							{{ form_label(form.keyLabel) }}<span class="mandatory">*</span>
						</div>
					</div>
					<div class="col-md-9">
						{{ form_widget(form.keyLabel) }}
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						<div class="d-flex">
							{{ form_label(form.sprintNumber) }}<span class="mandatory">*</span>
						</div>
					</div>
					<div class="col-md-9">
						{{ form_widget(form.sprintNumber) }}
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						{{ form_label(form.functionalityName) }}
					</div>
					<div class="col-md-9">
						{{ form_widget(form.functionalityName) }}
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						<div class="d-flex">
							{{ form_label(form.brands) }}<span class="mandatory">*</span>
						</div>
					</div>
					<div class="col-md-9">
						{{ form_widget(form.brands) }}
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						<div class="d-flex">
							{{ form_label(form.support) }}<span class="mandatory">*</span>
						</div>
					</div>
					<div class="col-md-9">
						{{ form_widget(form.support) }}
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						<div class="d-flex">
							{{ form_label(form.type) }}<span class="mandatory">*</span>
						</div>
					</div>
					<div class="col-md-9">
						{{ form_widget(form.type) }}
					</div>
				</div>
			</div>
			<div id="simple_labels">
				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.frLabel) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.frLabel) }}
						</div>
					</div>
				</div>

				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.enLabel) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.enLabel) }}
						</div>
					</div>
				</div>
			</div>
			<div id="rich_labels" class="hidden">
				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.frLabelRich) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.frLabelRich) }}
						</div>
					</div>
				</div>

				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.enLabelRich) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.enLabelRich) }}
						</div>
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="row">
					<div class="col-md-3">
						{{ form_label(form.parameterValue) }}
					</div>
					<div class="col-md-9">
						{{ form_widget(form.parameterValue) }}
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer text-right">
			<a class="mr-3 btn btn-dark" role="button" href="{{ path('fo_label_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
			<button class="btn btn-primary" type="submit">{{ button_label|default('Ajouter') |trans }}</button>
		</div>
	</div>
	{{ form_end(form) }}
{% endblock %}


