{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
     <style>
                .hidden { display: none; }
    </style>
{% endblock %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800" >{% trans %}edit_key_referent{% endtrans %} ( {{ fo_label.keyLabel }} )</h3>

    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                    	<div class="d-flex">
                            {{ form_label(form.keyLabel) }} <span class="mandatory">*</span>
                        </div>
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.keyLabel) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex">
                            {{ form_label(form.sprintNumber) }}<span class="mandatory">*</span>                        
                        </div>
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.sprintNumber) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.functionalityName) }}
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.functionalityName) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex">
                            {{ form_label(form.brands) }}<span class="mandatory">*</span>
                        </div>
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.brands) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex">
                            {{ form_label(form.support) }}<span class="mandatory">*</span>                    
                        </div>
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.support) }}
                    </div>
                </div>
            </div>
 
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex">
                            {{ form_label(form.type) }}<span class="mandatory">*</span>
                        </div>
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.type) }}
                    </div>
                </div>
            </div>

            <div id="simple_labels" class="hidden">
				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.frLabel) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.frLabel) }}
						</div>
					</div>
				</div>

				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.enLabel) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.enLabel) }}
						</div>
					</div>
				</div>
			</div>
			<div id="rich_labels" class="hidden">
				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.frLabelRich) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.frLabelRich) }}
						</div>
					</div>
				</div>


				<div class="form-group">
					<div class="row">
						<div class="col-md-3">
							<div class="d-flex">
								{{ form_label(form.enLabelRich) }}<span class="mandatory">*</span>
							</div>
						</div>
						<div class="col-md-9">
							{{ form_widget(form.enLabelRich) }}
						</div>
					</div>
				</div>
			</div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.parameterValue) }}
                    </div>
                    <div class="col-md-9">
                        {{ form_widget(form.parameterValue) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a href="#delete-folabel-modal" class="mr-3 btn btn btn-danger" data-folabel-id="{{ fo_label.id }}" data-folabel-key="{{ fo_label.keyLabel }}">{% trans %}Supprimer{% endtrans %}</a>
            <a class="mr-3 btn btn-dark" role="button" href="{{ path('fo_label_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
            <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
        </div>
    </div>
    {{ form_end(form) }}
{% endblock %}

{% block modals %}
    <!-- DELETE FO Label -->
    <div class="modal fade" id="delete-folabel-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('fo_label_delete', {profile: profile.id, id: ':id'}) }}" id="delete-folabel-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-folabel-token') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette clé de référence{% endtrans %} ?</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <!-- DELETE FO LABEL -->
    <script>
        window.$(function () {
            var $deleteFoLabelModal = window.$('div#delete-folabel-modal'),
                $deleteFoLabelForm  = $deleteFoLabelModal.find('form#delete-folabel-form'),
                deleteFoLabelAction = $deleteFoLabelForm.attr('action');

            window.$('a[href="#delete-folabel-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteFoLabelForm.attr('action', deleteFoLabelAction.replace(':id', window.$(this).attr('data-folabel-id')));

                $deleteFoLabelModal.modal('show');
            });

            $deleteFoLabelModal.on('hidden.bs.modal', function () {
                $deleteFoLabelForm.attr('action', deleteFoLabelAction);
            });
        });
    </script>
    <script>
     $(document).ready(function() {
        input = $('input[type=radio][name="edit_fo_label_form[type]"]:checked');
        value = input.val();
        if ("Simple" === value) {
            $('#simple_labels').removeClass("hidden");
            $('#edit_fo_label_form_frLabel').prop("required", true);
            $('#edit_fo_label_form_enLabel').prop("required", true);
            }
        if ("Rich" === value) {
            $('#rich_labels').removeClass("hidden");
            $('#edit_fo_label_form_frLabelRich').prop("required", true);
            $('#edit_fo_label_form_enLabelRich').prop("required", true);
            }                   
        }); 

        $('input[type=radio][name="edit_fo_label_form[type]"]').change(function () {
		if ("Simple" === this.value) {
		$('#rich_labels').hide();
		$('#simple_labels').show();
		$('#rich_labels').prop('required', false);
		$('#simple_labels').prop('required', true);
		}
		if ("Rich" === this.value) {
		$('#rich_labels').show();
		$('#simple_labels').hide();
		$('#rich_labels').prop('required', true);
		$('#simple_labels').prop('required', false);
}


});

        $('form[name="edit_fo_label_form"]').submit(function(e) {
        $('input[type=radio][name="edit_fo_label_form[type]"]:checked').removeAttr('disabled')
          });		
	</script>
{% endblock %}
