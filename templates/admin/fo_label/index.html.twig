{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}Liste des{% endtrans %} labels FO</h1>        

    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <div class="text-right">
                <a role="button" class="btn btn-primary" href="{{ path('fo_label_new', {'profile': profile.id}) }}">{% trans %}Nouveau libellé{% endtrans %}</a>
            </div>
        </div>
        <div class="row">

            <!-- Import -->
            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <!-- Card Header - Dropdown -->
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Import</h6>
                    </div>
                    <!-- Card Body -->
                    <div class="card-body">
                    {{ form_start(form_import, {'action': path('fo_label_index', {profile: profile.id}), 'method': 'POST' }) }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans %}Fichier{% endtrans %}</label>
                                    {{ form_widget(form_import.file) }}
                                    <ul class="list-unstyled text-danger">
                                        {% for error in form_import.vars.errors.form.getErrors(true) %}
                                            <li>{% trans %}file_not_allowed{% endtrans %}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form_label(form_import.source) }}
                                    {{ form_widget(form_import.source) }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form_label(form_import.brand) }}
                                    {{ form_widget(form_import.brand) }}
                                </div>
                            </div>
                        </div>
                    {{ form_end(form_import) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Filters -->
            {{ form_start(form) }}
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            {{ form_label(form.brand) }}
                            {{ form_widget(form.brand) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.functionalityName) }}
                            {{ form_widget(form.functionalityName) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.sprint) }}
                            {{ form_widget(form.sprint) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.traduction) }}
                            {{ form_widget(form.traduction) }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form_label(form.keyLabel) }}
                            {{ form_widget(form.keyLabel) }}
                        </div>
                    </div>
                </div>
            {{ form_end(form) }}
            <!-- Export -->
            {{ form_start(form_export, {'action': path('fo_label_export', {profile: profile.id}), 'method': 'POST' }) }}
                <div class="form-group">
                    {{ form_widget(form_export.submit) }}
                </div>
            {{ form_end(form_export) }}
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
                    <thead>
                    <tr>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Status{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Date de création{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Date de maj{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Sprint{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Fonctionnalité{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Brands{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Device{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Clé de libellé{% endtrans %}</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Libellé référent{% endtrans %} - FR</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Libellé référent{% endtrans %} - EN</small></th>
                        <th class="text-primary"><small class="font-weight-bold">{% trans %}Text type{% endtrans %}</small></th>
                        <th class="text-primary text-right"><small class="font-weight-bold">{% trans %}Actions{% endtrans %}</small></th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE FO Label -->
    <div class="modal fade" id="delete-folabel-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('fo_label_delete', {profile: profile.id, id: ':id'}) }}" id="delete-folabel-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-folabel-token') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette clé de référence{% endtrans %} ?</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            const keyLabel = document.querySelector('#fo_label_filterer_keyLabel');
            const brand = document.querySelector('#fo_label_filterer_brand');
            const functionalityName = document.querySelector('#fo_label_filterer_functionalityName');
            const traduction = document.querySelector('#fo_label_filterer_traduction');
            const sprint = document.querySelector('#fo_label_filterer_sprint');
            let table = $('#dataTable').DataTable({
                "processing": true, 
                "serverSide": true, 
                "ajax": {
                    "url": "{{ path('fo_label_fo_paginate', {'profile': profile.id}) }}",
                    "data": function ( d ) {
                        d.brand = brand.value;
                        d.functionalityName = functionalityName.value;
                        d.sprint = sprint.value;
                        d.traduction = traduction.value;
                        d.keyLabel = keyLabel.value;
                    }
                },
                "sAjaxDataProp": "data", 
                "pageLength": 25, 
                "paging" : true,
                "info" : true,
                "searching": false,
                "responsive": true,

                stateSave: true,
                columns: [
                        { data: 'bgcTranslationStatus'},
                        { data: 'creationDate' },
                        { data: 'lastUpdate' },
                        { data: 'sprint' },
                        { data: 'functionalityName' },
                        { data: 'brands' },
                        { data: 'support' },
                        { data: 'keyLabel' },
                        { data: 'frLabel' },
                        { data: 'enLabel' },
                        { data: 'type' },
                        { data: function ( row, type ) {
                            if ( type === 'display' ) {
                                myRender = '';
                                myRender += '<a href="'+ row.urlLabel +'" class="btn btn-sm btn-warning"><small>{% trans %}Modifier{% endtrans %}</small></a>';
                                myRender += '<a href="#delete-folabel-modal" class="modals_triggers btn btn-sm btn-danger" data-folabel-id="' + row.id + '" data-folabel-key="' + row.keyLabel + '"><small>{% trans %}Supprimer{% endtrans %}</small></a>';
                                return myRender;
                            }

                            return '' ;

                        }},
                ],
                "rowCallback": function( row, data ) {
                        $('td:eq(0)', row).html(data.translationStatusText);
                        $('td:eq(0)', row).attr('style','background-color:'+data.bgcTranslationStatus);
                        $('td:eq(0)', row).attr('title',data.translationStatusText);
                        $('td:eq(0)', row).addClass('p-0');
                    },
                'order': [[ 1, "desc" ]],
                'autoWidth': false,
                'columnDefs': [
                        {'orderable': false, targets: [0, 10]},
                        { width: "7%", targets: 0 },
                        { width: "7%", targets: 1 },
                        { width: "7%", targets: 2 },
                        { width: "7%", targets: 3 },
                        { width: "5%", targets: 4 },
                        { width: "5%", targets: 5 },
                        { width: "5%", targets: 6 },
                        { width: "10%", targets: 7 },
                        { width: "24%", targets: 8 },
                        { width: "24%", targets: 9 },
                        { width: "10%", targets: 10 },
                        { width: "12.5%", targets: 11 }
                    ]
                }
            );
            table.on('draw', function () {
                $(".truncate").each(function(){
                    if($(this).text().trim().length > 100){
                        let text = $(this).text().trim().substring(0 , 100) + '...';
                        $(this).html(text); 
                    }
                });   
            })
            brand.addEventListener('change', function () {
                table.draw();
            });
            keyLabel.addEventListener('input', function () {
                table.draw();
            });
            functionalityName.addEventListener('input', function () {
                table.draw();
            });
            traduction.addEventListener('input', function () {
                table.draw();
            });
            sprint.addEventListener('input', function () {
                table.draw();
            }); 
            $('body').on('click', '#fo_label_export_submit', function(event) {
                event.preventDefault();
                var url = '{{ path('fo_label_export', {'profile': profile.id}) }}';
                $.ajax({
                    type: 'POST',
                    url: url,
                    dataType: 'json',
                    data: {
                        brand: brand.value,
                        keyLabel: keyLabel.value,
                        functionalityName: functionalityName.value,
                        traduction: traduction.value,
                        sprint: sprint.value
                        },
                    success: function(response) {
                        window.location.href = url;
                    }
                });
            });
        });
    </script>
    <script>
        $('.custom-file-input').on('change', function(event) {
            var inputFile = event.currentTarget;
            $(inputFile).parent()
                .find('.custom-file-label')
                .html(inputFile.files[0].name);
        });
    </script>

    <!-- DELETE FO LABEL -->
    <script>
        window.$(function () {
            var $deleteFoLabelModal = window.$('div#delete-folabel-modal'),
                $deleteFoLabelForm  = $deleteFoLabelModal.find('form#delete-folabel-form'),
                deleteFoLabelAction = $deleteFoLabelForm.attr('action');

            $('body').on('click', "a[href='#delete-folabel-modal']", function(event) {
                event.preventDefault();
                $deleteFoLabelForm.attr('action', deleteFoLabelAction.replace(':id', window.$(this).attr('data-folabel-id')));

                $deleteFoLabelModal.modal('show');
            });

            $deleteFoLabelModal.on('hidden.bs.modal', function () {
                $deleteFoLabelForm.attr('action', deleteFoLabelAction);
            });
            $(".truncate").each(function(){
                if($(this).text().trim().length > 100){
                    let text = $(this).text().trim().substring(0 , 100) + '...';
                    $(this).html(text);
                }
            });      
        });
    </script>

{% endblock %}
