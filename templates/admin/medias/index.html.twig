{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/uppy/uppy.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    {{ render(controller('App\\Controller\\Bo\\Admin\\MediasController::mediasTreeAction',
        {   'profile': profile,
            'hideBtn': 'false',
            'hideTools': 'false'
        }
    )) }}
{% endblock %}

{% block modals %}
     <!-- ADD -->
     {% include "admin/medias/modals/add.html.twig" %}
    <!-- EDIT -->
    {% include "admin/medias/modals/edit.html.twig" %}
    <!-- DELETE -->
    {% include "admin/medias/modals/delete.html.twig" %}
   
{% endblock %}

{% block javascripts %}
    {{ parent() }}
        {{ include('admin/medias/js/js.html.twig', { 'hideBtn': 'false',  'hideTools': 'false'}) }}

{% endblock %}
