<script src="{{ asset('js/uppy/uppy.min.js') }}"></script>
<script src="{{ asset('js/uppy/locale.min.js') }}"></script>

<script>
	var pdfIconeSource = '/images/pdf-icon.png';
var isMediaUploaded = false;
var hideBtn = {{ hideBtn }};
if (hideBtn == false) {
let uppyMessages = document.getElementById('uppy-messages');

var uppy = Uppy.Core({
debug: false,
autoProceed: false,
locale: "{{ language }}" === 'fr' ? Uppy.locales.fr_FR : Uppy.locales.en_US,
restrictions: {
maxFileSize: 512000,
maxNumberOfFiles: 12,
minNumberOfFiles: 1,
allowedFileTypes: [
'image/*',
'.jpg',
'.jpeg',
'.png',
'.gif',
'.bmp',
'.pdf'
]
}
}).use(Uppy.Dashboard, {
inline: true,
height: 300,
width: '100%',
target: '#uppy-container',
browserBackButtonClose: true
}).use(Uppy.XHRUpload, {
endpoint: "{{ path('admin_medias_upload', {profile: profile.id}) }}",
fieldName: 'images',
method: 'post'
}).on('upload', function (data) {
uppyMessages.innerHTML = '';
}).on('upload-error', function (file, error, response) {
$("#media-form-add-button").prop("disabled", true);
let li = document.createElement('li');
li.appendChild(document.createTextNode(response.body.message));
uppyMessages.appendChild(li);
}).on('upload-success', (file, response) => {
const httpBody = response.body // extracted response data
$("#add_medias_form_id").val(httpBody.data.id);
$(".add_medias_form_name").val(httpBody.data.name);
isMediaUploaded = true;
}).on('complete', function (result) {
if (result.failed.length === 0) {
$("#media-form-add-button").prop("disabled", false);
}
});
$(function () {
var $addMediaModal = $('div#add-media-modal');
var $editMediaModal = $('div#edit-media-modal');
var $deleteMediaModal = $('div#delete-media-modal');
$('a[href="#delete-content-modal"]').on('click', function (event) {
event.preventDefault();
$deleteCarburantForm.attr('action', deleteCarburantAction.replace(':id', $(this).attr('data-content-id')));
$deleteCarburantModal.modal('show');
});

$('a[href="#add-media-modal"]').on('click', function (event,) {
event.preventDefault();
$addMediaModal.modal('show');
});
$('a[href="#edit-media-modal"]').on('click', function (event) {
event.preventDefault();
$("#edit-media-modal #alert-loading").hide();
$("#edit-media-modal #alert-success").hide();
$("#edit-media-modal #alert-error").hide();
// $('#edit_medias_form_name').attr('name', 'value');
var mediaId = $(this).attr('data-media');
var url = '{{ (path("admin_medias_edit", {profile: profile.id, media: ":id"})) }}'
$.ajax({
url: url.replace(':id', mediaId),
type: "GET",
dataType: "json",
success: function (data) {
var media = data.success
if (media) {
$("#edit_medias_form_id").val(media.id);
$(".edit_medias_form_name").val(media.name);
$(".edit_medias_form_textAlt").val(media.textAlt);
$(".edit_medias_form_copyright").val(media.copyright);
$(".edit_medias_form_comment").val(media.comment);
}
}
});

$editMediaModal.modal('show');
});

$('a[href="#delete-media-modal"]').on('click', function (event) {
event.preventDefault();
$("#delete-media-modal #alert-loading").hide();
$("#delete-media-modal #alert-success").hide();
$("#delete-media-modal #alert-error").hide();
var mediaId = $(this).attr('data-media');
$("#delete_medias_form_id").val(mediaId);
$deleteMediaModal.modal('show');
});

$('a[href="#add-media-modal"]').on('click', function (event) {
event.preventDefault();
$("#add-media-modal #alert-loading").hide();
$("#add-media-modal #alert-success").hide();
$("#add-media-modal #alert-error").hide();
$("#media-form-add-button").prop("disabled", true);
$addMediaModal.modal('show');
});

});
}
</script>
<script>

	// searsh ( call ajax ) to find where to add ajax action
// to add function add folder we need to delete commenter in html bloc

function openChild(index) {
event.stopPropagation();

var display = $("#Leaf-" + index).css('display');
if (display == "none") {
$("#Leaf-" + index).show();
$("#folder-" + index)[0].setAttribute('class', 'far fa-folder-open');
$("#icon-" + index)[0].setAttribute('src', '/images/minus.gif');
} else {
$("#Leaf-" + index).hide();
$("#folder-" + index)[0].setAttribute('class', 'far fa-folder');
$("#icon-" + index)[0].setAttribute('src', '/images/plus.gif');
}
}
// to add function edit & delete we need to have param 'editable == true' we change in the call
function showFiles(id, datas, order, editable) {
event.stopPropagation();
$(".bloc_file #files .cadre_image .BlocLoading").hide();
$(".bloc_file #alert-loading").hide();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-error").hide();
$(".bloc_file #files").hide();
$(".bloc_file #files").attr('data-id', id);
$(".bloc_file #files").attr('data-input', $(datas).text());
$(".bloc_file #bloc_add").hide();
$(".bloc_file #files #edit_name").hide();
$('#delete-folder-alert').hide();
var route = datas.getAttribute("data-title");
$(".bloc_file #files #title").html(route);
$(".bloc_file #files #title")[0].setAttribute('data-title', route);
$(".bloc_file #files #title")[0].setAttribute('data-order', order);
if (editable == 'true') {
$("#tools-folder").show();
} else {
$("#tools-folder").hide();
}
if (hideBtn == false) 
uppy.setMeta({parent_id: id})





this.getImage(id, order);
$(".bloc_file #files").show();
}
// show images
function getImage(id, order, currentPage = 1, refreshPages = true) {

if (refreshPages) {
$('.page-number').remove();
$('.page-text').hide();
}
// clear content
$(".bloc_file #files .cadre_image #list-image").html("");
$(".bloc_file #files .cadre_image .BlocLoading").show();
$(".bloc_file #files .cadre_image #no-image").hide();
$(".bloc_file #files .cadre_image #edit-media").hide();
$(".bloc_file #files .cadre_image #delete-media").hide();
$(".bloc_file #files .cadre_image").show();

var url = '{{ (path("admin_medias_directory_medias", {profile: profile.id, id: ":id"})) }}'
var context = this;
var images = '';
$.ajax({
url: url.replace(':id', id),
type: "GET",
data: {
page: currentPage
},
dataType: "json",
success: function (data) {
isAdmin = "{{ isAdmin }}";
images = data.success.medias;
totalPages = data.success.totalPages;
thirdValue = data.success.thirdValue;
toShowTools = false;
if (thirdValue == "{{ currentCountry }}" || isAdmin == 1) {
toShowTools = true;
$(".bloc_file #files .cadre_image #add-media").show();
} else {
toShowTools = false;
$(".bloc_file #files .cadre_image #add-media").hide();
}
if (images.length > 0) {
const toShow = 7; // should be impair
const skipPages = toShow;
imageHtml = "";
liHtml = "";
for (let image of images) {
imageHtml += appendImage(image, toShowTools);
}
$("#tools-folder").hide();
$(".bloc_file #files .cadre_image .BlocLoading").hide();
$('#previous-media-page').attr('onclick', 'getImage(' + id + ', 0, ' + (
currentPage - 1
) + ', false)');
$('#next-media-page').attr('onclick', 'getImage(' + id + ', 0, ' + (
currentPage + 1
) + ', false)');

$('#previous-media-10-pages').attr('onclick', 'getImage(' + id + ', 0, ' + (
currentPage - skipPages
) + ', false)');
$('#next-media-10-pages').attr('onclick', 'getImage(' + id + ', 0, ' + (
currentPage + skipPages
) + ', false)');

// manage step page links
if (currentPage <= skipPages) {
$('#previous-media-10-pages').removeClass('disabled').attr('onclick', 'getImage(' + id + ', 0, ' + 1 + ', false)');
} else {
$('#previous-media-10-pages').removeClass('disabled');
}
if ((totalPages - currentPage) < skipPages) {
$('#next-media-10-pages').removeClass('disabled').attr('onclick', 'getImage(' + id + ', 0, ' + totalPages + ', false)');
} else {
$('#next-media-10-pages').removeClass('disabled');
}
// manage disabled previous links
if (1 == currentPage) {
$('#previous-media-page').addClass('disabled').removeAttr('onclick');
$('#previous-media-10-pages').addClass('disabled').removeAttr('onclick');
} else {
$('#previous-media-page').removeClass('disabled');
}
// manage disabled next links
if (totalPages == currentPage) {
$('#next-media-page').addClass('disabled').removeAttr('onclick');
$('#next-media-10-pages').addClass('disabled').removeAttr('onclick');
} else {
$('#next-media-page').removeClass('disabled');
}

$('#media-pagination li').removeClass('active');
$(".bloc_file #files .cadre_image #list-image").html(imageHtml);

$('.page-number').remove();
pages = getPaginationPages(totalPages, currentPage, toShow);

lastOne = 0;
pages.forEach((page) => {
if (page - lastOne > 1) {
if (page == totalPages) {
liHtml += addButtonPagination(id, (lastOne + 1), "...")
} else {
liHtml += addButtonPagination(id, (page - 1), "...")
}
}
liHtml += addButtonPagination(id, (page), page)
lastOne = page;
});

$(liHtml).insertAfter($('#previous-media-page'));

$('#page-' + currentPage).addClass('active');
$('.page-text').show();
} else {
$(".bloc_file #files .cadre_image .BlocLoading").hide();
$(".bloc_file #files .cadre_image #no-image").show();
if (toShowTools == 'true') {
$("#tools-folder").show();
}
}
}
});
}
function selectImage(selected, id, username, toShowTools) {
event.stopPropagation();
isAdmin = "{{ isAdmin }}";
var elems = document.querySelectorAll(".bloc_file #files .cadre_image #list-image .cadre-image");
[].forEach.call(elems, function (el) {
el.classList.remove("selected");
});
$(".bloc_file #files .cadre_image #list-image .cadre-image#identifiant-" + id)[0].classList.add("selected");
if (hideBtn == false) {
if ('true' == toShowTools) {
if (username == "{{ currentUsername }}" || isAdmin == 1) {
$(".bloc_file #files .cadre_image #delete-media")[0].setAttribute("data-media", id);
$(".bloc_file #files .cadre_image #delete-media").show();
$(".bloc_file #files .cadre_image #edit-media")[0].setAttribute("data-media", id);
$(".bloc_file #files .cadre_image #edit-media").show();
} else {
    $(".bloc_file #files .cadre_image #delete-media").hide();
    $(".bloc_file #files .cadre_image #edit-media").hide();
}
$(".bloc_file #files .cadre_image #add-media").show();
}
} else {
$('#selected_img').val(id);
}
}

function showdetail(id, url) {
event.stopPropagation();
media = '';
// {"id":1,"url":"/images/ds.jpeg"}
$("#show-image #full-image")[0].setAttribute("src", getExtension(url) == 'pdf' ? pdfIconeSource : url);
// call ajax get info by id
var url = '{{ (path("admin_medias_get", {profile: profile.id, media: ":id"})) }}'
$.ajax({
url: url.replace(':id', id),
type: "GET",
dataType: "json",
success: function (data) {
if (data.success) {
media = data.success
dimension = media.dimension.width + 'x' + media.dimension.height
content = `
                    <p> <span>Image ${
media.extension.toUpperCase()
} : </span> ${dimension} (${
bytesToSize(media.size)
})</p>
                    <p> <span>Date de creation : </span> ${
media.createdAt ?? 'n/d'
}</p>
                    <p> <span>Dossier : </span> ${
media.parentPath ?? 'n/d'
}</p>
                    <p> <span>Titre : </span> ${
media.name ?? 'n/d'
}</p>
                    <p> <span>Legende (alt) : </span> ${
media.textAlt ?? 'n/d'
}</p>
                    <p> <span>Copyright : </span> ${
media.copyright ?? 'n/d'
}</p>
                    <p> <span>Commentaires : </span> ${
media.comment ?? 'n/d'
}</p>`;

$("#show-image .content .information").html(content);
$("#show-image").show();
}
}
});
}
// convert bytes to size
function bytesToSize(bytes, precision = 2) {
var sizes = [
'O',
'Ko',
'Mo',
'Go',
'To'
];
if (bytes == 0) 
return '0 O';



var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
return Math.round(bytes / Math.pow(1024, i), precision) + ' ' + sizes[i];
}

function closeShowImage() {
event.stopPropagation();
$("#show-image").hide();
}
// add folder
function showAddFolder(datas) {
event.stopPropagation();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-error").hide();
var folder = $(datas)[0].getAttribute('data-folder');
var order = $(datas)[0].getAttribute('data-order');
var title = $(datas)[0].getAttribute('data-title');
var id = $(datas).data('id');
$(".bloc_file #bloc_add #title").html(title);
$(".bloc_file #bloc_add #title")[0].setAttribute('data-folder', folder);
$(".bloc_file #bloc_add #title")[0].setAttribute('data-order', order);
$(".bloc_file #bloc_add #title")[0].setAttribute('data-title', title);
$(".bloc_file #bloc_add #title")[0].setAttribute('data-id', id);
$(".bloc_file #bloc_add #add_input")[0].value = '';
$(".bloc_file #files").hide();
$(".bloc_file #bloc_add").show();
}
function closeAddFolder() {
event.stopPropagation();
$(".bloc_file #bloc_add").hide();
}
function addFolder() {
event.stopPropagation();
$(".bloc_file #alert-loading").show();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-error").hide();
var order = $(".bloc_file #bloc_add #title")[0].getAttribute('data-order');
var folder = $(".bloc_file #bloc_add #title")[0].getAttribute('data-folder');
var title = $(".bloc_file #bloc_add #title")[0].getAttribute('data-title');
var input = $(".bloc_file #bloc_add #add_input")[0].value;
var id = $(".bloc_file #bloc_add #title")[0].getAttribute('data-id');
var url = '{{ (path('admin_medias_directory_add', {profile: profile.id, id: ":id"})) }}'
var context = this;
$.ajax({
url: url.replace(':id', id),
type: "POST",
dataType: "json",
data: {
"directory": input
},
success: function (data) {
context.successAction('add-success');
context.buildHtml(folder, order, title, input, data.id);
$(".bloc_file #files .cadre_image #add-media").show();
},
error: function () {
context.errorAction('add-error');
$(".bloc_file #files .cadre_image #add-media").show();
}
});
}

// edit folder
function showEditName() {
event.stopPropagation();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-loading").hide();
$('#delete-folder-alert').hide();
$(".bloc_file #files .cadre_image").hide();
$(".bloc_file #files #edit_input").val($(".bloc_file #files")[0].getAttribute('data-input'));
$(".bloc_file #files #edit_name").show();
}

function closeEditName() {
event.stopPropagation();
$(".bloc_file #files #edit_name").hide();
$(".bloc_file #files .cadre_image").show();
}
function editFolder() {
event.stopPropagation();
$(".bloc_file #alert-loading").show();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-error").hide();
var order = $(".bloc_file #files #title")[0].getAttribute('data-order');
var input = $(".bloc_file #files #edit_name #edit_input")[0].value;
// call ajax edit folder
var context = this;
var url = '{{ (path('admin_medias_directory_update', {profile: profile.id, id: ":id"})) }}'
var id = $(".bloc_file #files")[0].getAttribute('data-id');
oldTitle = $(".bloc_file #files #title")[0].getAttribute('data-title');
newTitle = this.buildNewTitle(oldTitle, input);
$.ajax({
url: url.replace(':id', id),
type: "POST",
dataType: "json",
data: {
"directory": input
},
success: function (data) {
$(".bloc_file #files").attr('data-input', input);
$(".bloc_file #files #title").html(newTitle);
$(".bloc_file #files #title")[0].setAttribute('data-title', newTitle);
$(".bloc_three .global_list #li-" + order + " #label-" + order).html(input);
$(".bloc_three .global_list #li-" + order + " #label-" + order)[0].setAttribute("data-title", newTitle);
if ($(".bloc_three .global_list #li-" + order + " #Leaf-" + order)[0]) {
context.updateChild(oldTitle, input);
}
context.successAction('edit-success');
},
error: function () {
context.errorAction('edit-error');
}
});
}
function updateChild(oldTitle, input) {
elements = document.querySelectorAll(`[data-title^="${oldTitle}"`);
elements.forEach(element => {
Title = element.attributes['data-title'].nodeValue;
let oldvalue = oldTitle.split(" > ");
Title = Title.split(" > ");
for (let index = 0; index < Title.length; index++) {
if (Title[index] == oldvalue[oldvalue.length - 1]) {
Title[index] = input.toUpperCase();
}

}
element.attributes['data-title'].nodeValue = Title.join(" > ");;
});

}
// delete folder
function showDeleteFolder() {
event.stopPropagation();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-success").hide();
$(".bloc_file #alert-loading").hide();
$('#delete-folder-alert').hide();
$(".bloc_file #files .cadre_image").hide();
$(".bloc_file #files #edit_name").hide();
$('#delete-folder-alert').show();
}
function deleteFolder() {
event.stopPropagation();
$(".bloc_file #alert-loading").show();
var order = $(".bloc_file #files #title")[0].getAttribute('data-order');
var id = $(".bloc_file #files")[0].getAttribute('data-id');
var url = '{{ (path('admin_medias_directory_remove', {profile: profile.id, id: ":id"})) }}'
var context = this;
$.ajax({
url: url.replace(':id', id),
type: "DELETE",
dataType: "json",
success: function (data) {
context.successAction('delete-success');
$(".bloc_three .global_list #li-" + order).remove();
},
error: function () {
context.errorAction('delete-error');
}
});
}
function closeDeleteFolder() {
event.stopPropagation();
$('#delete-folder-alert').hide();
$(".bloc_file #files .cadre_image").show();
}
// help function
function successAction(type) {
event.stopPropagation();
$(".bloc_file #alert-loading").hide();
if (type == 'edit-success') {
$(".bloc_file #alert-success .title").html("Folder edited");
$(".bloc_file #alert-success").show();
setTimeout(function () {
$(".bloc_file #alert-success").hide();
// $(".bloc_file #files").hide();
}, 2000);
$(".bloc_file #files #edit_name").hide();
$(".bloc_file #files .cadre_image").show();
} else if (type == 'delete-success') {
$(".bloc_file #alert-success .title").html("Folder deleted");
$(".bloc_file #alert-success").show();
setTimeout(function () {
$(".bloc_file #alert-success").hide();
$(".bloc_file #files").hide();
}, 2000);
} else if (type == 'add-success') {
$(".bloc_file #alert-success .title").html("Folder Added");
$(".bloc_file #alert-success").show();
setTimeout(function () {
$(".bloc_file #alert-success").hide();
$(".bloc_file #files").hide();
}, 2000);
}

}

function errorAction(type) {
event.stopPropagation();
$(".bloc_file #alert-loading").hide();
if (type == 'edit-error') {
$(".bloc_file #alert-error .title").html("Erreur lors de la modification");
$(".bloc_file #alert-error").show();
} else if (type == 'delete-error') {
$(".bloc_file #alert-error .title").html("Erreur lors de la suppression");
$(".bloc_file #alert-error").show();
setTimeout(function () {
$(".bloc_file #alert-error").hide();
}, 3000);
} else if (type == 'add-error') {
$(".bloc_file #alert-error .title").html("Erreur lors de l'ajout");
$(".bloc_file #alert-error").show();
}

}
function buildNewTitle(title, newValue) {
titleSplice = title.split(' > ');
titleSplice[(titleSplice.length - 1)] = newValue.toUpperCase();
return titleSplice.join(" > ");
}
function buildHtml(folder, order, title, input, id) {
SplitFolder = folder.split('-');
if (SplitFolder.length == 3) {
content = `<li id="li-${folder}-${order}">
                        <img src="/images/join.gif">
                        <i class="far fa-folder"></i>
                        <a href="#files" id="label-${folder}-${order}" data-title="${title} > ${input}" onclick="showFiles(${id}, this,'${folder}-${order}','true')" class="ml-1 link-child">${input}</a>
                    </li>`;
} else {
content = `<li id="li-${folder}-${order}">
                        <img src="/images/plus.gif" id="icon-${folder}-${order}" onclick="openChild('${folder}-${order}')">
                        <i class="far fa-folder" id="folder-${folder}-${order}" onclick="openChild('${folder}-${order}')"></i>
                        <a href="#files" id="label-${folder}-${order}" data-title="${title} > ${input}" onclick="showFiles(${id}, this,'${folder}-${order}','true')" class="ml-1 link-child">${input}</a>
                        
                        <ul class="pl-4" style="display: none;" id="Leaf-${folder}-${order}">                                                          
                            <li id="append-${folder}-${order}"></li>
                            <li><img src="/images/join.gif">
                                <i class="fas fa-folder-plus fa-lg pl-1" id="appendFolder-${folder}-${order}" style="color:#1cc88a" data-order="0" data-title="${title} > ${input}" data-folder="${folder}-${order}" data-id="${id}" onclick="showAddFolder(this)"></i>
                            </li>                                                                                
                        </ul>
                    </li>`;
}
$(".bloc_three .global_list #append-" + folder).before(content);
var orderAppend = $(".bloc_three .global_list #appendFolder-" + folder)[0].getAttribute('data-order');
$(".bloc_three .global_list #appendFolder-" + folder)[0].setAttribute('data-order', ++ orderAppend);
}

// edit Media
function addMedia() {
event.stopPropagation();
$("#add-media-modal #alert-loading").show();
id = $('#add_medias_form_id').val();
if (isMediaUploaded) {
var url = '{{ (path("admin_medias_add", {profile: profile.id, media: ':id'})) }}'
$.ajax({
url: url.replace(':id', id),
type: "POST",
dataType: "json",
data: $("form#add-media-form").serialize(),
success: function (data) {
if (data.success) {
image = data.success;
imageHtml = appendImage(image, true);
$(".bloc_file #files .cadre_image #no-image").hide();
$(".bloc_file #files .cadre_image #list-image").append(imageHtml);
$("#add-media-modal #alert-loading").hide();
$("#add-media-modal #alert-success .title").html("Media added successfully");
$("#add-media-modal #alert-success").show();
$("#tools-folder").hide();
setTimeout(function () {
$('div#add-media-modal').modal('hide');
}, 1500);
$('form#add-media-form').trigger("reset");
uppy.reset();
} else {
$("#add-media-modal #alert-danger .title").html("Error add media");
$("#add-media-modal #alert-danger").show();
setTimeout(function () {
$("#add-media-modal #alert-danger").hide();


}, 3000);
}
}
});
}


}
// edit Media
function editMedia() {
event.stopPropagation();
$("#edit-media-modal #alert-loading").show();
id = $("#edit_medias_form_id").val();
var url = '{{ (path("admin_medias_edit", {profile: profile.id, media: ":id"})) }}'
$.ajax({
url: url.replace(':id', id),
type: "POST",
dataType: "json",
data: $("form#edit-media-form").serialize(),
success: function (data) {
if (data.success) {
$("#edit-media-modal #alert-loading").hide();
$("#edit-media-modal #alert-success .title").html("Media edited successfully");
$("#edit-media-modal #alert-success").show();
setTimeout(function () {
$('div#edit-media-modal').modal('hide');
}, 1500);
} else {
$("#edit-media-modal #alert-danger .title").html("Error edit media");
$("#edit-media-modal #alert-danger").show();
setTimeout(function () {
$("#edit-media-modal #alert-danger").hide();
}, 3000);
}
}
});
}
// delete Media
function deleteMedia() {
event.stopPropagation();
$("#delete-media-modal #alert-loading").show();
id = $("#delete_medias_form_id").val();
var url = '{{ (path("admin_medias_delete", {profile: profile.id, media: ":id"})) }}'
$.ajax({
url: url.replace(':id', id),
type: "DELETE",
dataType: "json",
data: $("form#delete-media-form").serialize(),
success: function (data) {
if (true == data.success) {
$(".bloc_file #files .cadre_image #delete-media").hide();
$(".bloc_file #files .cadre_image #edit-media").hide();
$("#identifiant-" + id).parent().remove();
$("#delete-media-modal #alert-loading").hide();
$("#delete-media-modal #alert-success .title").html("Media deleted successfully");
$("#delete-media-modal #alert-success").show();
setTimeout(function () {
$('div#delete-media-modal').modal('hide');
}, 1500);
if (data.totalMedias == 0) {
$("#tools-folder").show();
$(".bloc_file #files .cadre_image #no-image").show();
}

} else {
$("#delete-media-modal #alert-loading").hide();
$("#delete-media-modal #alert-danger .title").html(data.error);
$("#delete-media-modal #alert-danger").show();
setTimeout(function () {
$("div#delete-media-modal").modal('hide');
}, 3000);
}
}
});
}

function appendImage(image, toShowTools) {
source = getExtension(image.path) == 'pdf' ? pdfIconeSource : image.path;
return `<div class="col-3 px-1 mb-2">
                <div class="py-2 px-3 cadre-image" id="identifiant-${
image.id
}">
                <img src="${source}" alt="Media" data-path="${
image.path
}" onclick="selectImage(this,'${
image.id
}', '${
image.createdBy
}', '${toShowTools}')" />
                <i class="fas fa-search fa-lg" style="color:#36b9cc;"  onclick="showdetail('${
image.id
}','${
image.path
}')"></i>
                </div>
        </div> `;
}

function getExtension(path) {
source = path.split('.');
return source.pop().toLowerCase();
}

function addButtonPagination(id, page, show) {
return `<li id="page-${page}" class="page-item page-number" onclick= "getImage(${id}, 0, ${page}, false)"><a class="page-link" href="javascript:void(0)">${show}</a></li>`;
}

function getPaginationPages(totalPages, currentPage, toShow) {
const median = parseInt((toShow - 3) / 2);
lastPage = totalPages - 1;
firstPage = 2;
pages = [];
if (totalPages <= toShow) {
pages = Array.from({
length: totalPages
}, (_, i) => i + 1);
} else {
pages = [1, currentPage, totalPages];
lengthLeft = lengthRight = median;
left = currentPage - median;
if (left < firstPage) {
lengthLeft = (currentPage - firstPage) % median;
lengthRight = lengthRight + (median - lengthLeft);
}
right = currentPage + median;
if (right >= totalPages) {
lengthRight = (lastPage - currentPage) % median;
lengthLeft = lengthLeft + (median - lengthRight);
}
lefts = Array.from({
length: lengthLeft
}, (_, i) => currentPage - i - 1);
rights = Array.from({
length: lengthRight
}, (_, i) => currentPage + i + 1);
pages = pages.concat(lefts.concat(rights)).sort((a, b) => a - b);
}
pages = [...new Set(pages)];

return pages;
}
</script>
