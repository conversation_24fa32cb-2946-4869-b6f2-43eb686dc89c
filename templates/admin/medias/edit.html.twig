{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h3 class="h3 mb-4 text-gray-800">{% trans %}Edit image{% endtrans %}</h3>

    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.name) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.name) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.extension) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.extension) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.textAlt) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.textAlt) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.copyright) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.copyright) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.formattedSize) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.formattedSize) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.comment) }}
                    </div>
                    <div class="col-md-7">
                        {{ form_widget(form.comment) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_medias_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
            <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
        </div>
    </div>
    {{ form_end(form) }}
{% endblock %}
