<div class="card shadow mb-4 media-page">
	<div class="row justify-content-center my-5">
		<div class="col-3 mx-3 bloc_three">
			<ul class="global_list">
				<li>
					<i class="far fa-folder-open" style="text-transform: uppercase;"></i>
					<span id="label-{{branch.id}}" data-title="{{"#{branch.label}"}}" onclick="showFiles({{branch.id}}, this,{{branch.id}},'false')" class="ml-1" style="text-transform: uppercase;">{{branch.label}}</span>

					{% if  branch.child is not null %}
						<ul class="pl-0">
							{% for keyleaf,Leaf in branch.child %}

								<li id="li-{{keyleaf}}">
									<img src="/images/plus.gif" id="icon-{{keyleaf}}" onclick="openChild('{{keyleaf}}')">
									<i class="far fa-folder" id="folder-{{keyleaf}}" onclick="openChild('{{keyleaf}}')"></i>
									<span id="label-{{keyleaf}}" data-title="{{"#{branch.label} > #{Leaf.label}"}}" onclick="showFiles({{Leaf.id}}, this,'{{"#{keyleaf}"}}','false')" class="ml-1" style="text-transform: uppercase;">{{Leaf.label}}</span>

									<ul class="pl-4" style="display: none;" id="Leaf-{{keyleaf}}">
										{% if  Leaf.child is not null %}
											{% for keyleafN2,LeafN2 in Leaf.child %}
												{% if  LeafN2.label == currentCountry or isAdmin == 1%}
													{% set hideTools = 'true' %}
												{% endif %}
												<li id="li-{{keyleaf}}-{{keyleafN2}}">
													<img src="/images/plus.gif" id="icon-{{keyleaf}}-{{keyleafN2}}" onclick="openChild('{{"#{keyleaf}-#{keyleafN2}"}}')">
													<i class="far fa-folder" id="folder-{{keyleaf}}-{{keyleafN2}}" onclick="openChild('{{"#{keyleaf}-#{keyleafN2}"}}')"></i>
													<a href='#files' id="label-{{keyleaf}}-{{keyleafN2}}" data-title="{{"#{branch.label} > #{Leaf.label} > #{LeafN2.label}"}}" onclick="showFiles({{LeafN2.id}}, this,'{{" #{keyleaf}-#{keyleafN2}"}}', '{{hideTools}}')" class="ml-1 link-child">{{LeafN2.label}}</a>

													<ul class="pl-4" style="display: none;" id="Leaf-{{keyleaf}}-{{keyleafN2}}">
														{% if  LeafN2.child is not null %}
															{% for keyleafN3,LeafN3 in LeafN2.child %}

																<li id="li-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}">
																	<img src="/images/plus.gif" id="icon-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}" onclick="openChild('{{"#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}')">
																	<i class="far fa-folder" id="folder-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}" onclick="openChild('{{"#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}')"></i>
																	<a href='#files' id="label-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}" data-title="{{"#{branch.label} > #{Leaf.label} > #{LeafN2.label} > #{LeafN3.label}"}}" onclick="showFiles({{LeafN3.id}}, this,'{{"#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}', '{{hideTools}}')" class="ml-1 link-child">{{LeafN3.label}}</a>

																	<ul class="pl-4" style="display: none;" id="Leaf-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}">
																		{% if  LeafN3.child is not null %}
																			{% for keyleafN4,LeafN4 in LeafN3.child %}

																				<li id="li-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}-{{keyleafN4}}">
																					<img src="/images/join.gif">
																					<i class="far fa-folder"></i>
																					<span id="label-{{keyleaf}}-{{keyleafN2}}-{{keyleafN3}}-{{keyleafN4}}" data-title="{{"#{branch.label} > #{Leaf.label} > #{LeafN2.label} > #{LeafN3.label} > #{LeafN4.label}"}}" onclick="showFiles({{LeafN4.id}}, this,'{{"#{keyleaf}-#{keyleafN2}-#{keyleafN3}-#{keyleafN4}"}}', '{{hideTools}}')" class="ml-1" style="text-transform: uppercase;">{{LeafN4.label}}</span>
																				</li>
																			{% endfor %}
																		{% endif %}
																		{% if  LeafN2.label == currentCountry or isAdmin == 1 %}
																			<div id="{{"append-#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}"></div>
																			<li><img src="/images/join.gif">
																				<i class="fas fa-folder-plus fa-lg pl-1" style="color:#1cc88a" id="{{"appendFolder-#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}" data-order="{% if LeafN3.child is not null %}{{LeafN3.child|length}}{% else %}0{% endif %}" data-title="{{"Racine > #{Leaf.label} > #{LeafN2.label} > #{LeafN3.label}"}}" data-folder="{{"#{keyleaf}-#{keyleafN2}-#{keyleafN3}"}}" data-id="{{ LeafN3.id }}" onclick="showAddFolder(this)"></i>
																			</li>
																		{% endif %}
																	</ul>

																</li>
															{% endfor %}
														{% endif %}
														{% if  LeafN2.label == currentCountry or isAdmin == 1 %}
															<div id="{{"append-#{keyleaf}-#{keyleafN2}"}}"></div>
															<li><img src="/images/join.gif">
																<i class="fas fa-folder-plus fa-lg pl-1" style="color:#1cc88a" id="{{"appendFolder-#{keyleaf}-#{keyleafN2}"}}" data-order="{% if LeafN2.child is not null %}{{LeafN2.child|length}}{% else %}0{% endif %}" data-title="{{"Racine > #{Leaf.label} > #{LeafN2.label}"}}" data-folder="{{"#{keyleaf}-#{keyleafN2}"}}" data-id="{{ LeafN2.id }}" onclick="showAddFolder(this)"></i>
															</li>
														{% endif %}
													</ul>

												</li>
											{% endfor %}
										{% endif %}

									</ul>

								</li>
							{% endfor %}

						</ul>
					{% endif %}
				</li>
			</ul>
		</div>
		<div class="col-7 mx-3 bloc_file">
			<div class="alert alert-secondary mt-3 mb-0" style="display:none;text-align:center" id="alert-loading" role="alert">
				<img src="/images/loading.png" alt="loading" class="loading" width="35"/>
			</div>
			<div class="alert alert-success mt-3 mb-0" style="display:none;" id="alert-success" role="alert">
				<span class="title"></span>
			</div>
			<div class="alert alert-danger mt-3 mb-0" style="display:none;" id="alert-error" role="alert">
				<span class="title"></span>
			</div>
			<div id="bloc_add" style="display:none;">
				<div class="mt-3" style="font-size:24px; color: black;">Ajout Dossier</div>
				<label class="mt-2" style="font-size:20px; color: black;">
					Chemin :
					<span id="title" data-folder="" data-order="" data-title="" style="font-size:16px;text-transform:uppercase;"></span>
				</label>
				<input type="text" class="form-control mt-3" id="add_input"/>
				<div class="mt-4 mb-3 text-right">
					<button type="button" class="btn btn-success" onclick="addFolder()">Ajouter</button>
					<button type="button" class="btn btn-secondary" onclick="closeAddFolder()">annuler</button>
				</div>
			</div>
			<div id="files" style="display:none;">
				<label class="mt-3" style="font-size:20px; color: black;">
					Dossier :
					<span id="title" data-order="" data-title="" data-id="" style="font-size:16px;text-transform:uppercase;"></span>

					{% if hideBtn == 'false' %}
						<span id="tools-folder">
							<span class="ml-2">
								<i class="far fa-edit" style="cursor:pointer;color:#15aabf;" onclick="showEditName();"></i>
							</span>
							<span class="ml-1">
								<i class="far fa-trash-alt" style="cursor:pointer;color:#B0413E;" onclick="showDeleteFolder();"></i>
							</span>
						</span>
					{% endif %}
				</label>
				<div class="mt-1" id="edit_name" style="display:none;">
					<div class="alert alert-primary" role="alert">
						Modifer nom dossier
						<input type="text" class="form-control mt-3" id="edit_input"/>
						<div class="mt-3 text-right">
							<button type="button" class="btn btn-info" onclick="editFolder()">Modifier</button>
							<button type="button" class="btn btn-secondary" onclick="closeEditName()">annuler</button>
						</div>
					</div>
				</div>
				<div id="delete-folder-alert" class="mt-1">
					<div class="alert alert-danger" role="alert">
						Êtes-vous sûr(e) de vouloir supprimer ce dossier ?
						<div class="mt-3 text-right">
							<button type="button" class="btn btn-danger" onclick="deleteFolder()">Supprimer</button>
							<button type="button" class="btn btn-secondary" onclick="closeDeleteFolder()">annuler</button>
						</div>
					</div>
				</div>
				<div class="my-4 cadre_image">
					<p>
						Listes images</p>
					<div class="BlocLoading">
						<img src="/images/loading.png" alt="loading" class="loading" width="35"/>
					</div>
					<div class="alert alert-danger mb-3" id="no-image" role="alert" style="width: 200px; margin: auto; margin-top: -20px;display:none;">
						Pas d'enregistrement
					</div>
					<div id="list-image" class="row mx-2 mb-2"></div>
					<div class="my-3 mx-3 text-right">
						{% if hideBtn == 'false' %}
							<a id="add-media" href="#add-media-modal" class="btn btn-sm btn-success">{% trans %}Ajouter{% endtrans %}</a>
							<a style="display:none;" id="edit-media" href="#edit-media-modal" class="btn btn-sm btn-info" data-media="">{% trans %}Modifier{% endtrans %}</a>
							<a style="display:none;" id="delete-media" href="#delete-media-modal" class="btn btn-sm btn-danger" data-media="">{% trans %}Supprimer{% endtrans %}</a>
						{% endif %}
					</div>
					<div class=" mx-3 text-right">
						<nav aria-label="Page navigation example">
							<ul class="pagination justify-content-end" id="media-pagination">
								<li class="page-item page-text" id="previous-media-10-pages"><a class="page-link" href="javascript:void(0)"><<</a></li>
								<li class="page-itempage-text" id="previous-media-page"><a class="page-link" href="javascript:void(0)"><</a></li>
								<li class="page-itempage-text" id="next-media-page"><a class="page-link" href="javascript:void(0)">></a></li>
								<li class="page-itempage-text" id="next-media-10-pages"><a class="page-link" href="javascript:void(0)">>></a></li>
							</ul>
						</nav>
					</div>

				</div>
			</div>
		</div>
	</div>
</div>
<div id="show-image" style="display:none">
	<div class="close-bloc text-right w-100 px-4 py-3">
		<i class="fas fa-times fa-2x" style="color:white;cursor:pointer;" onclick="closeShowImage()"></i>
	</div>
	<div class="content text-center">
		<div class="image">
			<img src="" id="full-image"/>
		</div>
		<div class="information my-4"></div>
	</div>
</div>
