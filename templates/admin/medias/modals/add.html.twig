<div class="modal fade" id="add-media-modal" tabindex="-1" role="dialog" aria-labelledby="add" aria-hidden="true">
        <form id="add-media-form" onsubmit="return false;">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Ajout image</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    {{ form_start(formAdd)}}
                    <div class="modal-body">
						<div class="alert alert-secondary mb-4 mx-3" style="display:none;text-align:center" id="alert-loading" role="alert">
							<img src="/images/loading.png" alt="loading" class="loading" width="35"/>
						</div>
						<div class="alert alert-success mb-4 mx-3" style="display:none;" id="alert-success" role="alert">
							<span class="title"></span>
						</div>
						<div class="alert alert-danger mb-4 mx-3" style="display:none;" id="alert-error" role="alert">
							<span class="title"></span>
						</div>
                        <div id="uppy-container" class="mx-4"></div>
                        <ul id="uppy-messages" class="text-danger" style="margin: 10px 0px;"></ul>
                        <div class="msg-Informatif mx-4 mb-5">
                        <p style="margin-bottom: 5px;">Formats acceptés: ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.pdf') </p>
                        <p style="margin-bottom: 5px;">Taille max accepté: (512 Kb)</p>
                        </div>
                         <div class="formulaire mx-4">
                            <div class="form-group">
                            {{ form_row(formAdd._token) }}
                                <input type="hidden" id="add_medias_form_id" name="add_medias_form_id">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.name) }} <span class="mandatory">*</span>
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.name, {'attr': {'class': 'add_medias_form_name'}}) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.textAlt) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.textAlt) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.copyright) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.copyright) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.comment) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.comment) }}
                                    </div>
                                </div>
                            </div>
                         </div>
                    </div>
                    <div class="modal-footer">
                        <button id="media-form-add-button" class="btn btn-success" onclick='addMedia()'>{% trans %}Enregistrer{% endtrans %}</button>
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    </div>
                    {{ form_end(formAdd, {render_rest: false}) }}
                </div>
            </div>
        </form>
    </div>