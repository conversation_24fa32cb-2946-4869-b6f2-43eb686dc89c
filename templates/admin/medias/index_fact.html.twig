{% extends '_layout/base_back.html.twig' %}
{% import _self as self %}

{% block stylesheets %}
    <link href="{{ asset('css/uppy/uppy.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% macro buildDirectories(directory) %}
    <li>
        {% if not directory.parentDirectory %}
            <i class="far fa-folder-open" style="text-transform: uppercase;"></i> {{directory.label}}
        {% else %}
            <img src="/images/plus.gif"> 
            <i class="far fa-folder"></i>
            <span class="ml-1" style="text-transform: uppercase;">{{directory.label}}</span>
        {% endif %}
        {% if directory.childDirectories|length %}
            <ul class="pl-0">
                {% for child in directory.childDirectories %}
                    {{ _self.buildDirectories(child) }}
                {% endfor %}
            </ul>
        {% endif %}
    </li>
{% endmacro %}

{% block body %}
    <div class="card shadow mb-4 media-page">
        <div class="row justify-content-center my-5">
            <div class="col-3 mx-3 bloc_three">
                <ul class="global_list">
                    {{ _self.buildDirectories(tree) }}
                </ul>
            </div>
            <div class="col-7 mx-3 bloc_file">
                <div class="alert alert-success mt-3 mb-0" style="display:none;" id="alert-success" role="alert">
                    <span class="title"></span>
                </div>
                <div id="bloc_add" style="display:none;">
                    <div class="mt-3" style="font-size:24px; color: black;">Ajout Dossier</div>
                    <label class="mt-2" style="font-size:20px; color: black;"> Chemin : <span id="title" data-folder="" data-order="" data-title="" style="font-size:16px;text-transform:uppercase;"></span> </label>
                    <input type="text" class="form-control mt-3" id="add_input"/>
                    <div class="mt-4 mb-3 text-right">
                    <button type="button" class="btn btn-success" onclick="addFolder()">Ajouter</button>
                    <button type="button" class="btn btn-secondary" onclick="closeAddFolder()">annuler</button>
                    </div>
                </div>
                <div id="files" style="display:none;">
                    <label class="mt-3" style="font-size:20px; color: black;">
                        Dossier : 
                        <span id="title" data-order="" data-title="" style="font-size:16px;text-transform:uppercase;"></span> 
                        <span id="tools-folder">
                        <span class="ml-2"><i class="far fa-edit" style="cursor:pointer;color:#15aabf;" onclick="showEditName();"></i></span>
                        <span class="ml-1"><i class="far fa-trash-alt" style="cursor:pointer;color:#B0413E;" onclick="showDeleteFolder();"></i></span>
                        <span>
                    </label>
                    <div class="mt-1" id="edit_name" style="display:none;">
                        <div class="alert alert-primary" role="alert">
                            Modifer nom dossier
                            <input type="text" class="form-control mt-3" id="edit_input"/>
                            <div class="mt-3 text-right">
                                <button type="button" class="btn btn-info" onclick="editFolder()">Modifier</button>
                                <button type="button" class="btn btn-secondary" onclick="closeEditName()">annuler</button>
                            </div>
                        </div>
                    </div>
                    <div id="delete-folder-alert" class="mt-1">
                        <div class="alert alert-danger" role="alert">
                                Êtes-vous sûr(e) de vouloir supprimer ce dossier et c'est sous dossier et images ?
                                <div class="mt-3 text-right">
                                <button type="button" class="btn btn-danger" onclick="deleteFolder()">Supprimer</button>
                                <button type="button" class="btn btn-secondary" onclick="closeDeleteFolder()">annuler</button>
                            </div>
                        </div>
                    </div>
                    <div class="my-4 cadre_image">
                        <p> Listes images</p>
                        <div class="alert alert-danger mb-3" id="no-image" role="alert" style="width: 200px; margin: auto; margin-top: -20px;display:none;">
                            Pas d'enregistrement
                        </div>
                        <div id="list-image" class="row mx-2 mb-2">
                        </div>
                        <div class="my-3 mx-3 text-right">
                              <a id="add-media" href="#add-media-modal" class="btn btn-sm btn-success" >{% trans %}Ajouter{% endtrans %}</a>
                              <a style="display:none;" id="edit-media" href="#edit-media-modal" class="btn btn-sm btn-info" data-media="">{% trans %}Modifier{% endtrans %}</a>
                              <a style="display:none;" id="delete-media" href="#delete-media-modal" class="btn btn-sm btn-danger" data-media="">{% trans %}Supprimer{% endtrans %}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- ADD -->
    <div class="modal fade" id="add-media-modal" tabindex="-1" role="dialog" aria-labelledby="add" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Ajout image</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    {{ form_start(formAdd)}}
                    <div class="modal-body">
                        <div id="uppy-container" class="mx-4"></div>
                        <ul id="uppy-messages" class="text-danger" style="margin: 10px 0px;"></ul>
                        <div class="msg-Informatif mx-4 mb-5">
                        <p style="margin-bottom: 5px;">Formats acceptés: ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.pdf') </p>
                        <p style="margin-bottom: 5px;">Taille max accepté: (512 Kb)</p>
                        </div>
                         <div class="formulaire mx-4">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.name) }} <span class="mandatory">*</span>
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.name) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.textAlt) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.textAlt) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.copyright) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.copyright) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formAdd.comment) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formAdd.comment) }}
                                    </div>
                                </div>
                            </div>
                         </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-success" type="submit">{% trans %}Ajouter{% endtrans %}</button>
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    </div>
                    {{ form_end(formAdd) }}
                </div>
            </div>
    </div>
    <!-- EDIT -->
    <div class="modal fade" id="edit-media-modal" tabindex="-1" role="dialog" aria-labelledby="edit" aria-hidden="true">
        <form action="" id="edit-media-form" method="POST">
            {# <input type="hidden" name="token" value="{{ csrf_token('delete-media') }}"/> #}
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Modifer image</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    {{ form_start(formEdit)}}
                    <div class="modal-body">
                         <div class="formulaire mx-4">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formEdit.name) }} <span class="mandatory">*</span>
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formEdit.name) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formEdit.textAlt) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formEdit.textAlt) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formEdit.copyright) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formEdit.copyright) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form_label(formEdit.comment) }}
                                    </div>
                                    <div class="col-md-7">
                                        {{ form_widget(formEdit.comment) }}
                                    </div>
                                </div>
                            </div>
                         </div>
                    </div>
                    <div class="modal-footer">
                            <button class="btn btn-info" type="submit">{% trans %}Modifer{% endtrans %}</button>
                            <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        </div>
                    {{ form_end(formEdit) }}                    
                </div>
            </div>
        </form>
    </div>
    <!-- DELETE -->
    <div class="modal fade" id="delete-media-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('admin_medias_delete', {profile: profile.id, media: ':id'}) }}" id="delete-media-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-media') }}"/>
            <input type="hidden" name="_method" value="DELETE">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}confirmation_delete_image{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/uppy/uppy.min.js') }}"></script>
    <script src="{{ asset('js/uppy/locale.min.js') }}"></script>
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>

    <script>
        $(function () {
            let uppyMessages = document.getElementById('uppy-messages');

            var uppy = Uppy.Core({
                debug: false,
                autoProceed: false,
                locale: "{{language}}" === 'fr' ? Uppy.locales.fr_FR : Uppy.locales.en_US,
                restrictions: {
                    maxFileSize: 512000,
                    maxNumberOfFiles: 12,
                    minNumberOfFiles: 1,
                    allowedFileTypes: ['image/*', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.pdf']
                }
            })
                .use(Uppy.Dashboard, {
                    inline: true,
                    height: 300,
                    width: '100%',
                    target: '#uppy-container',
                    browserBackButtonClose: true,
                })
                .use(Uppy.XHRUpload, {
                    endpoint:"{{ path('admin_medias_upload', {profile: profile.id}) }}",
                    fieldName: 'images' ,
                    method:'post'
                })
                .on('upload', function (data) {
                    uppyMessages.innerHTML = '';
                })
                .on('upload-error', function (file, error, response) {
                    let li = document.createElement('li');
                    li.appendChild(document.createTextNode(response.body.message));
                    uppyMessages.appendChild(li);
                })
                .on('complete', function (result) {
                    if (result.failed.length === 0) {
                        uppy.reset();
                        location.reload()
                    }
                });

            var $addMediaModal = $('div#add-media-modal');
            var $editMediaModal = $('div#edit-media-modal');
            var $deleteMediaModal = $('div#delete-media-modal');

            $('a[href="#add-media-modal"]').on('click', function (event) {
                event.preventDefault();
                $addMediaModal.modal('show');
            });
            $('a[href="#edit-media-modal"]').on('click', function (event) {
                event.preventDefault();
                $editMediaModal.modal('show');
            });
            $('a[href="#delete-media-modal"]').on('click', function (event) {
                event.preventDefault();
                $deleteMediaModal.modal('show');
            });

        });
    </script>
    <script>
     
     // searsh ( call ajax ) to find where to add ajax action
     // to add function add folder we need to delete commenter in html bloc

    function openChild(index){
        event.stopPropagation();

        var display = $("#Leaf-"+index).css('display');

         if(display == "none") { 
             $("#Leaf-"+index).show(); 
             $("#folder-"+index)[0].setAttribute('class', 'far fa-folder-open'); 
             $("#icon-"+index)[0].setAttribute('src', '/images/minus.gif');
        }else{  
            $("#Leaf-"+index).hide(); 
            $("#folder-"+index)[0].setAttribute('class', 'far fa-folder'); 
            $("#icon-"+index)[0].setAttribute('src', '/images/plus.gif');
        }
    }
    // to add function edit & delete we need to have param 'editable == true' we change in the call 
    function showFiles(datas,order,editable){
        event.stopPropagation();
        $(".bloc_file #bloc_add").hide();
        $(".bloc_file #files #edit_name").hide();
        $('#delete-folder-alert').hide();
        var route = datas.getAttribute("data-title");
        $(".bloc_file #files #title").html(route);
        $(".bloc_file #files #title")[0].setAttribute('data-title',route);
        $(".bloc_file #files #title")[0].setAttribute('data-order',order);
        if( editable == 'true' ) $(".bloc_file #files #tools-folder").show();
        else $(".bloc_file #files #tools-folder").hide();
        this.getImage(order);
        $(".bloc_file #files").show();
    }
    // show images
    function getImage(order){
        // clear content
        $(".bloc_file #files .cadre_image #list-image").html("");
        $(".bloc_file #files .cadre_image #no-image").hide();
        $(".bloc_file #files .cadre_image #edit-media").hide();
        $(".bloc_file #files .cadre_image #delete-media").hide();
        // random show
        show = Math.floor(Math.random() * 2);
        // call ajax (get image from REPO)
        if(show == 1){
        images = [
                        {"id":1,"URL":"/images/ds.jpeg"},
                        {"id":2,"URL":"/images/psa.png"},
                        {"id":3,"URL":"/images/vin-image.jpg"},
                        {"id":4,"URL":"/images/ds.jpeg"},
                        {"id":5,"URL":"/images/psa.png"},
                        {"id":6,"URL":"/images/vin-image.jpg"}
                    ];
        }else{
            images = [];
        }
        
        if(images.length > 0 ){
            imageHtml = "";
            for(let image of images){
                    imageHtml += `<div class="col-3 px-1 mb-2">
                                        <div class="py-2 px-3 cadre-image" onclick="selectImage(this,'${image.id}')">
                                        <img src="${image.URL}" alt="Media"/>
                                        </div>
                                </div> `;
            }
            $(".bloc_file #files .cadre_image #list-image").html(imageHtml);
        }else{
            $(".bloc_file #files .cadre_image #no-image").show();
        }
         
    }
    function selectImage(selected,id){
         var elems = document.querySelectorAll(".bloc_file #files .cadre_image #list-image .cadre-image");
            [].forEach.call(elems, function(el) {
                el.classList.remove("selected");
            });
          selected.classList.add("selected");
          $(".bloc_file #files .cadre_image #edit-media")[0].setAttribute("data-media",id);
          $(".bloc_file #files .cadre_image #delete-media")[0].setAttribute("data-media",id);
          $(".bloc_file #files .cadre_image #edit-media").show();
          $(".bloc_file #files .cadre_image #delete-media").show();
    }
    // add folder
    function showAddFolder(datas){
        event.stopPropagation();

        var folder = $(datas)[0].getAttribute('data-folder');
        var order = $(datas)[0].getAttribute('data-order');
        var title = $(datas)[0].getAttribute('data-title');
        var folderId = $(data).data('id');
        $(".bloc_file #files").hide();
        $(".bloc_file #bloc_add #title").html(title);
        $(".bloc_file #bloc_add #title")[0].setAttribute('data-folder',folder);
        $(".bloc_file #bloc_add #title")[0].setAttribute('data-order',order);
        $(".bloc_file #bloc_add #title")[0].setAttribute('data-title',title);
        $(".bloc_file #bloc_add #title")[0].setAttribute('data-ID',id);
        $(".bloc_file #bloc_add #add_input")[0].value = '';
        $(".bloc_file #bloc_add").show();
    }
    function closeAddFolder(){
        event.stopPropagation();
        $(".bloc_file #bloc_add").hide();
    }
    function addFolder(){
        event.stopPropagation();
        var order = $(".bloc_file #bloc_add #title")[0].getAttribute('data-order');
        var folder = $(".bloc_file #bloc_add #title")[0].getAttribute('data-folder');
        var title = $(".bloc_file #bloc_add #title")[0].getAttribute('data-title');
        var input = $(".bloc_file #bloc_add #add_input")[0].value;
        // call ajax add folder
        $.ajax({
            url:'{{ (path('admin_medias_folder_add', {profile: profile.id})) }}',
            type: "POST",
            dataType: "json",
            data: {
                "folder": input
            },
            success: function (data){
                console.log(data);
            }
        });
        //this.successAction('add-success');
        //this.buildHtml(folder,order,title,input);
    }
    // edit folder
    function showEditName(){
        event.stopPropagation();
         $('#delete-folder-alert').hide();
        $(".bloc_file #files #edit_name").show();
    }
    function closeEditName(){
        event.stopPropagation();
        $(".bloc_file #files #edit_name").hide();
    }
    function editFolder(){
        event.stopPropagation();
        var order = $(".bloc_file #files #title")[0].getAttribute('data-order');
        var input = $(".bloc_file #files #edit_name #edit_input")[0].value;
        // call ajax edit folder
        newTitle = this.buildNewTitle($(".bloc_file #files #title")[0].getAttribute('data-title'),input);
        $(".bloc_file #files #title").html(newTitle);
        $(".bloc_file #files #title")[0].setAttribute('data-title',newTitle);
        $(".bloc_three .global_list #li-"+order+" #label-"+order).html(input);
        $(".bloc_three .global_list #li-"+order+" #label-"+order)[0].setAttribute("data-title",newTitle);
        this.successAction('edit-success');
        
    }
    // delete folder
    function showDeleteFolder(){
        event.stopPropagation();
        $(".bloc_file #files #edit_name").hide();
        $('#delete-folder-alert').show();
    }
    function closeDeletefolder(){
        event.stopPropagation();
         $('#delete-folder-alert').hide();
    }
    function deleteFolder(){
        event.stopPropagation();
        var order = $(".bloc_file #files #title")[0].getAttribute('data-order');
        // call ajax delete folder
        $(".bloc_three .global_list #li-"+order).remove();
         this.successAction('delete-success');
    }
    // help function
    function successAction(type){
        event.stopPropagation();
        if(type == 'edit-success'){
            $(".bloc_file #alert-success .title").html("Folder edited");
            $(".bloc_file #alert-success").show();
             setTimeout(function(){
                 $(".bloc_file #alert-success").hide();
                 $(".bloc_file #files #edit_name #edit_input")[0].value = "";
                  $(".bloc_file #files #edit_name").hide();
             }, 3000);
        }else if(type == 'delete-success'){
                $(".bloc_file #alert-success .title").html("Folder deleted");
                $(".bloc_file #alert-success").show();
                setTimeout(function(){
                    $(".bloc_file #alert-success").hide();
                    $(".bloc_file #files").hide();
                }, 3000);
        }else if(type == 'add-success'){
            $(".bloc_file #alert-success .title").html("Folder Added");
            $(".bloc_file #alert-success").show();
            setTimeout(function(){
                $(".bloc_file #alert-success").hide();
                $(".bloc_file #bloc_add").hide();
            }, 3000);
        }
        
    }
    function buildNewTitle(title, newValue){
        titleSplice = title.split(' > ');
        titleSplice[(titleSplice.length - 1)] = newValue;
        newTitle = "";
            for (var i = 0; i < titleSplice.length ; i++) {
                    newTitle += titleSplice[i];
                    if(i != (titleSplice.length -1)) newTitle += " > ";
            }
            return newTitle;
    }
    function buildHtml(folder,order,title,input){
        isAdmin = "{{isAdmin}}";
        editable = false;
        if(input == "{{currentCountry}}" || isAdmin == 1){
            editable = true;
        }
         SplitFolder = folder.split('-');
             if(SplitFolder.length == 3){
                   content = `<li id="li-${folder}-${order}">
                                <img src="/images/join.gif">
                                <i class="far fa-folder"></i>
                                <span id="label-${folder}-${order}" data-title="${title} > ${input}" onclick="showFiles(this,'${folder}-${order}','${editable}')" class="ml-1" style="text-transform: uppercase;">${input}</span>
                            </li>`;
             }else{
                 content = `<li id="li-${folder}-${order}">
                                <img src="/images/plus.gif" id="icon-${folder}-${order}" onclick="openChild('${folder}-${order}')">
                                <i class="far fa-folder" id="folder-${folder}-${order}" onclick="openChild('${folder}-${order}')"></i>
                                <span id="label-${folder}-${order}" data-title="${title} > ${input}" onclick="showFiles(this,'${folder}-${order}', '${editable}')" class="ml-1" style="text-transform: uppercase;">${input}</span>
                                
                                <ul class="pl-4" style="display: none;" id="Leaf-${folder}-${order}">                                                          
                                    <li id="append-${folder}-${order}"></li>
                                    <li><img src="/images/join.gif">
                                        <i class="fas fa-folder-plus fa-lg pl-1" id="appendFolder-${folder}-${order}" style="color:#1cc88a" data-order="0" data-title="${title} > ${input}" data-folder="${folder}-${order}" onclick="showAddFolder(this)"></i>
                                    </li>                                                                                
                                </ul>
                            </li>`;
             }
         $(".bloc_three .global_list #append-"+folder).before(content);
         var orderAppend = $(".bloc_three .global_list #appendFolder-"+folder)[0].getAttribute('data-order');
         $(".bloc_three .global_list #appendFolder-"+folder)[0].setAttribute('data-order', ++orderAppend);
         
    }
    </script>
{% endblock %}