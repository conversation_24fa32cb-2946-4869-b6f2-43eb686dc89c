<form class="d-inline-block" method="post" action="{{ path('app_version_delete', {'id': app_version.id, 'profile' : profile.id}) }}" id="form-{{ app_version.id }}"
      onsubmit="return false;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ app_version.id) }}">
    <button class="btn btn-danger text-right" data-toggle="modal" data-target="#deleteModal{{ app_version.id }}">{% trans %}Supprimer{% endtrans %}</button>
</form>
<!-- Logout Modal-->
<div class="modal fade" id="deleteModal{{ app_version.id }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{% trans %}delete_version_dialog_title{% endtrans %} : {{ app_version.version }}</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-left">{% trans %}delete_version_dialog_body{% endtrans %}</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                <button class="btn btn-danger" type="button" onclick="$('#form-{{ app_version.id }}').attr('onsubmit', 'return true').submit();">{% trans %}Supprimer{% endtrans %}</button>
            </div>
        </div>
    </div>
</div>
