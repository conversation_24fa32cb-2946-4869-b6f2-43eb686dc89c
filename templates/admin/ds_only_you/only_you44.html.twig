<div class="card shadow mb-4">
    <div class="card-header" id="{{'heading-' ~ section }}">
        <h6 class="mb-0">
            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="false"
                aria-controls="section-{{ section }}">
                DS ONLY YOU
            </a>
        </h6>
    </div>
    <div class="overflow-auto card-body" style="overflow-y:scroll;height: 600px">
        {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
            <h3>DS ONLY YOU</h3>
            <div>
                <div class="row">
                    <div class="col-md-3">Activation</div>
                    <div class="col-md-4 ">
                        {{ form_widget(form.oly_enabled) }}
                    </div>
                </div>
                {% set languagesLength = profile.site.languages.count %}
                {% for language in profile.site.languages %}
                    {% set formL = form['form-' ~ language.code ] %}
                    {% if languagesLength > 1 %}
                        <h5>{{ language.label }} <img src="{{ asset('images/flags/'~language.code~'.svg') }}"alt="{{ language.label }}" style="max-width: 1.5rem;"></h5>
                    {% endif %}
                    <div class="row">
                        <div class="col-md-3">{{ 'title' | trans }}</div>
                        <div class="col-md-7">
                            {{ form_widget(formL.title) }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">{{ 'Description' | trans }}</div>
                        <div class="col-md-7">
                            {{ form_widget(formL.description) }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">{{ 'assistance_phone' | trans }}</div>
                        <div class="col-md-3">
                            {{ form_widget(formL.assistance_phone) }}
                        </div>
                    </div>
                    <div class="row mt-5">
                        {% set imageNotExist = (formL.vars.data.image is not defined) or (not formL.vars.data.image) %}
                        {% set imageModalId = 'oly-' ~ language.code %}
                        <div class="col-3">{{ form_label(formL.image) }}</div>
                        <div class="col-9 image-container">
                            <a target="_blank" class="image-link-holder mr-3 {% if imageNotExist %} d-none {% endif %}" style="text-decoration: none">
                                <img class="image-holder" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                                    src="{{ mediaUrl ~ '/' ~ formL.vars.data.image.path }}" alt="{{ formL.vars.data.image.textAlt }}" {% endif %}>
                            </a>
                            <a href="{{ '#' ~ imageModalId }}" class="btn btn-sm btn-success mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                            <button class="btn btn-sm btn-danger delete-image {% if imageNotExist %} d-none {% endif %}" type="button">{% trans %}Supprimer{% endtrans %}</button>
                            {{ include('admin/ds_only_you/media_modal.html.twig', {'imageModalId': imageModalId, 'imageForm': formL.image}) }}
                        </div>
                    </div>
                    {# {{ include('admin/ds_only_you/delete_media_modal.html.twig') }} #}
                {% endfor %}
            </div>
            <div class="text-right mt-3">
                <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
            </div>
        {{ form_end(form) }}
    </div>
</div>