{% import _self as formMacros %}
{% macro customPrototype(accessory, lIndex) %}
    <div class="accessory-container">
        <div class="row mt-2">
            <div class="col-md-3">{{ form_label(accessory.priority) }}</div>
            <div class="col-md-2">
                {{ form_widget(accessory.priority) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-3">{{ form_label(accessory.title) }}</div>
            <div class="col-md-7">
                {{ form_widget(accessory.title) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-3">{{ form_label(accessory.description) }}</div>
            <div class="col-md-7">
                {{ form_widget(accessory.description) }}
            </div>
        </div>
        <div class="row mt-5">
            <div class="col-md-3">{{ form_label(accessory.image) }}</div>
            {% set imageNotExist = (accessory.vars.data.image is not defined) or (not accessory.vars.data.image) %}
            {% set imageModalId = accessory.vars.id %}
            <div class="col-9 image-container">
                <a target="_blank" class="image-link-holder-{{accessory.image.vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}"
                    {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ accessory.vars.data.image.path }}" {% endif %} style="text-decoration: none">
                    <img class="image-holder-{{accessory.image.vars.id}}" name="{{accessory.image.vars.full_name}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                        src="{{ mediaUrl ~ '/' ~ accessory.vars.data.image.path }}" alt="{{ accessory.vars.data.image.textAlt }}" {% endif %}>
                </a>
                <a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick= "addModal('{{accessory.image.vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
                <button class="btn btn-sm btn-danger delete-image-{{accessory.image.vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick= "deleteMedia('{{accessory.image.vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>
                <div class="d-none">
                    {{ form_row(accessory.image, {
                        attr: {class: 'image-picker show-html image-picker-selector'}
                        })
                    }}
                </div>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-3">{{ form_label(accessory.ctaUrl) }}</div>
            <div class="col-md-7">
                {{ form_widget(accessory.ctaUrl) }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-3">{{ form_label(accessory.labelCta) }}</div>
            <div class="col-md-7">
                {{ form_widget(accessory.labelCta) }}
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-10 text-right">
                <button class="btn btn-danger delete-accessory" type="button">{% trans %}loyalty_accessory.delete_accessory{% endtrans %}</button>
            </div>
        </div>
    </div>
{% endmacro %} 
<div class="accessories-fields">
    <div class="row mt-2">
        <div class="col-md-3">{{ form_label(formL.store_url) }}</div>
        <div class="col-md-7">
            {{ form_widget(formL.store_url) }}
        </div>
    </div>
    <hr style="border-top: 1px dotted #8c8b8b; margin: 20px 0px; width: 80%;">
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i> {{ 'loyalty_accessory.info' | trans}}
    </div>
    <div class="accessories-wrapper mt-2"
        data-prototype="{{ formMacros.customPrototype(formL.accessories.vars.prototype)|e('html_attr') }}"
        data-index="{{ formL.accessories|length }}">
        {% for accessory in formL.accessories %}
            {{ formMacros.customPrototype(accessory, loop.index) }}
        {% endfor %}
    </div>
    <div class="row mt-3">
        <div class="col-2">
            <button class="btn btn-info jslink"> {{ "loyalty_accessory.add_accessory" | trans }}</button>
        </div>                
    </div>
</div>