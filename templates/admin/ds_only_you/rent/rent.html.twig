<div class="card">
    <div class="card-header" id="header-{{ section }}">
        <h6 class="mb-0 font-weight-bold text-primary">
            <a class="float-left text-left text-decoration-none w-100" data-toggle="collapse"
                href="#section-{{ section }}" role="button" aria-expanded="true"
                aria-controls="section-{{ section }}">
                {{ ("SECTION" ~ '-' ~ section) | trans }}
            </a>
        </h6>
    </div>
    <div id="section-{{ section }}" class="collapse {% if is_current %}show{% endif %}" aria-labelledby="header-{{ section }}">
        <div class="overflow-auto card-body" style="overflow-y:scroll">
            {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                <div>
                    <div class="row mb-3">
                        <div class="col-md-3">Activation</div>
                        <div class="col-md-4 ">
                            {{ form_widget(form.enabled) }}
                        </div>
                    </div>
                    {% set languagesLength = profile.site.languages.count %}
                    {% for language in profile.site.languages %}
                        {% set formL = form['form-' ~ language.code ] %}
                        {% if languagesLength == 1 %}
                            {{ include('admin/ds_only_you/rent/rent_l_parameters.html.twig', { 'formL': formL }) }}
                        {% else %}
                            <div class="card">
                                <div class="card-header" id="{{'heading-' ~ section ~ language.code }}" style="background-color: lemonchiffon;" >
                                    <h6 class="mb-0">
                                        <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                        href="#section-{{ section ~ language.code }}" role="button" aria-expanded="false"
                                        aria-controls="section-{{ section ~ language.code }}">
                                            <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                            {{ language.label }}
                                        </a>
                                    </h6>
                                </div>
                                <div id="{{ 'section-' ~ section ~ language.code }}" class="collapse" aria-labelledby="{{'heading-' ~ section ~ language.code }}">
                                    <div class="card-body" id="form_container" data-language="{{language.code}}" >
                                        {{ include('admin/ds_only_you/rent/rent_l_parameters.html.twig', { 'formL': formL }) }}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="text-right mt-3">
                    <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                </div>
                 <div class="d-none"> {{ form_rest(form) }} </div>
            {{ form_end(form) }}
        </div>
    </div>
</div>