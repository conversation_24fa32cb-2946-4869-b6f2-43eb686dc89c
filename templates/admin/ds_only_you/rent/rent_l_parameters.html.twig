
<div class="row">
    <div class="col-md-3">{{ form_label(formL.title) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.title) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.description) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.description) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.ftm_url_member) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.ftm_url_member) }}
    </div>
</div>
<div class="row mt-2">
    <div class="col-md-3">{{ form_label(formL.ftm_url_no_member) }}</div>
    <div class="col-md-7">
        {{ form_widget(formL.ftm_url_no_member) }}
    </div>
</div>
<div class="row mt-5">
    {% set imageNotExist = (formL.vars.data.image is not defined) or (not formL.vars.data.image) %}
    {% set imageModalId = section ~ '-' ~ language.code %}
    <div class="col-3">{{ form_label(formL.image) }}</div>
    <div class="col-9 image-container">
        <a target="_blank" class="image-link-holder-{{formL.image.vars.id}} mr-3 {% if imageNotExist %} d-none {% endif %}"
            {% if not imageNotExist %} href="{{ mediaUrl ~ '/' ~ formL.vars.data.image.path }}" {% endif %} style="text-decoration: none">
            <img class="image-holder-{{formL.image.vars.id}}" style="height: 60px;border: 1px solid black" {% if not imageNotExist %}
                src="{{ formL.vars.data.image.extension == 'pdf'|lower ? asset('images/pdf-icon.png') : mediaUrl ~ '/' ~ formL.vars.data.image.path }}" alt="{{ formL.vars.data.image.textAlt }}" {% endif %}>
        </a>
        <a href="#add-modal" class="btn btn-sm btn-success mr-3" data-toggle="modal" onclick="addModal('{{formL.image.vars.id}}')">{% trans %}Ajouter{% endtrans %}</a>
        <button class="btn btn-sm btn-danger delete-image-{{formL.image.vars.id}} {% if imageNotExist %} d-none {% endif %}" type="button" onclick= "deleteMedia('{{formL.image.vars.id}}')">{% trans %}Supprimer{% endtrans %}</button>
    </div>
</div>