{% extends '_layout/base_back.html.twig' %}
{% set medias = profile.site.medias %}
{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
        .pdf-popup-modal .thumbnails li {
            width: 200px;
        }
        .pdf-popup-modal .thumbnails li .thumbnail p {
            font-size: 12px;
        }
        .pdf-popup-modal ul.thumbnails.image_picker_selector li .thumbnail.selected {
            background: unset;
            border: 2px solid #4e73df;
        } 
        .accessory-container {
            padding: 20px;
        }
        .accessory-container:nth-child(odd) {
            background-color: #FAEADA;
        }
        .accessory-container:nth-child(even) {
            background-color: #F9FDF3;
        }       
    </style>
{% endblock %}
{% block body %}
    {{ include('admin/ds_only_you/media_modal.html.twig') }}
    {{ include('admin/ds_only_you/only_you/only_you.html.twig', {'form': onlyYouForm, 'section': onlyYouForm.vars.id, 'is_current': onlyYouForm.vars.id == currentSection, 'profile': profile }) }}
    {{ include('admin/ds_only_you/privileges/privileges.html.twig', {'form': privilegesForm, 'section': privilegesForm.vars.id, 'is_current': privilegesForm.vars.id == currentSection}) }}
    {{ include('admin/ds_only_you/valet/valet.html.twig', {'form': valetForm, 'section': valetForm.vars.id, 'is_current': valetForm.vars.id == currentSection}) }}
    {{ include('admin/ds_only_you/rent/rent.html.twig', {'form': rentForm, 'section': rentForm.vars.id, 'is_current': rentForm.vars.id == currentSection}) }}
    {{ include('admin/ds_only_you/assistance/assistance.html.twig', {'form': assistanceForm, 'section': assistanceForm.vars.id, 'is_current': assistanceForm.vars.id == currentSection}) }}
    {{ include('admin/ds_only_you/accessories/accessories.html.twig', {'form': accessoriesForm, 'section': accessoriesForm.vars.id, 'is_current': accessoriesForm.vars.id == currentSection,'profile': profile}) }} 
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    {{ include('admin/medias/js/js.html.twig', { 'hideBtn': 'true' }) }}
    <script>
        $(function () {
            $(".image-picker").imagepicker({hide_select: true});
            $(".pdf-picker").imagepicker({
                hide_select: true,   
                show_label: true
            });
            $(".adopters_enablor").on('change', function() {
                $('.' + $(this).data('disable')).attr('disabled', !this.checked);
                $(".adopters_check").trigger('change');
            });

            $(".adopters_check").on('change', function() {
                if (this.checked) {
                    $('.' + $(this).data('hide')).show();
                } else {
                    $('.' + $(this).data('hide')).hide();
                }
            });
            $(".adopters_enablor").trigger('change');

            $('.jslink').on('click', function(e) {
                var $wrapper = $(this).closest('.accessories-fields').find('.accessories-wrapper');
                e.preventDefault();
                // Get the data-prototype explained earlier
                var prototype = $wrapper.data('prototype');
                // get the new index
                var index = $wrapper.data('index');
                // Replace '__name__' in the prototype's HTML to
                // instead be a number based on how many items we have
                var newForm = prototype.replace(/__name__/g, index);
                newForm = newForm.replace(/__INDEXOR__/, (index + 1));
                // increase the index with one for the next item
                $wrapper.data('index', index + 1);
                // Display the form in the page before the "new" link
                $wrapper.append(newForm);
                $(".image-picker").imagepicker({hide_select: true});
            });
            $('.accessories-wrapper').on('click', '.delete-accessory', function() {
                var $wrapper = $(this).closest('.accessories-fields').find('.accessories-wrapper');
                var index = $wrapper.data('index');
                $(this).closest('.accessory-container').remove();
                $wrapper.data('index', index - 1);
            });
        });

        function addModal(formId){
            $('#form-img-holder').val(formId);
        }

     function validateImage(){
        if($('.cadre-image.selected').attr('id')){
            idMedia = $('.cadre-image.selected').attr('id');
            id = idMedia.split('-')[1];
            formId = $('#form-img-holder').val();
            $('#'+formId).val(id);
            imageSrc = $('.cadre-image.selected img').attr('src');
            imageAlt = $('.cadre-image.selected img').attr('alt');
            imageHref= $('.cadre-image.selected img').data('path');
            $(".image-holder-"+formId).attr("src", imageSrc).attr("alt", imageAlt);
            $(".image-link-holder-"+formId).attr("href", imageHref).removeClass('d-none');
            $(".delete-image-"+formId).removeClass('d-none');
        }
    } 

    function deleteMedia(formId){
        $(".delete-image-"+formId).addClass('d-none');
        parent = $(this).closest('.image-container');
        $(".image-link-holder-"+formId).removeAttr("href").addClass('d-none').children(".image-holder").removeAttr("src").removeAttr("alt");
        $('#'+formId).val(null).change();
    }
    </script>
{% endblock %}
