{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <div class="mt-4 mb-4 mt-5">
        {{ form_start(form, {'method': 'POST'}) }}
        <div class="input-group mb-3">
            {{ form_widget(form.lcdv, {'attr': {'class': 'form-control'}}) }}
            <div class="input-group-append">
                {{ form_widget(form.submit, {'attr': {'class': 'btn btn-primary'}}) }}
            </div>
        </div>

        <ul class="list-unstyled text-danger">
            {% for error in form.vars.errors.form.getErrors(true) %}
                <li><b>[{{ error.origin.name }}]:</b> {{ error.message }}</li>
            {% endfor %}
        </ul>
        {{ form_end(form) }}
    </div>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans %}Liste des{% endtrans %} LCDV DS</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary">Lcdv</th>
                            <th class="text-primary">{% trans %}Date de création{% endtrans %}</th>
                            <th class="text-primary">{% trans %}Date de mise à jour{% endtrans %}</th>
                            <th class="text-primary text-right" width="20%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for ds_lcdv in ds_lcdvs %}
                        <tr>
                            <td>{{ ds_lcdv.lcdv }}</td>
                            <td>{{ ds_lcdv.creationDate ? ds_lcdv.creationDate|date('Y-m-d H:i:s') : '-' }}</td>
                            <td>{{ ds_lcdv.lastUpdate ? ds_lcdv.lastUpdate|date('Y-m-d H:i:s') : '-' }}</td>
                            <td class="text-right">
                                <a role="button" class="btn btn-warning mr-1" href="{{ path('ds_lcdv_edit', {'profile': profile.id, 'id': ds_lcdv.id}) }}">{% trans %}Modifier{% endtrans %}</a>
                                {{ include('admin/ds_lcdv/_delete_form.html.twig') }}
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div></div></div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });

    </script>
{% endblock %}
