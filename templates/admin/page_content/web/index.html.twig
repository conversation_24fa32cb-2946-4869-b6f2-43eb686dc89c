{% extends '_layout/base_back.html.twig' %}
{% block body %}
<h1 class="h3 mb-4 text-gray-800">{{ "#{pageType.type}_title" | trans }}</h1>
<div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#pagecontent-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#pagecontent-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
           <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%}active{% endif %}" id="pagecontent-app">
                       <div id="pageContent-app">
                       {% if pageType.type == "unsubscribe" %}
                            <p class="font-weight-bold" style="color:black;">{% trans %}unsubscribe_parag1{% endtrans %}</p>
                            <p style="width: 75%;">{% trans %}unsubscribe_parag2{% endtrans %}</p>
                            <p class="font-weight-bold">{% trans %}unsubscribe_parag3{% endtrans %}</p>
                        {% endif %}
                            {{ form_start(formApp, {'attr': { 'name':'myCustomFormName' }}) }}
                            {% for pageContent in formApp.pageContents %}
                                {% set language = pageContent.vars.data.language %}
                                <div class="card">
                                    <div class="card-header" id="{{'heading-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#pageContent-app">
                                        <div class="card-body">
                                            {{ form_widget(pageContent.body) }}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_page_content_index',{'profile': profile.id, 'type': pageType.type}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formApp) }}
                        </div>
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="pagecontent-web">
                        <div id="pageContent-web">
                        {% if pageType.type == "unsubscribe" %}
                            <p class="font-weight-bold" style="color:black;">{% trans %}unsubscribe_parag1{% endtrans %}</p>
                            <p style="width: 75%;">{% trans %}unsubscribe_parag2{% endtrans %}</p>
                            <p class="font-weight-bold">{% trans %}unsubscribe_parag3{% endtrans %}</p>
                        {% endif %}
                            {{ form_start(formWeb, {'attr': { 'name':'myCustomFormName' }}) }}
                            {% for pageContent in formWeb.pageContents %}
                                {% set language = pageContent.vars.data.language %}
                                <div class="card">
                                    <div class="card-header" id="{{'heading-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>
                                    <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#pageContent-web">
                                        <div class="card-body">
                                            {{ form_widget(pageContent.body) }}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_page_content_index',{'profile': profile.id, 'type': pageType.type}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>
                            {{ form_end(formWeb) }}
                        </div>
                    </div>
            </div>
        </div>
</div>
{% endblock %}
