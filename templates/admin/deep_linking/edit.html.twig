{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card">
        <div class="card-header" id="header-deeplinking">
            <h6 class="mb-0 font-weight-bold text-primary">
                <a class="float-left text-left text-decoration-none w-100" role="button" aria-expanded="true"
                    aria-controls="section-deeplinking">
                    {% trans %}update_url{% endtrans %} ({{ page.page }})
                </a>
            </h6>
        </div>
        <div id="section-deeplinking" aria-labelledby="header-deeplinking">
            <div class="overflow-auto card-body" style="overflow-y:scroll">
                {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                    {% for language in profile.site.languages %}
                        {% set formL = form['form-' ~ language.code ] %}                        
                            <div class="row mt-3">
                                <div class="col-md-3">{{ "deep_linking.desktopurl" | trans }} 
                                    <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formL.url) }}
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-3">{{ "deep_linking.full_url" | trans }} 
                                    <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                </div>
                                <div class="col-md-9 font-weight-bold">
                                    {{deeplinkingUrl}}
                                </div>
                            </div>
                    {% endfor %}
                    <div class="text-right mt-3">
                        <a class="mr-3 btn btn-dark" role="button" href="{{ path('deep_linking_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
                        <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                    </div>
                {{ form_end(form) }}            
            </div>
        </div>
    </div>
{% endblock %}
