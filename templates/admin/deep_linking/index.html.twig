{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'deep_linking.list' | trans }}</h6>
        </div>
        <div class="card-body">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs">
                        {% for language in profile.site.languages %}
                            <li class="nav-item">
                                <a class="nav-link {% if loop.first %}active{% endif %}" data-toggle="tab"
                                    href="#deeplinking-{{ language.code }}" style="min-width: 180px">
                                    <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                    <span class="m-0 font-weight-bold text-primary">{{ language.label }}</span>
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="overflow-auto" style="overflow-y:scroll;">
                    <div class="card-body">
                        <div class="tab-content">
                            {% for language in profile.site.languages %}
                                <div class="tab-pane {% if loop.first %}active{% endif %}" id="deeplinking-{{ language.code }}">
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th class="text-primary">{{ 'deep_linking.title' | trans }}</th>
                                                        <th class="text-primary">{{ 'deep_linking.url' | trans }}</th>
                                                        <th class="text-primary text-right" width="20%">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for deep_linking in deep_linkings %}
                                                        {% if deep_linking.code == language.code %}
                                                            <tr>
                                                                <td>{{ deep_linking.page}}</td>
                                                                <td>{{ deep_linking.url }}</td>
                                                                <td class="text-right">
                                                                    <a role="button" class="btn btn-warning mr-1" href="{{ path('deep_linking_edit', {'profile': profile.id, 'id': deep_linking.pageId, 'code_language': deep_linking.code}) }}">{% trans %}Modifier{% endtrans %}</a>
                                                                </td>
                                                            </tr>
                                                        {% endif %}
                                                    {% else %}
                                                        <tr>
                                                            <td colspan="4">no records found</td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('.dataTable').DataTable({
                'order': [[1, "desc" ]],
            });
        });

    </script>
{% endblock %}
