{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
   
    <div class="card shadow-sm">
        <div class="card-header">
            BTA Eligibility
        </div>
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        {{ form_errors(form.bta) }}
                        {{ form_widget(form.bta, {'attr':{'disabled': readonly}}) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        {{ form_errors(form.codeSecureEnabled) }}
                        {{ form_widget(form.codeSecureEnabled) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_errors(form.excludeStartDate) }}
                        {{ form_label(form.excludeStartDate) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.excludeStartDate, {'attr':{'readonly': readonly}}) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_errors(form.excludeEndDate) }}
                        {{ form_label(form.excludeEndDate) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.excludeEndDate, {'attr':{'readonly': readonly}}) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">

                        {{ form_label(form.codeSecureUrl) }}
                    </div>
                    <div class="col-md-6">
                        {{ form_errors(form.codeSecureUrl) }}
                        {{ form_widget(form.codeSecureUrl) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.excludeLcdvs) }}
                    </div>
                    <div class="col-md-6">
                        {{ form_errors(form.excludeLcdvs) }}
                        {{ form_widget(form.excludeLcdvs, {'attr':{'readonly': readonly}}) }}
                    </div>
                    {{ form_rest(form) }}
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
