{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <div class="card shadow mb-3">
        <div class="card">
            <a href="#connect_pack_activation" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="connect_pack_activation">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'bta.connect_pack.activation'|trans }}</h6>
            </a>
            <!-- Card Content - Collapse -->
            <div class="collapse show" id="connect_pack_activation" style="">
                <div class="card-body">
                    {{ form_start(formActivation, {'method': 'POST'}) }}
                    <div class="form-group">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form_errors(formActivation.enabled) }}
                                {{ form_widget(formActivation.enabled) }}
                             </div>
                        </div>
                    </div>  
                    <div class="mt-2 mb-4 float-right">
                        <button class="btn btn-primary float-right" type="submit">{{ 'save'|trans }}</button>
                    </div>
                    {{ form_end(formActivation) }}
                </div>
            </div>
        </div>  
    </div>
    <div class="card shadow mb-3">
        <div class="card">
            <a href="#connect_pack_ajout" class="d-block card-header py-3" data-toggle="collapse" role="button" aria-expanded="true" aria-controls="connect_pack_ajout">
                <h6 class="m-0 font-weight-bold text-primary">{{ 'bta.connect_pack.ajout'|trans }}</h6>
            </a>
            <!-- Card Content - Collapse -->
            <div class="collapse show" id="connect_pack_ajout" style="">
                <div class="card-body">
                    {{ form_start(form, {'method': 'POST'}) }}
                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-3">
                                    {{ form_errors(form.vin) }}
                                    {{ form_label(form.vin) }}
                                </div>
                                <div class="col-md-3">
                                    {{ form_widget(form.vin) }}
                                </div>
                            </div>
                        </div> 
                        <div class="mt-2 mb-4 float-right">
                            <button class="btn btn-primary float-right" type="submit">{{ 'save'|trans }}</button>
                        </div>
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'bta.connect_pack.list' | trans }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary">{{ 'bta.connect_pack.name' | trans }}</th>
                            <th class="text-primary text-right" width="20%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for connectPack in connectPacks %}
                        <tr>
                            <td>{{ connectPack['vin'] }}</td>
                            <td class="text-right">
                                <a role="button" class="btn btn-warning mr-1" href="{{ path('connect_pack_edit', {'profile': profile.id, 'id': connectPack['id']}) }}">{% trans %}Modifier{% endtrans %}</a>
                                {{ include('admin/bta/connect_pack/_delete.html.twig', {'connect_pack_id': connectPack['id'], 'connect_pack_vin': connectPack['vin']}) }}
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                'order': [[1, "desc" ]],
            });
        });

    </script>
{% endblock %}