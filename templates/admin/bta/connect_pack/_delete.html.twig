<form class="d-inline-block" method="post" action="{{ path('connect_pack_delete', {'id': connect_pack_id, 'profile' : profile.id}) }}" id="form-{{ connect_pack_id }}"
      onsubmit="return false;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ connect_pack_id) }}">
    <button class="btn btn-danger text-right" data-toggle="modal" data-target="#deleteModal{{ connect_pack_id }}">{% trans %}Supprimer{% endtrans %}</button>
</form>
<!-- Logout Modal-->
<div class="modal fade" id="deleteModal{{ connect_pack_id }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{% trans %}bta.connect_pack.dialog_title{% endtrans %} : {{ connect_pack_vin }}</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-left">{% trans %}bta.connect_pack.dialog_body{% endtrans %}</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                <button class="btn btn-danger" type="button" onclick="$('#form-{{ connect_pack_id }}').attr('onsubmit', 'return true').submit();">{% trans %}Supprimer{% endtrans %}</button>
            </div>
        </div>
    </div>
</div>