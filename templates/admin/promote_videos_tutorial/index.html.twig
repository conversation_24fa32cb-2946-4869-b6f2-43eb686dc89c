{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{{ 'promote_videos_title' | trans }}</h1>
    <div class="mb-4 text-right">
        <a role="button" class="btn btn-primary" href="{{ path('promote_video_config_add' , {'profile': profile.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
    </div>
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary">{{ 'title'|trans }}</th>
                            <th class="text-primary">{{ 'videos_id'|trans }}</th>
                            <th class="text-primary">{{ 'lcdv_model_lcdv'|trans }}</th>
                            <th class="text-primary">{{ 'Status'|trans }}</th>
                            <th class="text-primary">{{ 'priority'|trans }}</th>
                            <th class="text-primary">{% trans %}Date de création{% endtrans %}</th>
                            <th class="text-primary text-right" width="20%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for video in videos %}
                        <tr>
                            <td>{{ video.title }}</td>
                            <td>{{ video.youtube_id }}</td>
                            <td>
                                <p>
                                    {% for key, lcdvLabel in video.lcdvs %}
                                        {% if key == 10 %}
                                            <span>........</span>
                                        {% endif %}
                                        <span class="{% if key > 9 %}truncate{% endif %}">
                                            {{ lcdvLabel.lcdv }}<br />
                                        </span>
                                    {% endfor %}
                                </p>
                            </td>
                            <td class="text-center">
                                {% if video.active %}
                                    <span class="badge badge-success">{% trans %}Activé{% endtrans %}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{% trans %}Désactivé{% endtrans %}</span>
                                {% endif %}
                            </td>
                            <td>{{ video.priority }}</td>
                            <td>{{ video.created_at ? video.created_at|date('Y-m-d H:i') : '-' }}</td>
                            <td class="text-right">
                                <a role="button" class="btn btn-warning mr-1"
                                   href="{{ path('promote_video_config_edit', {'profile': profile.id, 'video': video.id}) }}"
                                >
                                    {% trans %}Modifier{% endtrans %}
                                </a>
                                <a href="#delete-video-modal" class="btn btn-danger mr-1"
                                   data-video-id="{{ video.id }}"
                                   data-toggle="tooltip" data-title="">{% trans %}Supprimer{% endtrans %}</a>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-video-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
        <form action="{{ path('promote_video_config_delete', {profile: profile.id, video: ':id'}) }}" id="delete-video-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-video') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}video_modal_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            const table = $('#dataTable').DataTable(
                {
                    "order": [[ 5, "desc" ]]
                }
            );
            table.on('draw', function () {
                $(".truncate").hide();
            });
            $(".truncate").hide();
        });

    </script>

    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-video-modal'),
                $deleteForm  = $deletekModal.find('form#delete-video-form'),
                deleteAction = $deleteForm.attr('action');

            $('#dataTable').on('click', 'a[href="#delete-video-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-video-id')));
                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
