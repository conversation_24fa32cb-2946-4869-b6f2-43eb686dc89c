{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ 'title' | trans }}
                    <span class="mandatory">*</span>
                    {% include "admin/promote_videos_tutorial/errors.html.twig" with {'errors': form.vars.errors.form.title.getErrors(true)} %}
                </div>
                <div class="col-md-5">
                    {{ form_widget(form.title) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ 'videos_id' | trans }}
                    <span class="mandatory">*</span>
                    {% include "admin/promote_videos_tutorial/errors.html.twig" with {'errors': form.vars.errors.form.youtubeId.getErrors(true)} %}
                </div>
                <div class="col-md-5">
                    {{ form_widget(form.youtubeId) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ "prdv_activation" | trans }}
                </div>
                <div class="col-md-7">
                    {{ form_widget(form.active) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ 'priority' | trans }}
                    <span class="mandatory">*</span>
                    {% include "admin/promote_videos_tutorial/errors.html.twig" with {'errors': form.vars.errors.form.priority.getErrors(true)} %}
                </div>
                <div class="col-md-5">
                    {{ form_widget(form.priority) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ 'videos_lcdv' | trans }}
                    {% include "admin/promote_videos_tutorial/errors.html.twig" with {'errors': form.vars.errors.form.lcdvs.getErrors(true)} %}
                </div>
                <div class="col-md-5">
                    {{ form_widget(form.lcdvs) }}
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('promote_video_config_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
            {% if update == true %}
                <a href="#delete-video-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-video-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
            {% endif %}
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% if update == true %}
	<div class="modal fade" id="delete-video-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
		<form action="{{ path('promote_video_config_delete', {profile: profile.id, video: id}) }}" id="delete-video-form" method="POST">
			<input type="hidden" name="token" value="{{ csrf_token('delete-video') }}"/>
			<input type="hidden" name="_method" value="DELETE">

			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="delete">Confirmation</h5>
						<button class="close" type="button" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
					<div class="modal-body">{% trans %}video_modal_delete{% endtrans %}</div>
					<div class="modal-footer">
						<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
						<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
					</div>
				</div>
			</div>
		</form>
	</div>
{% endif %}
