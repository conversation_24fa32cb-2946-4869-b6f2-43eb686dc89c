{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('eligibility_ami_settings_index',{'profile': profile.id, 'type': type}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">
                            {% if profile.site.brand == 'OP' %} 
                                {{ "o2ov_title" | trans }}
                            {% else %}
                                {{ "o2c_title" | trans }}
                            {% endif %}  
                             
                             </h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.enabled, {label_attr: {class: 'checkbox-custom '},
                                    attr: {class: 'adopter_disable', 'data-disable': 'settings_APP_adopters_enabled'} }) }}
                            </div></div>
                            <div class="row mb-4" id="adopters-config">
                                <div class="col-md-3"> {% trans %} Early_Adopters {% endtrans %}</div>
                                <div class="col-md-7">
                                    {{ form_widget(formApp.adopters_enabled ,{label_attr: {class: 'checkbox-custom'}, attr: {class: 'settings_APP_adopters_enabled'}}) }}
                                    {{ form_widget(formApp.adopters_list) }}
                                </div>
                            </div>
                             <div class="row mb-1">
                                <div class="col-md-10">
                                    <hr style="border-top: 1px solid #8c8b8b;">
                                </div>
                            </div>
                             <div class="row mb-4">
                                <div class="col-md-3"> <h5 class="text-gray-900 font-weight-bold">  {{ form_label(formApp.phone_sos) }}</h5></div>
                                <div class="col-md-7">
                                    {{ form_widget(formApp.phone_sos) }}
                                </div>
                            </div>
                        <div class="mt-2 float-right">
                            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('eligibility_ami_settings_index',{'profile': profile.id, 'type': type}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "eligibilities_o2c_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "activation" | trans }}
                            </div>

                            <div class="col-md-7">
                                {{ form_widget(formWeb.enabled ,{label_attr: {class: 'checkbox-custom '},
                                    attr: {class: 'adopter_disable', 'data-disable': 'settings_WEB_adopters_enabled'} })}}
                            </div></div>
                            <div class="row mb-4" id="adopters-config">
                            <div class="col-md-3"> {% trans %} Early_Adopters {% endtrans %}</div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.adopters_enabled,{label_attr: {class: 'checkbox-custom'}, attr: {class: 'settings_WEB_adopters_enabled'}}) }}
                                {{ form_widget(formWeb.adopters_list) }}
                            </div> </div>
                            <div class="row mb-1">
                                <div class="col-md-10">
                                    <hr style="border-top: 1px solid #8c8b8b;">
                                </div>
                            </div>
                             <div class="row mb-4">
                                <div class="col-md-3"> <h5 class="text-gray-900 font-weight-bold"> {% trans %} phone_sos {% endtrans %}</h5></div>
                                <div class="col-md-7">
                                    {{ form_widget(formWeb.phone_sos) }}
                                </div>
                            </div>
                        <div class="mt-2 float-right">
                            <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
                        </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $(".form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
            $(".adopter_disable").on('change', function() {
                $('.' + $(this).data('disable')).attr('disabled', !this.checked);
            });
            $(".adopter_disable").trigger('change');
        });
        var checkadoptersApp = function() {
            if (document.getElementById('settings_APP_adopters_enabled').checked  ) {
                document.getElementById('settings_APP_adopters_list').style.visibility = 'visible';
            } else {
                document.getElementById('settings_APP_adopters_list').style.visibility = 'hidden';
            }
        }
        checkadoptersApp();
        $('#adopters-config input').on('change', function() {
            checkadoptersApp();
        });
        var checkadoptersWeb = function() {
            if (document.getElementById('settings_WEB_adopters_enabled').checked  ) {
                document.getElementById('settings_WEB_adopters_list').style.visibility = 'visible';
            } else {
                document.getElementById('settings_WEB_adopters_list').style.visibility = 'hidden';
            }
        }
        checkadoptersWeb();
        $('#adopters-config input').on('change', function() {
            checkadoptersWeb();
        });
    </script>
{% endblock %}