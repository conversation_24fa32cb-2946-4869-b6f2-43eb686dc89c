{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <h3 class="h3 mb-4 text-gray-800">{{ 'eligibilities_o2c_edit' | trans }}</h3>
    {{ include('admin/eligibilities_ami/_form.html.twig', {'button_label': 'save', 'update': true}) }}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('.number input').keypress(function(event) {
                return event.charCode >= 48 && event.charCode <= 57;
            });
        });
    </script>
{% endblock %}