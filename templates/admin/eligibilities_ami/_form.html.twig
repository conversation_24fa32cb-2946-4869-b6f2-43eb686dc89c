{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.lcdv) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.lcdv) }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.brand) }} <span class="mandatory">*</span>
                    </div>
                    <div class="col-md-3">
                        {{ form_widget(form.brand) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('eligibility_ami_settings_index',{'profile': profile.id, 'type': type}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
            {% if update == true %}
                <a href="#delete-eligibility-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-eligibility-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
            {% endif %}
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% if update == true %}
	<div class="modal fade" id="delete-eligibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
		<form action="{{ path('eligibility_ami_settings_delete', {profile: profile.id, eligibility: eligibility, type: type}) }}" id="delete-eligibility-form" method="POST">
			<input type="hidden" name="token" value="{{ csrf_token('delete-eligibility') }}"/>
			<input type="hidden" name="_method" value="DELETE">

			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="delete">Confirmation</h5>
						<button class="close" type="button" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
					<div class="modal-body">{% trans %}êtes-vous sûr de vouloir le supprimer{% endtrans %}</div>
					<div class="modal-footer">
						<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
						<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
					</div>
				</div>
			</div>
		</form>
	</div>
{% endif %}