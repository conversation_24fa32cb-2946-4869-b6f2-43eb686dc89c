<form class="d-inline-block" method="post" action="{{ path('deep_linking_page_delete', {'id': deep_linking_page.id, 'profile' : profile.id}) }}" id="form-{{ deep_linking_page.id }}"
      onsubmit="return false;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ deep_linking_page.id) }}">
    <button class="btn btn-danger text-right" data-toggle="modal" data-target="#deleteModal{{ deep_linking_page.id }}">{% trans %}Supprimer{% endtrans %}</button>
</form>
<!-- Logout Modal-->
<div class="modal fade" id="deleteModal{{ deep_linking_page.id }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{% trans %}deep_linking_page.dialog_title{% endtrans %} : {{ deep_linking_page.page }}</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-left">{% trans %}deep_linking_page.dialog_body{% endtrans %}</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                <button class="btn btn-danger" type="button" onclick="$('#form-{{ deep_linking_page.id }}').attr('onsubmit', 'return true').submit();">{% trans %}Supprimer{% endtrans %}</button>
            </div>
        </div>
    </div>
</div>
