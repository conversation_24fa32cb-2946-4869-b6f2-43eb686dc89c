{% extends '_layout/base_back.html.twig' %}
{% block body %}
    <div class="card">
        <div class="card-header" id="header-{{ section }}">
            <h6 class="mb-0 font-weight-bold text-primary">
                <a class="float-left text-left text-decoration-none w-100" role="button" aria-expanded="true"
                    aria-controls="section-{{ section }}">
                    SHOP
                </a>
            </h6>
        </div>
        <div id="section-{{ section }}" aria-labelledby="header-{{ section }} " style="visibility: hidden;">
            <div class="overflow-auto card-body" style="overflow-y:scroll">
                {{ form_start(form, { 'attr': {'class': 'mt-1'}}) }}
                    <div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.enabled) }}
                            </div>
                        </div>
                    </div>

                    <div id="section-Eboutique" style="display: none;">
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.url_eboutique' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.UrlEboutique) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation_connected_packs' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.ActivationServiceConnectPacks) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation_navco' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.ActivationServiceNavco) }}
                            </div>
                        </div>
                    </div>

                    <div id="section-Sams" style="display: none;">
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation_nav' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.ActivationConnectedNav) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.url_nav' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.UrlConnectedNav) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation_zar' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.ActivationZar) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.url_zar' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.UrlZar) }}
                            </div>
                        </div>

                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.activation_tmts' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.ActivationTmts) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.url_tmts' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.UrlTmts) }}
                            </div>
                        </div>
                        <div class="row mb-5">
                            <div class="col-md-3">{{ 'shop.subscription_url' | trans }}</div>
                            <div class="col-md-4 ">
                                {{ form_widget(form.SubscriptionUrl) }}
                            </div>
                        </div>
                    </div>
                    <div class="text-right mt-3">
                        <button class="btn btn-primary" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
                    </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        
        $(document).ready(function () {

        var section = document.getElementById('shop_parameters_enabled');
        var rad = section.getElementsByClassName('form-check');

        for (var i = 0; i < rad.length; i++) {
  
        var input = rad[i].getElementsByClassName('form-check-input');
        selected = input[0].value
        console.log(input[0].checked)
        var sectionEboutique = document.getElementById('section-Eboutique');
        var sectionSams = document.getElementById('section-Sams');
        if (selected == "Eboutique" && input[0].checked){
            sectionEboutique.style.display = "block";
            sectionSams.style.display = "none";
        }

        if (selected == "Sams" && input[0].checked){
            sectionEboutique.style.display = "none";
            sectionSams.style.display = "block";
        }

        if (selected == "Inactif" && input[0].checked){
            sectionEboutique.style.display = "none";
            sectionSams.style.display = "none";
        }
        };

        document.getElementById('section-SHOP').style.visibility = "visible";


        
});
    </script>

    <script>
        var section = document.getElementById('shop_parameters_enabled');
        var rad = section.getElementsByClassName('form-check');
        var prev = null;

        for (var i = 0; i < rad.length; i++) {
    rad[i].addEventListener('change', function() {
        var input = this.getElementsByClassName('form-check-input');
        selected = input[0].value
        var sectionEboutique = document.getElementById('section-Eboutique');
        var sectionSams = document.getElementById('section-Sams');
        if (selected == "Eboutique"){
            sectionEboutique.style.display = "block";
            sectionSams.style.display = "none";
        }

        if (selected == "Sams"){
            sectionEboutique.style.display = "none";
            sectionSams.style.display = "block";
        }

        if (selected == "Inactif"){
            sectionEboutique.style.display = "none";
            sectionSams.style.display = "none";
        }
    });
}
    </script>

{% endblock %}