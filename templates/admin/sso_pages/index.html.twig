{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{{ 'sso_urls_title' | trans }}</h1>
    <div class="card shadow mb-4">
        <div class="card-body">
            {% if allowed %}
                {{ form_start(form, {'attr': {'class': 'mb-2'}}) }}
                    <div class="row">
                        <div class="col-md-2 mt-2">
                            <div class="form-group">
                                <h5>{{ 'change_sso_base_url' | trans }}</h5>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form_widget(form.url) }}
                            </div>
                        </div>
                        <div class="col-md-1 text-right">
                            <button class="btn btn-info" type="submit">{{ button_label|default('save') |trans }}</button>
                        </div>
                    </div>
                {{ form_end(form) }}
            {% endif %}
            <div class="table-responsive">
                <table id="social-networks-table" class="table table-bordered table-hover">
                    <thead>
                    <tr class="text-primary">
                        <th>{{ 'title' | trans }}</th>
                        <th>{{ 'sso_url' | trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for sso_url in sso_urls %}
                        <tr>
                            <td>{{ sso_url.title }}</td>
                            <td>{{ sso_url.url }}</td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% trans %}empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });

    </script>
{% endblock %}
