{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('apv_services_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        {% for service in services %}
                            <div class="mb-3">
                                <h5 class="text-gray-900 font-weight-bold">{{ service  | trans }}</h5>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    {{ "prdv_activation" | trans }}
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formApp[service~'_enabled']) }}
                                </div>
                            </div>
                        {% endfor %}
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('apv_services_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        {% for service in services %}
                            <div class="mb-3">
                                <h5 class="text-gray-900 font-weight-bold">{{ service  | trans }}</h5>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    {{ "prdv_activation" | trans }}
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formWeb[service~'_enabled']) }}
                                </div>
                            </div>
                        {% endfor %}
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right">
        <button id="form_submitor" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $("#form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            })
        });
    </script>
{% endblock %}
