{% extends '_layout/base_back.html.twig' %}

{% block body %}
    {{ form_start(form, {'action': path('settings_configuration_index',{'profile': profile.id}), 'method': 'POST'}) }}
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{{ 'Settings Configuration'|trans }}</h6>
        </div>
        <div class="overflow-auto card-body">
            <div id="settings_configuration">
                <div class="card-body">
                    {% for functionality in functionalities %}
                        <div class="row mb-3">
                            <div class="col-md-4">
                                {{ functionality.functionality | trans }}
                            </div>
                            <div class="col-md-3" style="">
                                {{ form_widget(form[functionality.functionality]) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-10">
                                <hr style="border-top: 1px solid #8c8b8b;">
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            <div class="mt-2 float-right">
                <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
        </div>
    </div>

    {{ form_end(form) }}
{% endblock %}