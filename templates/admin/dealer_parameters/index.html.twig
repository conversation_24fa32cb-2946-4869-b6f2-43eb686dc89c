{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab" href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab" href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('dealer_settings_index',{'profile': profile.id}), 'method': 'POST'}) }}

                    <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "dealer_settings_menu" | trans }}</h5>
                        </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            {{ "activation" | trans }}
                        </div>
                        <div class="col-md-3">
                            {{ form_widget(formApp.dealers) }}
                        </div>
                    </div>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                {{ "dealer_radius" | trans }}
                            </div>
                            <div class="col-md-3 number">
                                {{ form_widget(formApp.radius) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                {{ "dealer_max" | trans }}
                            </div>
                            <div class="col-md-3 number">
                                {{ form_widget(formApp.max_results) }}
                            </div>
                        </div>
                        <div class="row mb-4" style="display: none">
                            <div class="col-md-3">
                                {{ form_widget(formApp.near_dealers) }}
                            </div>
                        </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "dealer_settings_filters_title" | trans }}</h5>
                       </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            {{ "Criterias" | trans }}
                        </div>
                        <div class="col-md-5">
                            {{ form_widget(formApp.criterias_apv) }}
                        </div>
                    </div>
                    <div class="row mb-4">
                            <div class="col-md-4">
                                {{ "filter_services" | trans }}</div>
                     <div class="col-md-4 filter_dealers_app" style="font-size: 13.7px">
                         {% set data = formApp.filter_dealers.vars.choices %}
                         {% for label,datum in data %}
                           <h6 class="text-gray-900 font-weight-bold"> {{ label }} </h6>
                             {% for key,choice in datum %}
                             {{ form_widget(formApp.filter_dealers[key],{label_attr: {class: 'checkbox-custom checkbox-inline'}}) }}<br/>
                             {% endfor %}
                         {% endfor %}
                     </div>
                    </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "favorite_dealer_label" | trans }}</h5>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            {{ "activation" | trans }}
                        </div>
                        <div class="col-md-7">
                            {{ form_widget(formApp.favorite_dealer) }}

                        </div>
                    </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('dealer_settings_index',{'profile': profile.id}), 'method': 'POST'}) }}
                    <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "dealer_settings_menu" | trans }}</h5>
                        </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            {{ "activation" | trans }}
                        </div>
                        <div class="col-md-3">
                            {{ form_widget(formWeb.dealers) }}
                        </div>
                    </div>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                {{ "dealer_radius" | trans }}
                            </div>
                            <div class="col-md-3 number">
                                {{ form_widget(formWeb.radius) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                {{ "dealer_max" | trans }}
                            </div>
                            <div class="col-md-3 number">
                                {{ form_widget(formWeb.max_results) }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "dealer_settings_near_title" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                {{ form_label(formWeb.near_dealers) }}
                            </div>
                            <div class="col-md-3 number_limit">
                                {{ form_widget(formWeb.near_dealers) }}
                            </div>
                        </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "dealer_settings_filters_title" | trans }}</h5>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            {{ "Criterias" | trans }}
                        </div>
                        <div class="col-md-5">
                            {{ form_widget(formWeb.criterias_apv) }}
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            {{ "filter_services" | trans }}
                        </div>
                        <div class="col-md-4 filter_dealers_web" style="font-size: 13.7px">
                            {% set data = formWeb.filter_dealers.vars.choices %}
                            {% for label,datum in data %}
                            <h6 class="text-gray-900 font-weight-bold">  {{ label }} </h6>
                                {% for key,choice in datum %}
                                {{ form_widget(formWeb.filter_dealers[key],{label_attr: {class: 'checkbox-custom checkbox-inline'}}) }}<br/>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "favorite_dealer_label" | trans }}</h5>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            {{ "activation" | trans }}
                        </div>
                        <div class="col-md-7">
                            {{ form_widget(formWeb.favorite_dealer) }}
                        </div>
                    </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right">
        <button id="form_submitor" class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            if (! $('#parameters-web .number_limit input').val()) {
                $('#parameters-web .number_limit input').val(5)
            }
            $('.number input').keypress(function(event) {
                return event.charCode >= 48 && event.charCode <= 57;
            });
            $('.number_limit input').keypress(function(event) {
                return event.charCode >= 49 && event.charCode <= 53;
            });
            
            document.getElementById('dealer_parameters_WEB_criterias_apv').checked = true;
            document.getElementById('dealer_parameters_APP_criterias_apv').checked = true;
            document.getElementById('dealer_parameters_WEB_criterias_apv').disabled = true;
            document.getElementById('dealer_parameters_APP_criterias_apv').disabled = true;


            if($(".filter_dealers_app input:checked").length > 5)
            {
                $(".filter_dealers_app input:checkbox").not(":checked").attr("disabled",true);
            }
            var maxchecked_app =function(){

                $(".filter_dealers_app input:checkbox").click(function() {
                    var bol = $(".filter_dealers_app input:checked").length >= 6;
                    $(".filter_dealers_app input:checkbox").not(":checked").attr("disabled",bol);
                });
            }
            maxchecked_app();
            $('.filter_dealers_app input').on('change', function() {
                maxchecked_app();
            });

            if($(".filter_dealers_web input:checked").length > 5)
            {
                $(".filter_dealers_web input:checkbox").not(":checked").attr("disabled",true);
            }
            var maxchecked_web =function(){

                $(".filter_dealers_web input:checkbox").click(function() {
                    var bol = $(".filter_dealers_web input:checked").length >= 6;
                    $(".filter_dealers_web input:checkbox").not(":checked").attr("disabled",bol);
                });
            }
            maxchecked_web();
            $('.filter_dealers_web input').on('change', function() {
                maxchecked_web();
            });
            $("#form_submitor").on('click', function() {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id+" form").submit();
            });
        });
    </script>
{% endblock %}
