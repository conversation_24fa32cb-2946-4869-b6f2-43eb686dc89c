{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
	<link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
	<div class="card shadow mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto">
            <div class="card-body">
                <div class="tab-content">
                    <h6 class="mb-4 font-weight-bold text-primary">{{ 'functionnalities.functionnalities_title' | trans }}</h6>
                    <div class="tab-pane {% if source == 'APP'%} active {% endif %}" id="wsparameters-app">
                        {% include "admin/fonctionnalities/functionnalities_table.html.twig" with {'data': dataApp } %}
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                        {% include "admin/fonctionnalities/functionnalities_table.html.twig" with {'data': dataWeb } %}
                    </div>
              </div>
           </div>
      </div>
</div>

{% endblock %}
{% block javascripts %}
{{ parent() }}<script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
<script src="{{ asset('js/datatables/dataTables.buttons.min.js') }}"></script>
<script src="{{ asset('js/datatables/buttons.html5.min.js') }}"></script>
<script>
	$(document).ready(function () {
       var date= {{"now"|date("YmdHi")}}
$('.dataTable').DataTable({
        "pageLength": 100,
        dom: '<"row"<"col-md-6"l><"col-md-6"f>>rtipB', 
        buttons: [
           {
            extend: 'csv',
            text: 'Exporter',
            charset: 'utf-8',
            extension: '.csv',
            fieldSeparator: ';',
            fieldBoundary: '',
            filename: 'MYM_{{brand}}_PARAMS_'+date,
            bom: true,
            className: 'btn-primary'
           }
        ],
        
    });

});
</script>{% endblock %}
