<div class="table-responsive">
	<table class="table table-bordered dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
		<thead>
			<tr>
				<th>&nbsp;</th>
				{% for country in data['countries'] %}
					<th class="text-primary">{{ country }}</th>
				{% endfor %}
			</tr>
		</thead>
		<tbody>
			{% for defaultFunctionnality in data['defaultFunctionnalities'] %}
				<tr>
					<td class="text-primary">{{ ('functionnalities.'~ defaultFunctionnality['typeService']) | trans }}</td>
					{% for country in data['countries'] %}
						<td>
							{% if data['functionnalities'][country][defaultFunctionnality['typeService']]['enabled'] is defined
                                                        and  data['functionnalities'][country][defaultFunctionnality['typeService']]['enabled'] == true
                                                    %}
								{% set title = '&#10004;' %}
								{% if data['functionnalities'][country][defaultFunctionnality['typeService']]['params'] is defined %}
									{% set title = title ~ data['functionnalities'][country][defaultFunctionnality['typeService']]['params'][0]['value'] %}
								{% endif %}
								<span class="text-success">
									{{title | raw}}
								</span>
							{% else %}
								<span class="text-danger">
									&#10008;
								</span>
							{% endif %}
						</td>
						{% set title = '' %}
					{% endfor %}
				</tr>
			{% endfor %}
		</tbody>
	</table>
	<div class="croiselib mt-4">Légendes :</div>
	<div>
		<span class="text-success">&#10004;</span>
		<span>
			:
			{{'functionnalities.legends.functionnality_active' | trans}}</span>
	</div>
	<div>
		<span class="text-success">&#10004; C</span>
		<span>
			:
			{{'functionnalities.legends.functionnality_active_prdv_solution_central' | trans}}</span>
	</div>
	<div>
		<span class="text-success">&#10004; L</span>
		<span>
			:
			{{'functionnalities.legends.functionnality_active_prdv_solution_local' | trans}}</span>
	</div>
	<div>
		<span class="text-danger">&#10008;</span>
		<span>
			:
			{{'functionnalities.legends.functionnality_disabled' | trans}}</span>
	</div>
	<div>
		<span class="text-warning">EA</span>
		<span>
			:
			{{'functionnalities.legends.early_adapter' | trans}}</span>
	</div>
</div>

