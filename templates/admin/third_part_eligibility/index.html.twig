{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
        <div class="card shadow mb-4">
        <div class="card-body">
            {{ form_start(form, {}) }}
            <div class="mb-3">
                <h1 class="h3 mb-4 text-gray-800">{% trans %}Eligibility {% endtrans %} -  {{ type | upper }}</h1>
            </div>
            <div class="row mb-4">
                <div class="col-md-3">
                    {{ "activation" | trans }}
                </div>
                <div class="col-md-7">
                    {{ form_widget(form.enabled) }}
                </div>
            </div>
            <div class="mt-2 float-right">
                <button class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>
    <div class="card shadow mb-4">
        <div class="card-header">
            {{type}} Eligibility
            <a href="{{path('third_part_eligibilities_add', {'type':type, 'profile': profile.id})}}" class="btn btn-primary float-right">Ajouter</a>
        </div>
        <div class="card-body">
            {{form_start(form)}}
                {{ form_widget(form) }}
            {{form_end(form)}}
            <div class="table-responsive">
                <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="text-primary">{{ 'model'|trans }}</th>
                            <th class="text-primary">{{ 'lcdv_model_lcdv'|trans }}</th>
                            <th class="text-primary">{% trans %}startDate{% endtrans %}</th>
                            <th class="text-primary">{% trans %}endDate{% endtrans %}</th>
                            <th class="text-primary text-center" width="10%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for vehicle_label in models %}
                        <tr>
                            <td>{{ vehicle_label.label }}</td>
                            <td>{{ vehicle_label.lcdv }}</td>
                            <td>{{ vehicle_label.start_date is defined ? vehicle_label.start_date|date('Y-m-d') : '-' }}</td>
                            <td>{{ vehicle_label.end_date  is defined ? vehicle_label.end_date|date('Y-m-d') : '-' }}</td>
                            <td width="15%">
                                <a role="button" class="btn btn-sm btn-warning mr-1"
                                   href="{{ path('third_part_eligibilities_edit', {'profile': profile.id, 'type': type ,'id': vehicle_label.id}) }}"
                                ><span class="fa fa-pen"></a>
                                <a href="#delete-eligibility-modal" class="btn btn-sm btn-danger"
                                   data-eligibility-id="{{ vehicle_label.id }}"
                                   data-toggle="tooltip" data-title=""><span class="fa fa-trash" aria-hidden="true"></a>
                            </td> 
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4">no records found</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer"></div>
    </div>
{% endblock %}
{% block modals %}
<!-- DELETE -->
<div class="modal fade" id="delete-eligibility-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
    <form action="{{ path('third_part_eligibilities_delete', {'type':type, profile: profile.id, id: ':id'}) }}" id="delete-eligibility-form" method="POST">
        <input type="hidden" name="token" value="{{ csrf_token('delete-eligibility') }}"/>
        <input type="hidden" name="_method" value="DELETE">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="delete">Confirmation</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">{% trans %}Etes-vous sûr(e) de vouloir supprimer cette véhicule{% endtrans %} ?</div>
                <div class="modal-footer">
                    <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                    <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <!-- DELETE SOCIAL NETWORK -->
    <script>
        window.$(function () {
            var $deletekModal = window.$('div#delete-eligibility-modal'),
                $deleteForm  = $deletekModal.find('form#delete-eligibility-form'),
                deleteAction = $deleteForm.attr('action');

            window.$('a[href="#delete-eligibility-modal"]').on('click', function (event) {
                event.preventDefault();

                $deleteForm.attr('action', deleteAction.replace(':id', window.$(this).attr('data-eligibility-id')));

                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
    </script>
{% endblock %}
