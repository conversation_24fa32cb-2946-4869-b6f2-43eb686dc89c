{% extends '_layout/base_back.html.twig' %}

{% block body %}
{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
   
    <div class="card shadow-sm">
        <div class="card-header">
            {{type}} Eligibility
        </div>
        <div class="card-body">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_errors(form.label) }}
                        {{ form_label(form.label) }}
                    </div>
                    <div class="col-md-6">
                        {{ form_errors(form.label) }}
                        {{ form_widget(form.label) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.lcdv) }}
                    </div>
                    <div class="col-md-6">
                        {{ form_errors(form.lcdv) }}
                        {{ form_widget(form.lcdv) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.startDate) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_errors(form.startDate) }}
                        {{ form_widget(form.startDate) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-md-3">
                        {{ form_label(form.endDate) }}
                    </div>
                    <div class="col-md-3">
                        {{ form_errors(form.endDate) }}
                        {{ form_widget(form.endDate) }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="row">
                    {{ form_rest(form) }}
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% endblock %}