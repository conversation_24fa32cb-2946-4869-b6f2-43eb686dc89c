{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
	<link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
	<style type="text/css">
		.thumbnails li img {
			width: 180px;
			height: 180px;
		}
	</style>
{% endblock %}
{% block body %}

	<div class="card shadow mb-4">
		<div class="card-header">{% trans %}evrouting_image_title{% endtrans %}</div>
		<div class="card-body">
			<div class="row">
				<div class="col-2">
					{% trans %}evrouting_icon_label{% endtrans %}
				</div>
				<div class="col-9">
					{% if mediaService %}
						<a href="{{ mediaUrl ~ '/' ~ mediaService.media.path }}" target="_blank">
							<img style="max-height: 60px; border: 1px solid;" src="{{ mediaUrl ~ '/' ~ mediaService.media.path }}" alt="{{ mediaService.media.textAlt }}">
						</a>
					{% endif %}
					<a href="#select-media-modal" class="btn btn-success ml-4 mr-3" data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
					{% if mediaService %}
						<a href="#delete-media-modal" data-toggle="modal" class="btn btn-danger">{% trans %}Supprimer{% endtrans %}</a>
					{% endif %}
				</div>
			</div>
		</div>
	</div>

{% endblock %}

{% block modals %}
	{{ include('admin/ev_routing_icon/modals/add.html.twig') }}
	{{ include('admin/ev_routing_icon/modals/delete.html.twig') }}
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	<script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
	<script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
	<script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
	<script>
		$(function () {
$(".image-picker").imagepicker()
});
	</script>
{% endblock %}
