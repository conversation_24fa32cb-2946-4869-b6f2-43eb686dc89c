{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#list_models_tab">
                    {{ 'vehicle_labels' | trans }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#default_image_tab">
                    {{'vehicle_default_image_label'|trans }}
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;height: 600px">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane active" id="list_models_tab">
                        <h1 class="h3 mb-4 text-gray-800">{{ 'vehicle_labels' | trans | upper }}</h1>

                        <div class="card shadow mb-4">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="width: 100%;">
                                        <thead>
                                            <tr>
                                                <th class="text-primary">{{ 'short_label'|trans }}</th>
                                                <th class="text-primary">{{ 'lcdv_model_lcdv'|trans }}</th>
                                                {% if(brand=='OP' or brand=='VX') %}
                                                <th class="text-primary">{{ 'rpo_model_rpo'|trans }}</th>
                                                {% endif %}
                                                <th class="text-primary">{% trans %}Date de création{% endtrans %}</th>
                                                <th class="text-primary">{% trans %}Date de mise à jour{% endtrans %}</th>
                                                <th class="text-primary text-center" width="10%">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% for vehicle_label in vehicle_labels %}
                                            <tr>
                                                <td>{{ vehicle_label.label }}</td>
                                                <td>
                                                    {% for lcdvLabel in vehicle_label.lcdvs %}
                                                        {{ lcdvLabel.lcdv }} <br />
                                                    {% endfor %}
                                                </td>
                                                {% if(brand=='OP'or brand=='VX') %}
                                                    <td>
                                                        {% for rpoLabel in vehicle_label.rpos %}
                                                            {{ rpoLabel.rpo }} <br />
                                                        {% endfor %}
                                                    </td>
                                                {% endif %}
                                                <td>{{ vehicle_label.createdAt ? vehicle_label.createdAt|date('Y-m-d H:i') : '-' }}</td>
                                                <td>{{ vehicle_label.updatedAt ? vehicle_label.updatedAt|date('Y-m-d H:i') : '-' }}</td>
                                                <td class="text-center">
                                                    <a role="button" class="btn btn-warning mr-1"
                                                       href="{{ path('local_model_label_edit', {'profile': profile.id, 'label': vehicle_label.id}) }}"
                                                    >
                                                        {% trans %}Modifier{% endtrans %}
                                                    </a>
                                                </td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="4">no records found</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" id="default_image_tab">
                        <div class="card">
                            <div class="card-body">
                                <label>{{'vehicle_default_image_label'|trans }} </label> 

                                <img style="max-height: 60px; border: 1px solid;" src="{{ defaultImage ? mediaUrl ~ '/' ~ defaultImage.media.path }}" alt="{{ defaultImage ? defaultImage.media.textAlt }}" id="lcdv_preview_defaultGlobalImage" class="m-2"> 
                            </div>
                             <div class="card-footer "></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });

    </script>
{% endblock %}
