{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <style type="text/css">
        .thumbnails li img {
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}
{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}local_lcdv_update{% endtrans %}</h1>
    {{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
        <div class="card shadow-sm">
            <div class="card-body">
                {% for index, label in form.labels %}
                    <div class="form-group">
                        <div class="row">
                            <div class="col-md-3">
                                {% set language = label.vars.value.language %}
                                {{ 'lcdv_model_label' | trans | upper }} {{ language.code | upper }}
                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                            </div>
                            <div class="col-md-5">
                                {{ form_widget(label.localLabel) }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-3">
                            {{ 'lcdv_model_lcdv' | trans | upper }}
                        </div>
                        <div class="col-md-5">
                            <textarea class="form-control" disabled="disabled" rows="5" class="form-control">{{ label.stringLcdvs }}</textarea>
                        </div>
                    </div>
                </div>
                {% if(brand=='OP' or brand=='VX') %}
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-3">
                            {{ 'rpo_model_rpo' | trans | upper }}
                        </div>
                        <div class="col-md-5">
                            <textarea class="form-control" disabled="disabled" rows="5" class="form-control">{{ label.stringRpos }}</textarea>
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-3">
                           LCDV example
                        </div>
                        <div class="col-md-5">
                           <span id="lcdvExampleSpan"> {{ label.lcdvExample }}</span>
                        </div>
                    </div>
                </div>
                {{include('admin/model_label/model_visual_form_part.html.twig', {'model': model, 'lcdvExample': label.lcdvExample})}}
            </div>
            <div class="card-footer text-right">
                <a class="mr-3 btn btn-dark" role="button" href="{{ path('local_model_label_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                <button class="btn btn-success">{{ button_label|default('save') |trans }}</button>
            </div>
        </div>
    {{ form_end(form) }}

{{ include('admin/model_label/media_modal.html.twig',{'prefix': 'defaultImage'}) }}
{{ include('admin/model_label/v3d_modal.html.twig') }}

{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/image-picker/image-picker.js') }}"></script>
    <script src="{{ asset('js/app.js') }}"></script>
{% endblock %}
