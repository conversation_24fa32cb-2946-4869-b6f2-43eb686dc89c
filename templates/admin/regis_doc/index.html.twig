{% extends '_layout/base_back.html.twig' %}

{% block stylesheets %}
    <link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <style type="text/css">
        .thumbnails li img{
            width: 180px;
            height: 180px;
        }
    </style>
{% endblock %}

{% block body %}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message | trans }}
        </div>
    {% endfor %}
    {% for message in app.flashes('error') %}
        <div class="alert alert-danger">
            {{ message | trans }}
        </div>
    {% endfor %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <!-- Card Header - Dropdown -->
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ 'regis_doc_image_header'|trans }}</h6>
                </div>
                <!-- Card Body -->
                <div class="card-body">
                    <div class="alert alert-warning">
                         {% trans %}
                            regis_doc_help_label
                         {% endtrans %}
                    </div>
                    <div class="col-9">
                        {% if mediaService %}
                            {% set extension = mediaService.media.extension|lower %}
                            <a href="{{ mediaUrl ~ '/' ~ mediaService.media.path }}" target="_blank">
                                <img style="max-height: 60px; border: 1px solid;" src="{{ extension == 'pdf' ? asset('images/pdf-icon.png') : mediaUrl ~ '/' ~ mediaService.media.path }}" alt="{{ mediaService.media.textAlt }}">
                            </a>
                        {% endif %}
                        <a href="#select-media-modal" class="btn btn-success ml-4 mr-3"  data-toggle="modal">{% trans %}Ajouter{% endtrans %}</a>
                        {% if mediaService %}
                            <a href="#delete-media-modal" data-toggle="modal" class="btn btn-danger">{% trans %}Supprimer{% endtrans %}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block modals %}
    <!-- DELETE MEDIA -->
    {% if mediaService %}
        <div class="modal fade" id="delete-media-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
            <form action="{{ path('admin_regis_doc_delete', {profile: profile.id}) }}" id="delete-media-form" method="POST">
                <input type="hidden" name="token" value="{{ csrf_token('delete-media-token') }}"/>
                <input type="hidden" name="_method" value="DELETE">

                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="delete">Confirmation</h5>
                            <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">{{ 'regis_doc_image_delete_confirmation'|trans }}</div>
                        <div class="modal-footer">
                            <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                            <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {% endif %}

    <!-- CHOOSE MEDIA -->
    <div class="modal fade" id="select-media-modal" tabindex="-1" role="dialog" aria-labelledby="select" aria-hidden="true">
        <form id="form_regis_doc_image" action="{{ path('admin_regis_doc_add', {'profile': profile.id}) }}" method="POST">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">{{ 'regis_doc_image_select_header'|trans }}</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                    <input type="hidden" name="regis_doc_image" id="selected_img">
                            {{ render(controller('App\\Controller\\Bo\\Admin\\MediasController::mediasTreeAction',
                                {   'profile': profile,
                                    'hideBtn': 'true'
                                }
                              )) }}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-primary" type="submit" onclick="saveMedia()">{% trans %}Valider{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    {{ include('admin/medias/js/js.html.twig', { 'hideBtn': 'true' }) }}
    <script>
        function saveMedia(){
            imageId= $("#selected_img").val();
            $('#select_regis_doc_image').val(imageId);
            $('#form_regis_doc_image').submit();
        }
    </script>
{% endblock %}
