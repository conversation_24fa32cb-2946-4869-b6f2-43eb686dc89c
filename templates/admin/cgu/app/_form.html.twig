<div class="card shadow">
    <div class="overflow-auto" style="overflow-y:scroll;">
    	<div class="card-header text-primary">{{title|default('new_cgu_version'|trans)}}</div>
        <div class="card-body" id="cgus-app">
            {{form_start(form)}}
            {% if form_errors(form) %}
                <span class="alert alert-danger">{{form_errors(form)}}</span>
            {% endif %}
            {{form_row(form.enabled)}}
            <span id="version_error_span" class="text-danger"></span>
            {{form_row(form.title)}}
            {{form_row(form.version)}}
            {% for cgu in form.cguTranslations %}
                {% set language = cgu.vars.data.language %}
                <div class="card">
                    <div class="card-header" id="{{'heading-' ~ language.code }}">
                        <h6 class="mb-0">
                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                               href="#section-{{ language.code }}" role="button" aria-expanded="false"
                               aria-controls="section-{{ language.code }}">
                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                {{ language.label }}
                            </a>
                        </h6>
                    </div>

                    <div id="{{ 'section-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-' ~ language.code }}" data-parent="#cgus-app">
                        <div class="card-body">
                            {{ form_errors(cgu.content) }}
                            {{ form_widget(cgu.content) }}
                        </div>
                    </div>
                </div>
             {% endfor %}
             {{form_rest(form)}}
             <button class="btn btn-success m-2 float-right">{{'save' | trans}}</button>
             
            {{form_end(form)}}
        </div>
    </div>
</div>
