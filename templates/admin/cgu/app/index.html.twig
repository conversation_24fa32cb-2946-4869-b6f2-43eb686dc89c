<a href="{{path('admin_cgu_index', {'profile': profile.id})}}#form_area" id="new_cgu_v_btn" class="btn-sm btn btn-primary float-right m-1"> <i class="fas fa-plus"></i> {{'new_cgu_version'|trans }}</a>
<h5>CGUs:</h5>
<div class="table-responsive">
    <table class="table table-bordered" id="cguTable" width="100%" cellspacing="0" role="grid"
        aria-describedby="dataTable_info" style="width: 100%;">
        <thead>
            <tr>
                <th class="text-primary" width="10%">{{ 'title' | trans }}</th>
                <th class="text-primary" width="10%">{{ 'version_app' | trans }}</th>
                <th class="text-primary" width="15%">{% trans %}Date de création{% endtrans %}</th>
                <th class="text-primary" width="15%">{% trans %}update_date{% endtrans %}</th>
                <th class="text-primary" width="15%">{% trans %}published_date{% endtrans %}</th>
                <th class="text-primary" width="10%">{{ "published" | trans | capitalize }}</th>
                <th class="text-primary" width="35%">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for cgu in cgus %}
                {{ include('admin/cgu/app/_cgu_row.html.twig', {'cgu':  cgu, 'profile': profile}) }}
            {% endfor %}
        </tbody>
    </table>
</div>
 {% set title = cgu is defined and cgu is not empty ? 'Edit CGU '~ cgu.title : '' %}
<div id="form_area">
{{ include('admin/cgu/app/_form.html.twig', {'title':  title}) }}
</div> 
