{% set currentAppVersion = cgu.version ? cgu.version.version : "DEFAULT" %}
{% set currentAppVersionId = cgu.version ? cgu.version.id : null %}
<tr id="tr-cgu-{{ cgu.id }}" {% if cgu.enabled %} data-enabled=1 data-app-version={{ currentAppVersionId }} {% endif %}>
    <td>{{ cgu.title }}</td>
    <td>{{ currentAppVersion }}</td>
    <td>{{ cgu.creationDate|date('d-m-Y H:i:s') }}</td>
    <td>{{ cgu.lastUpdate|date('d-m-Y H:i:s') }}</td>
    <td class="p-date">{{ cgu.publishedDate ? cgu.publishedDate|date('d-m-Y H:i:s') : '-' }}</td>
    <td class="cgu-enabled-td app-version-{{ currentAppVersionId }}"><i class='{{ cgu.enabled ? "fas fa-check text-success" : "fa fa-ban text-danger"}}'></i></td>
    <td> 
        <a href="{{path('admin_cgu_index', {'profile': profile.id, 'id':cgu.id})}}#form_area" class="btn btn-warning btn-sm edit_cgu_v_btn">{{ 'modifier'|trans }}</a>
        <a href="#delete-cgu-modal" class="btn btn-danger btn-sm" data-toggle="modal" data-cgu-id="{{cgu.id}}">{{ 'supprimer'|trans }}</a>
    </td>
</tr>