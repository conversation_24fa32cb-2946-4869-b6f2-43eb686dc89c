{% extends '_layout/base_back.html.twig' %}
{% block stylesheets %}
    <link href="{{ asset('css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
    <style type="text/css">
    @keyframes highlight {
    0% {
        background: #4bb54347; 
    }
        100% {
            background: none;
        }
    }

    .highlight {
        animation: highlight 2s;
    }
    </style>
{% endblock %}
{% block body %}
    <h1 class="h3 mb-4 text-gray-800">{% trans %}CGU{% endtrans %}</h1>

    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == 'APP'%}active{% endif %}" data-toggle="tab" href="#wsparameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == 'WEB'%}active{% endif %}" data-toggle="tab" href="#wsparameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="overflow-auto" style="overflow-y:scroll;">
            <div class="card-body">
                <div class="tab-content">
                    <div class="tab-pane {% if source == 'APP'%}active{% endif %}" id="wsparameters-app">
                    {{ render(controller(
                        'App\\Controller\\Bo\\Admin\\CguAppController::index',
                        {'profile':profile.id, 'id': cgu ? cgu.id}
                    )) }}
                    </div>
                    <div class="tab-pane {% if source == 'WEB'%}active{% endif %}" id="wsparameters-web">
                        <div id="cgus-web">
                            {{ form_start(formWeb) }}

                            {% for cgu in formWeb.cgus %}
                                {% set language = cgu.vars.data.language %}
                                <div class="card">
                                    <div class="card-header" id="{{'heading-web-' ~ language.code }}">
                                        <h6 class="mb-0">
                                            <a class="float-left w-100 text-left text-decoration-none p-1 text-dark" data-toggle="collapse"
                                               href="#section-web-{{ language.code }}" role="button" aria-expanded="false"
                                               aria-controls="section-web-{{ language.code }}">
                                                <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                                                {{ language.label }}
                                            </a>
                                        </h6>
                                    </div>

                                    <div id="{{ 'section-web-' ~ language.code }}" class="collapse {% if loop.first %}show{% endif %}" aria-labelledby="{{ 'heading-web-' ~ language.code }}" data-parent="#cgus-web">
                                        <div class="card-body">
                                            {{ form_widget(cgu.content) }}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}

                            <div class="text-right mt-3">
                                <a class="mr-3 btn btn-dark" role="button" href="{{ path('admin_cgu_index',{'profile': profile.id}) }}">{% trans %}Annuler{% endtrans %}</a>
                                <button class="btn btn-success" type="submit">{{ button_label|default('save') |trans }}</button>
                            </div>

                            {{ form_end(formWeb) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block modals %}
    <!-- DELETE -->
    <div class="modal fade" id="delete-cgu-modal" tabindex="-1" role="dialog" aria-cguledby="delete" aria-hidden="true">
        <form action="{{ path('app_cgu_delete', {profile: profile.id, id: ':id' }) }}" id="delete-cgu-form" method="POST">
            <input type="hidden" name="token" value="{{ csrf_token('delete-cgu') }}"/>
            <input type="hidden" name="_method" value="DELETE">

            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="delete">Confirmation</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-cgu="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">{% trans %}cgu_confirm_delete{% endtrans %}</div>
                    <div class="modal-footer">
                        <button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                        <button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        window.$(function () {
            var $deletekModal = $('div#delete-cgu-modal'),
                $deleteForm  = $deletekModal.find('form#delete-cgu-form'),
                deleteAction = $deleteForm.attr('action');

            $('#cguTable').on('click', 'a[href="#delete-cgu-modal"]', function (event) {
                event.preventDefault();
                $deleteForm.attr('action', deleteAction.replace(':id', $(this).attr('data-cgu-id')));
                $deletekModal.modal('show');
            });

            $deletekModal.on('hidden.bs.modal', function () {
                $deleteForm.attr('action', deleteAction);
            });
        });
        $(function () {
          $('[data-toggle="popover"]').popover({ html : true});
        });

</script>
<script type="text/javascript">
$('body').on('submit','form[name="app_cgu_form"]', function(e){
  e.preventDefault();
  var $_self = $(this);
  $('.btn-success').prop('disabled', true);
  $.post($(this).attr('action'), $(this).serialize(), function(data, status, jqXHR) {
        if (jqXHR.status === 201 || jqXHR.status === 200) {
            if ($(data).attr('data-enabled') !== undefined) {
                var tdSelector = ".cgu-enabled-td.app-version-" + $(data).attr('data-app-version');
                var selectorByVersion = tdSelector + " i";
                $(selectorByVersion).removeClass('fas fa-check text-success');
                $(selectorByVersion).addClass('fas fa-ban text-danger');
                $(tdSelector).parent().find('.p-date').text('-');
            }
        }
        if (jqXHR.status === 201) {
            $('#cguTable tbody').append(data);
            scrollToAnchor('tr:last-child');
            highlight('tr:last-child');
            //Can do a reset here;
        }  else if (jqXHR.status === 200) {
            var id = $_self.attr('data-cgu-id');
            $('#tr-cgu-'+id).replaceWith(data);
            scrollToAnchor('#tr-cgu-'+id);
            highlight('#tr-cgu-'+id);

        }
        $('.invalid-feedback').remove();
        $('input').removeClass('is-invalid');

      }).fail(function(jqXHR, textStatus, errorThrown) {
        $('#form_area').html(jqXHR.responseText);
      })
      .always(function() {
         $('.btn-success').prop('disabled', false);
      });
});

function scrollToAnchor(aid){
    var aTag = $(aid);
    if (aTag !== undefined && aTag.offset() !== undefined) {
        $('html,body').animate({scrollTop: aTag.offset().top},'slow');
    }
}
function highlight(element) {
    $(element).addClass("highlight");
    setTimeout(function () {
        $(element).removeClass('highlight');
    }, 3000);
}
</script>
{% endblock %}
