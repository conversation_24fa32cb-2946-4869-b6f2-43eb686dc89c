{% extends '_layout/base_back.html.twig' %}

{% block body %}
    <div class="card shadow mb-4">
        <div class="card-header pt-4">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {% if source == "APP" %}active{% endif %}" data-toggle="tab"
                       href="#parameters-app">
                        <h6 class="m-0 font-weight-bold text-primary">APP</h6>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if source == "WEB" %}active{% endif %}" data-toggle="tab"
                       href="#parameters-web">
                        <h6 class="m-0 font-weight-bold text-primary">WEB</h6>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane {% if source == "APP" %}active{% endif %}" id="parameters-app">
                    {{ form_start(formApp, {'action': path('system_update_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "brand_update_label" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.brand_update_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formApp.brand_update_url_pc) }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.brand_update_url_pc) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formApp.brand_update_url_mac) }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.brand_update_url_mac) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formApp['brand_update_help_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "nac_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_carto_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.nac_carto_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formApp['nac_carto_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.nac_soft_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formApp['nac_soft_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "rcc_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.soft_rcc_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formApp['soft_rcc_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "aio_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formApp.soft_aio_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formApp['soft_aio_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "smeg_label" | trans }}</h5>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-gray-700 font-weight-bold">{{ "smeg_carto_label" | trans }}</h6>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            {{ "smeg_activation" | trans }}
                        </div>
                        <div class="col-md-7">
                            {{ form_widget(formApp.smeg_carto_enabled) }}
                        </div>
                    </div>
                    <div class="row mb-4">
                         {% for language in languages %}
                            {% set formL = formApp['smeg_carto_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                    </div>
                    {{ form_end(formApp) }}
                </div>
                <div class="tab-pane {% if source == "WEB" %}active{% endif %}" id="parameters-web">
                    {{ form_start(formWeb, {'action': path('system_update_parameters_index',{'profile': profile.id}), 'method': 'POST'}) }}
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "brand_update_label" | trans }}</h5>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.brand_update_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formWeb.brand_update_url_pc) }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.brand_update_url_pc) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ form_label(formWeb.brand_update_url_mac) }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.brand_update_url_mac) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formWeb['brand_update_help_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "nac_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_carto_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.nac_carto_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                            {% for language in languages %}
                            {% set formL = formWeb['nac_carto_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.nac_soft_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                             {% for language in languages %}
                            {% set formL = formWeb['nac_soft_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "rcc_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.soft_rcc_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                        {% for language in languages %}
                            {% set formL = formWeb['soft_rcc_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                    </div>
                        <div class="mb-3">
                            <h5 class="text-gray-900 font-weight-bold">{{ "aio_label" | trans }}</h5>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-gray-700 font-weight-bold">{{ "nac_soft_label" | trans }}</h6>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                {{ "nac_activation" | trans }}
                            </div>
                            <div class="col-md-7">
                                {{ form_widget(formWeb.soft_aio_enabled) }}
                            </div>
                        </div>
                        <div class="row mb-4">
                             {% for language in languages %}
                            {% set formL = formWeb['soft_aio_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                        </div>
                    <div class="mb-3">
                        <h5 class="text-gray-900 font-weight-bold">{{ "smeg_label" | trans }}</h5>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-gray-700 font-weight-bold">{{ "smeg_carto_label" | trans }}</h6>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            {{ "smeg_activation" | trans }}
                        </div>
                        <div class="col-md-7">
                            {{ form_widget(formWeb.smeg_carto_enabled) }}
                        </div>
                    </div>
                    <div class="row mb-4">
                        {% for language in languages %}
                            {% set formL = formWeb['smeg_carto_url-' ~ language.code] %}
                             <div class="col-md-3">
                             
                             {{ form_label(formL)}}
                              <img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;">
                             </div>
                               <div class="col-md-7">
                              {{ form_widget(formL) }}
                                 </div>
                             {% endfor %}
                    </div>
                    {{ form_end(formWeb) }}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-2 float-right">
        <button id="form_submitor"
                class="btn btn-primary float-right">{{ button_label|default('save')|trans|capitalize }}</button>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function () {
            $("#form_submitor").on('click', function () {
                var id = $('.card-header-tabs .nav-item .active').attr("href");
                $(id + " form").submit();
            })
        });
    </script>
{% endblock %}
