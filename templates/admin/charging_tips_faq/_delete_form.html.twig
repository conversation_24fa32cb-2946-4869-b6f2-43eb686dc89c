<form class="d-inline-block" method="post" action="{{ path('charging_tips_faq_delete', {'chargingTips': charging.id, 'profile' : profile.id}) }}" id="form-{{ charging.id }}"
      onsubmit="return false;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ charging.id) }}">
    <button class="btn btn-danger btn-sm text-right" data-toggle="modal" data-target="#deleteModal{{ charging.id }}">{% trans %}Supprimer{% endtrans %}</button>
</form>
<!-- Logout Modal-->
<div class="modal fade" id="deleteModal{{ charging.id }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{% trans %}Suppression{% endtrans %}</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-left">{% trans %}charging_modal_delete{% endtrans %}</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
                <button class="btn btn-danger" type="button" onclick="$('#form-{{ charging.id }}').attr('onsubmit', 'return true').submit();">{% trans %}Supprimer{% endtrans %}</button>
            </div>
        </div>
    </div>
</div>
