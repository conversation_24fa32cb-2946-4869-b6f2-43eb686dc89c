<div class="card shadow mt-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                 {% for language in languages %}
                     <li class="nav-item">
                        <a class="nav-link {% if loop.first %} active {% endif %} w-100" data-toggle="tab" href="#charging-{{language.id}}">
                            <h6 class="m-0 font-weight-bold text-primary"><img src="{{ asset('images/flags/'~language.code~'.svg') }}" alt="{{ language.label }}" style="max-width: 2rem;width: 50%;"> {{language.label}}</h6>
                        </a>
                    </li>
                 {% endfor %}
            </ul>
        </div>
        <div class="overflow-auto">
            <div class="card-body">
                <div class="tab-content">
                 {% for language in languages %}
                      <div class="tab-pane {% if loop.first %} active {% endif %}" id="charging-{{language.id}}">
                            <div class="mb-4 text-right">
                                  <a role="button" class="btn btn-primary" href="{{ path('charging_tips_faq_add' , {'profile': profile.id,'langue': language.id}) }}">{% trans %}Ajouter{% endtrans %}</a>
                            </div>
            <div class="table-responsive">
                <table id="dataTable-{{language.id}}" class="table table-bordered table-hover dataTable">
                    <thead>
                    <tr class="text-primary">
                        <th>{% trans %}vehicle type{% endtrans %}</th>
                        <th>{% trans %}title{% endtrans %}</th>
                        <th>{% trans %}template{% endtrans %}</th>
                        <th>{% trans %}URL label{% endtrans %}</th>
                        <th>{% trans %}Online{% endtrans %}</th>
                        <th class="text-right">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for charging in site.ChargingTypesByLanguage(language) %}
                        <tr>
                            <td>{{ charging.vehicleType }}</td>
                            <td>{{ charging.title }}</td>
                            <td>{{ charging.template }}</td>
                            <td>{{ charging.urlLabel }}</td>
                            <td> {{ charging.online ? 'YES' : 'No' }}</td>
                            <td class="text-right">
                                <a role="button" class="btn btn-sm btn-warning mr-1"
                                href="{{ path('charging_tips_faq_edit', {'profile': profile.id, 'chargingTips': charging.id}) }}"
                                >
                                {% trans %}Modifier{% endtrans %}
                                </a>
                                 {{ include('admin/charging_tips_faq/_delete_form.html.twig') }}
                             </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">
                                {% trans %}charging_empty{% endtrans %}!
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
                     </div>
                 {% endfor %}
              </div>
           </div>
      </div>
  </div>  
