{% extends '_layout/base_back.html.twig' %}

{% block body %}

{{ include('admin/charging_tips_faq/_tables.html.twig', {'languages':  languages}) }}

{% endblock %}
{% block javascripts %}
    {{ parent() }}

    <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            {% for language in languages %}
                  $('#dataTable-{{ language.id }}').DataTable();
            {% endfor %}
            
        });
    </script>
{% endblock %}