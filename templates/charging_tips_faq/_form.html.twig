{{ form_start(form, { 'attr': {'class': 'mt-4'}}) }}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'Vehicle Type' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.vehicleType) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'title' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-6 text">
                    {{ form_widget(form.title) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'description' | trans }}
                    <span class="mandatory">*</span>
                </div>
                <div class="col-md-6 text">
                    {{ form_widget(form.description) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'online' | trans }}
                </div>
                <div class="col-md-1 ">
                    {{ form_widget(form.online) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'template' | trans }}
                </div>
                <div class="col-md-6">
                    {{ form_widget(form.template) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'URL' | trans }}
                </div>
                <div class="col-md-6 text">
                    {{ form_widget(form.url) }}
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-2">
                    {{ 'URL label' | trans }}
                </div>
                <div class="col-md-6 text">
                    {{ form_widget(form.urlLabel) }}
                </div>
            </div>
        </div>
        <div class="card-footer text-right">
            <a class="mr-1 btn btn-dark" role="button" href="{{ path('charging_tips_faq_index',{'profile': profile.id}) }}">{% trans %}Retourner à la liste{% endtrans %}</a>
            {% if update == true %}
                <a href="#delete-charging-modal" class="btn btn-danger mr-3" data-toggle="modal" data-target="#delete-charging-modal" data-title="">{% trans %}Supprimer{% endtrans %}</a>
            {% endif %}
            <button class="btn btn-success" type="submit">{{ button_label|default('Enregistrer') |trans }}</button>
        </div>
    </div>
{{ form_end(form) }}
{% if update == true %}
	<div class="modal fade" id="delete-charging-modal" tabindex="-1" role="dialog" aria-labelledby="delete" aria-hidden="true">
		<form action="{{ path('charging_tips_faq_delete', {profile: profile.id, chargingTips: id}) }}" id="delete-charging-form" method="POST">
			<input type="hidden" name="token" value="{{ csrf_token('delete-charging') }}"/>
			<input type="hidden" name="_method" value="DELETE">

			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="delete">Confirmation</h5>
						<button class="close" type="button" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
					</div>
					<div class="modal-body">{% trans %}charging_modal_delete{% endtrans %}</div>
					<div class="modal-footer">
						<button class="btn btn-light" type="button" data-dismiss="modal">{% trans %}Annuler{% endtrans %}</button>
						<button class="btn btn-danger" type="submit">{% trans %}Supprimer{% endtrans %}</button>
					</div>
				</div>
			</div>
		</form>
	</div>
{% endif %} 