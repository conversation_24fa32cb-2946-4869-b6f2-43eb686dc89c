{% block checkbox_widget -%}

    {{ block('checkbox_widget_base') }}

{%- endblock checkbox_widget %}

{% block checkbox_label %}
    <div class="mb-3">
        <h5 class="text-gray-900 font-weight-bold">{{- label is not same as(false) ? label|trans -}}
        {{- form_errors(form) -}}
        {% if attr.RELEASE_REQUIRED is defined and  attr.RELEASE_REQUIRED is same as(true) %}
           <SMALL> ({{"new_app_publication_required"|trans}})</SMALL>
        {% endif %}

    </h5>
        
    </div>
{% endblock %}

{%- block checkbox_widget_base -%}

    <div class="row mb-4">
        <div class="col-md-3">
            {{ "label_activation"|trans }} 
        </div>
        <div class="col-md-7">
             <input type="checkbox" {{ block('widget_attributes') }} {% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked="checked"{% endif %} />
        </div>
    </div>
{%- endblock checkbox_widget_base -%}
