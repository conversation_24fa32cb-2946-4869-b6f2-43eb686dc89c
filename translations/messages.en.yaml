#Menu Left-bar
dashboard: Dashboard
administration: Administration
site_creation: 'Site Creation'
fo_label: 'Reference Label Management'
ws_parameters: 'WebServices Parameters'
functionalities: Features
social_networks: 'Social Networks'
ds_range: 'DS Range Management'
locale_translation: 'Local Translations'
offers: Offers
mediatheque: Media Library
profiles_directories: 'Profile Management'
site_parameters: 'Site Settings'
'Paramètres WS Local': 'WS LOCAL Parameters'
PRDV: 'Online booking'
CGU: 'Terms & conditions'
export_features_menu: 'Export applications'
road_assistance_menu: 'Road assitance'

#Header
Utilisateur: User
Pays: Country
Environnement: Environment
'Se déconnecter': Log out

#Page Select: Choose your site
'Choisissez votre site': Choose your site

#Page Accueil
Bienvenu: 'Welcome to'
'Vous êtes authentifié comme': 'You are authenticated as'
Rôle: Role

#Page Gestion gamme DS
'Date de création': 'Creation date'
'Date de mise à jour': 'Update date'
'Retourner à la liste': 'Return to the list'
'êtes-vous sûr de vouloir le supprimer': 'Are you sure you want to delete this LCDV?'
'Suppression du LCDV': 'Deleting the LCDV'

#Page Réseaux Sociaux
'Gestion des Réseaux Sociaux': 'Social Network Management'
'Modification des Réseaux Sociaux': 'Editing Social Networks'
'La liste des réseaux sociaux est vide': 'The list of social networks is empty'
Ajouter: Add
Recherche: Search
Modifier: Edit
Activé: Activated
Activer: Activate
Désactivé: Disabled
Désactiver: Desactivate
Supprimer: Delete
Annuler: Cancel
'Etes-vous sûr(e) de vouloir supprimer ce réseau social': 'Are you sure you want to delete this social network?'
'Etes-vous sûr(e) de vouloir désactiver ce réseau social': 'Are you sure you want to disable this social network?'
'Etes-vous sûr(e) de vouloir activer ce réseau social': 'Are you sure you want to activate this social network?'
'Chargement en cours': 'Loading'
'La modification a été effectuée avec succès !': 'The change has been successfully completed!'
'La supression a été effectuée avec succès !': 'The deletion was successfully completed!'

#Page FO Label
'Nouveau libellé': 'New label'
'add_key_referent': 'Add a new Reference Label'
'edit_key_referent': 'Edit a Reference Label'
'Clé': 'Label name'
Libellé: Label
'Sprint de création du libellé': 'Sprint in which the label has been created'
'Nom de la fonctionnalité associée au libellé': 'Name of the feature associated with the label'
'Propagation marques': 'Brands'
'Support': 'Platforms'
'Libellé référent français': 'French reference label'
'Libellé référent anglais': 'English reference label'
'Valeur du paramètre': 'Value of the parameter'
'Date de maj': 'Update date'
'Fonctionnalité': 'Feature'
Brands: Brands
Device: Device
'Clé de libellé': 'Label key'
'Libellé référent': 'Reference label'
'Aucun enregistrement trouvé': 'No records found'
'Liste des': 'List of'
"La valeur support n'est pas autorisée": "The value %support% is not allowed"
"La valeur brand n'est pas autorisée": "The value %brand% is not allowed"
Exporter: Export

#Locale translation
'Liste des traductions locales': 'List of local translations'
'Traduction Pays': 'Country Translation'
'Traductions Locales': 'Local Translations'
Fichier: File
"file_not_allowed": "This type of file is not allowed."
'no records found': 'No records found'
'Choisir un fichier': 'Choose a file'

#WS Parameters Customer@
'customer_at': 'WS Customer@'
'customer_at-url': 'URL'
'customer_at_clientid': 'Client ID'
'customer_at_secret': 'SECRET'

#WS Parameters BrandID
'brand_id': 'WS BrandID'
'brand_id-url': 'URL'
'brand_id_url2': 'URL'
'brand_id_url_api': 'URI BRANDID API'
'brand_id_secret': 'SECRET'

#WS Parameters APIConnect
'api_connect': 'WS API Connect'
'api_connect_url': 'URL'

#WS Parameters Corvet
'corvet': 'WS Corvet'
'corvet-url': 'URL'
'corvet-clientid': 'Client ID'

#WS Parameters visual3D
'visual3d': 'WS Visual3d'
'visual3d-url': 'URL'
'visual3d-width': 'Width'
'visual3d-height': 'Height'

#WS Parameters LVH
'lvh': 'LVH'
'lvh-checkbox': 'Activation'
'lvh-url': 'URL'
'lvh-cache': 'Cache Duration'

#WS Parameters ApiMe
'api_me': 'API ME'
'api_me-url': 'URL'

#WS Parameters ApiCar
'api_car': 'API CAR'
'api_car-url': 'URL'

#WS Parameters ApiShop
'api_shop': 'API SHOP'
'api_shop-url': 'URL'

#WS Parameters ApiDealer
'api_dealer': 'API Dealer'
'api_dealer_url': 'URL'
'api_dealer_pdv_max': 'Max. number of shops displayed'
'api_dealer_rayon_max': 'Max. search radius in Km'
'api_dealer-consumer': 'consumer'
'api_dealer-brand_activity': 'Brand Activity'
'api_dealer_local-code_service_valet_arcard_ac': 'Code Service Valet Arcad AC'
'api_dealer_local-code_service_valet_ds7': 'Code Service Valet DS7'
'api_dealer_local-lcdv_ds' : 'LCDV DS'
'api_dealer_local': 'API Dealer Local'

#WS Parameters PRDV ERCS
'prdv_ercs': 'PRDV ERCS'
'prdv_ercs-url': 'URL'

#WS Parameters ApiMiddleware
'api_middleware': 'API Middleware APP'
'api_middleware-url': 'URL'

#WS Parameters GoogleMap
'google_map': 'GoogleMap'
'google_map-url': 'URL'
'google_map-key': 'Key'

#WS Parameters CVS
'cvs': 'CVS'
'cvs-url': 'URL'
'cvs-clientid': 'Client ID'

#WS Parameters APIC
'apic': 'APIC'
'apic-url': 'URL'
'apic-clientid': 'Client ID'

#Site parameters
site_name: 'Name'
site_code: 'Code'
online_application: 'Online Application'
timezone: 'Timezone'
brand: 'Brand'
site_general: General
save: 'Save'
languages: Languages
site_languages: Site language
selected_values: Selected values
unselected_values: Available values
reference_language: Reference language
site_settings: National Settings
distance_unit: Distance Unit
consumption_unit: Consumption Unit
cost: Cost
volume_unit: Volume unit
date_format: Date format
hour_format: Time format
consumption: Consumption
distance: Distance
volume: Volume
dates: Dates
online_site: Website online
ldap_code: Code LDAP 
default_civility:  Civilities Customer@ active 
name_order: Name Order Firstname/Lastname
complementary_currencies: complementary currencies
currencies: Currencies
domtom: Display the country selection message for the French overseas departments
first_last_name: "Firstname + Lastname"
last_first_name: "Lastname + Firstname"

#Label filters
label_updated: Updated label
label_to_update: Label to update
label_to_translate: New label to translate
filter: Filter

#Directory profiles
profil: profile
site: site
bo_access: BO Access
ws_parameters_app: WS Parameters APP
ws_parameters_web: WS Parameters WEB

#PRDV parameters
prdv_parameters_app: Online Booking APP
prdv_parameters_web: Online booking WEB
prdv_activation: Activation
prdv_perimeter: Solution
prdv_url: URL
prdv_central: Central
prdv_local: Local
prdv_label: Make an appointment
quote_label: Make a quote
prdv_app_label: Make an appointment (Available on app)

#MAJ CARTO&SOFT
pc_update_url: PC Download URL
mac_update_url: MAC Download URL
brand_update_label: Brand Update (NAC/RCC)
nac_parameters_app: Carto & Soft Update APP
nac_parameters_web: Carto & Soft Update  WEB
nac_activation: Activation
nac_carto_label: Maps
nac_soft_label: Software
nac_url: URL
nac_menu_label: System Update
nac_label: NAC Update
rcc_label: RCC Update
aio_label: AIO Update
smeg_label: SMEG Update
smeg_activation: Activation
smeg_carto_label: cartography
help_manual_url: Help manual

#Eligibility vehicles
eligibility_empty: 'The vehicle list is empty'
'Eligibilité boitier NAC': 'NAC box eligibility'
'Eligibilité boitier RCC': 'RCC box eligibility'
'Eligibilité boitier véhicule': 'Vehicle box eligibility'
'Nouveau modèle de véhicule': 'New vehicle model'

#WS MAPCARE
api_mapcare: WS MAPCARE
api_mapcare-url: URL
api_mapcare-app_id: App ID
api_mapcare-secret: Secret

#WS MAJESTIC
api_majestic: WS MAJESTIC
api_majestic-url: URL
api_majestic-nac_soft_firmware: NAC software type firmware
api_majestic-nac_soft_carto: NAC software type carto
api_majestic-rcc_soft_firmware: RCC software type firmware
api_majestic-aio_soft_firmware: AIO software type firmware

#WS SAMS
api_sams: SAMS
api_sams-url_catalog: URL Catalog
api_sams-url_contrib: URL Contrib
api_sams-url_contract: URL Contract
Services connectés: Services
sams_payment_method : SAMS Payment Method
sams_payment_method-url_method_payment: URL Payment Method

#Medias
medias_list_empty: 'The media list is empty'
Nom: Name
Download: 'Downloaded on'
'Edit image': "Edit image"
Taille: Size
confirmation_delete_image: "Are you sure you want to delete this image?"

#vehicle label
lcdv_model_new: Add a new model
lcdv_model_update: Update a model
lcdv_model_label: Label
lcdv_model_lcdv: Lcdv
rpo_model_rpo: RPO
vehicle_labels: Vehicle Models
LCDV example: LCDV Example
short_label: Short label
lcdv_model_delete: Are you sure you want to delete this label?
lcdv_label_deleted: Label successfully deleted
lcdv_label_added: Label successfully added
lcdv_label_not_deleted: Error removing label
lcdv_label_updated: Label successfully updated
local_lcdv_updated: Labels successfully updated
local_label_error: Invalid labels
local_lcdv_update: Update model labels
local_vehicle_labels: Local Vehicle Models

#Site creation
site_create: Create site
site_created_success: Site has been correctly created
site_updated_success: Site has been correctly updated
site_created_error: At least one mandatory field is missing

#privacy policy
'privacy_policy': 'Privacy Policy Management'
'Politique de confidentialité': 'Privacy policy'

#Registration document
regis_doc_image_success: Image successfully added
regis_doc_image_no_selected: No selected image
regis_doc_image_deleted: Image successfully deleted
regis_doc_image_header: Help visual for entering the VIN
regis_doc_image_delete_confirmation: Are you sure you want to delete the image?
regis_doc_image_select_header: Image selector
regis_doc_help_menu: Registration document picture
regis_doc_help_label: <strong>For APP, these parameters are taken into account when the application is published.</strong>
#Smart Banner
smart_banner_title: Smart Banner
smart_banner_menu: Smart Banner
app_icon: APP Icon
store_app_name: APP Name
play_store_url: Play Store URL
play_store_store_id: Store iD Play Store
play_store_app_id: App iD Play Store
app_store_url: Apple Store URL
app_store_store_id: Store iD Apple Store
app_store_app_id: App iD Apple Store
smartbanner_home: Homepage
smartbanner_car: Car Page
smartbanner_maintenance: Maintenance Page
smartbanner_workshop: Workshop Page
smartbanner_connexion: Connexion Page
#favorite dealer
favorite_dealer_menu: Favorite Dealer
favorite_dealer_label: Favorite Dealer

legal_menu: Legal
accessibility_menu: Accessibility

#DEALER
dealer_settings_menu: Dealers
dealer_radius: Search radius (km)
dealer_max: Maximum number of results
dealer_settings_near_title: Near dealers
dealer_settings_filters_title: Filters dealers
filter_services: Services Filtre ( 5 Maximum)
near_dealers: Maximum number of results (5 Maximum)
#BUSINESS LIST
business_list_menu: Business List
business_List: Business List

#PROMOTE APP
promote_app_title: Coaching & statistics block
promote_app_menu: Promote APP

#SAMS
SAMS_NAVCO: NAVCO
SAMS_ZAR: ZAR
SAMS_NAVCOZAR: Bundle NAVCO /ZAR
SAMS_TMTS: TMTS
SAMS_LEV: Remote LEV
Early_Adopters : Early adopters
Sams_Webview : Sams Webview
BOUTIQUE : Boutique
connected_services : Connected Services
services : Services
apv_services: After Sales Services
SAMS_BOUTIQUE : Sams Boutique
marketing_consent : Marketing Consent
SAMS_FULLDIGITAL: TMTS Full Digital Notifications
SAMS_CONNECTEDALARM: Connected Alarm
SAMS_DIGITALKEY: Digital Key
SAMS_AE_CALL: AE CALL
SAMS_PARTNERSERVICE: Partner Service
SAMS_SECUTITY: Stolen Vehicle
SAMS_TripsintheCloud: Trips in the Cloud
#ASSISTANCE
assistance_title: Customer relationship
assistance_menu: Assistance
faq_url: FAQ
contact_form_url: Care Form
form_type: Form Type
assistance_phone: Phone
assistance_brand_title: "%brand% Assistance"
assistance_dealer_title: Favorite Dealer
rlc_assistance_menu: Customer Relationship
#LogIncident
log_incident_menu: INCIDENTS
api_log_incident: LOG INCIDENTS
api_log_incident-url: URL

#SSO URLS
sso_urls_title: SSO Urls List
change_sso_base_url: Change the SSO base url
sso_url: SSO Url
sso_pages_menu: SSO Pages

#History logs
history_menu: History

#General Parameters APP
general_parameters_menu: Paramétrage général APP
findmycar_label: Find My Car
label_activation: Activation
new_app_publication_required: requires new APP release
scanvin_label: Scan Véhicule

#Eligibility B9E
eligibility_b9e_menu: B9E Kuantic

#API MS user data
api_ms_user_data: MS USER DATA
api_ms_user_data-url: URL

#Eligibility Boitier
eligibility_boitier_menu: Eligibility Boitier
eligibility_boitier_b9e_menu: B9E
eligibility_boitier_nac_menu: NAC
eligibility_boitier_rcc_menu: RCC
eligibility_boitier_aio_menu: AIO

#export 
schedule_export: 'New export request'
export_list: 'Export liste'
trans_files_export: 'Export files'
export_request_success: 'The export request has been registered'
requestedAt: 'Requested at'
export: Export

#videos
title: Title
videos_title: Managing Playlists and Videos
videos_id: Youtube ID
videos_types: Type
priority: Priority
video_modal_delete: "Are you sure you want to delete it ?"
video_updated: "Successfully Updated"
video_added: "Successfully Added"
video_deleted: "Successfully Deleted"
video_not_deleted: "Error Occured while deleting"
videos_new: Add a PlayList (or a Video)
videos_update: Update a PlayList (or a Video)
videos_lcdv: LCDVs List
video_tutorial_menu: Tutorial Videos
api_youtube: API YOUTUBE V3
api_youtube-url: URL
api_youtube-clientid: Client ID
api_youtube-tags_prefix: Tags Prefix
playlist_or_video: PlayList/Video

#Whiteliste
vehicle_whitelist_empty: 'Empty List'
Nouveau véhicule White list : 'New White List Vehicle'
Modifier le véhicule : ' Edit Vehicle '
Marque : 'Brand'
Vin : 'VIN'

#road assistance
provider: provider
phone: Phone number

#custom messages
connected_services_message: Connected Services
welcome_message: Welcome Message
custom_messages_title: Custom Messages
before: Before
after: After
position_in_time: Position in time
display_weight: Display weight
days_number: Number of days
add_new_message: Add a new message
custom_message_menu: Custom Messages
maintenance_reminder_message: Maintenance reminder
favorite_dealer_message: Favorite Dealer
my_services_message: My services
dimbo_discover_message: Dimbo - Discover
dimbo_lead_message: Dimbo - Lead
reinsurance_message: Reinsurance message

# ws parameters salesforce marketing cloud
salesforce_marketing_cloud: Salesforce Marketing Cloud
salesforce_marketing_cloud-url: Marketing Cloud Server URL
salesforce_marketing_cloud-id: ID
salesforce_marketing_cloud-token: Token
salesforce_marketing_cloud-mid: MID
salesforce_marketing_cloud-checkbox_etanalytics: Etanalytics
salesforce_marketing_cloud-checkbox_pianalytics: Pianalytics
salesforce_marketing_cloud-checkbox_location: Location
salesforce_marketing_cloud-checkbox_inbox: Inbox

# maitenance parameters
maitenance_config_title: Maintenance parameters
activation: Activation
docsoa_esa_type: DOCSOA/ESA
dimbo: DIMBO
severe: Severe
normal: Normal
maintenane_menu: Maintenance
esa: FCS ESA
esa-url: URL
esa-clientid: Client ID
forfait: WS Forfait
forfait-url: URL
forfait-clientid: Client ID
forfait-certname: Cert Name
forfait-passphrase: Pass Phrase
forfait_maintenance: WS Forfait Entretien
forfait_maintenance-url: URL
forfait_maintenance-clientid: Client ID
forfait_maintenance-certname: Cert Name
forfait_maintenance-passphrase: Pass Phrase

#API MS user data
api_sys_dimbr_data: SYS DIMBR DATA
api_sys_dimbr_data-url: URL

promote_video: Promote
maintenance_use_condition: Use Condition

precontrol_activation: Technical pre-check
precontrol_title: Technical Control
operations_label: Label Systematic operation

repetitions: Repetitions
interval: Interval
interval_unit: Interval unit
alert: Alert
alert_unit: Alert unit

month: Month(s)
year: Year(s)

control_modal_delete: Are you sure you want to delete this control ?
technical_controls_title: Technical Controls

technical_controls_title_add: Add a technical control
technical_controls_title_edit: Update a technical control
maintenance: Maintenance
maintenance-steps: Steps
maintenance_precontrol_menu: Technical Control

#eligibilities
eligibilities_o2c_title: AMI(O2C)
eligibilities_ami_menu: AMI(O2C)
eligibilities_o2c_menu: AMI(O2C)
eligibilities_o2c_list: List of O2C Eligibilities
eligibilities_o2c_add: Add a new O2C Model
eligibilities_o2c_edit: Edit an O2C Model

use_middleware_v2: Use V2 Maintenance
middleware_maintenance_v2_menu: Maintenance V2

#model vehicl default image
vehicle_default_image_label: Default vehicle visual
vehicle_default_image_success: The default vehicle visual was successfully updated 
vehicle_default_image_select_valid_media: Please add a valid image

advisor_menu: Advisor
ws_advisor: Advisor
ws_advisor-url: URL
ws_advisor-app_id: App ID
ws_advisor-client_id: Client ID
ws_advisor-certificate: Certificate
ws_advisor-certificate_key: Certificate Key
ws_advisor-certificate_pass_phrase: Certificate Passe phrase

#ereca settings
recall_campaign_menu: Recall Campaign
recall_campaign: Recall Campaign
ws_ereca: eRECA
ws_ereca-certificate: Certificate
ws_ereca-client_id: Client ID
ws_ereca-auth_http_login: Auth HTTP Login
ws_ereca-auth_http_password: Auth HTTP Password
ws_ereca-certificate_pass_phrase: Certificate Passe phrase
ws_ereca-url: URL

#form invalid message
form_invalid_global_message: Some Entries are invalid

priority_information_message: 1 to 10 stands for high priority to less priority
choose_brand_title: Brand
choose_country_title: Country
choose_profile_title: Profile
choose_brand: Choose a brand
choose_country: Choose a country
choose_profile: Choose your profile

#Consent Marketing
consent_type: Consent Type
consent_enabled: Activation
text_introduction: Introduction Text
consent_added: Consent added with success
consent_deleted: Consent deleted with success
consent_not_deleted: Consent delete failed

#CGU
cgu_confirm_delete: Are you sure you want to remove this version of CGU ?
update_date: Last update
enabled: enabled
new_cgu_version: New CGU Version

# ws parameters
v1_bo: BO V1
v1_bo-url: URL
#lcv_stellantis
lcv_stellantis: Lcv stellantis

#CFirst parameters
api_customer_first-url: 'URL'
api_customer_first-user: 'USER'
api_customer_first-source: 'Source'
api_customer_first-secret: 'Secret'
api_customer_first-certname: 'Certificate'
api_customer_first: CFirst
api_sys_customer_first: API SYS CFirst

#Documentation
documentation: Documentation
scan_my_car: Scan My Car
scan_my_car_title: Scan My Car

#ws IMA
ws_ima: IMA
ws_ima-url: URL
ws_ima-id: ID
ws_ima-secret: SECRET

#OLB Agenda
ws_agenda: Agenda
ws_agenda-url: URL
ws_agenda-client_id: Client ID
ws_agenda-pass_phrase: Certificate Pass Phrase
ws_agenda-cert_name: Certificat (.cer & .key)

#OLB PRDV
ws_prdv: PRDV
ws_prdv-url: URL

# CFirst System parameters
api_sys_customer_first-url: URL
api_sys_customer_first-months: Months

#SettingsConfig
settings_configuration: Settings Configuration

trace_connection_menu: Connection logs
connection_trace_title: Connections logs

#API SYS Corvet
api_sys_corvet: 'API SYS Corvet'
api_sys_corvet-url: 'URL'

#DS ONLY YOU
ds_only_you_menu: DS ONLY YOU
oly_title: Title
oly_description: Description
oly_assistance_doc_url: Doc Assistance Url
oly_assistance_phone: Assistance Phone
oly_image: Image

#Charging tips faq
charging_tips_faq : Charging tips FAQ
charging_empty : The liste is empty
charging_tips_faq_add : Add charging tips FAQ
charging_modal_delete :  Are you sure you want to delete it?
charging_tips_faq_edit : Edit charging tips FAQ 


#My account sams
myaccount_sams : MyAccount SAMS
myaccount_sams-url: URL

# SSO
api_sso : SSO
api_sso-url: URL 

# Remote Acces
SAMS_RACCESS : Remote ACCES 


SAMS_DSCP: PRIVILEGE

SECTION-DS_ONLY_YOU: DS ONLY YOU
SECTION-SAMS_DSCP: DS PRIVILEGE
SECTION-DS_VALET: DS VALET

delete_version_dialog_title: Delete of version
delete_version_dialog_body: Are you sure you want to delete this version ?
update_version_title: Update version
version_app: App Version
app_version_menu: App Versions
app_version_list: Liste des versions de l'app
published: published
version_delete_error: This version cannot be deleted, used by other functionnalities
published_date: Publish Date
hivebrite: HIVEBRITE
hivebrite-url: SSO URL

SECTION-DS_RENT: DS RENT
rent_ftm_url: FreeToMove URL
rent_ftm_url_member: FreeToMove URL (Member)
rent_ftm_url_no_member: FreeToMove URL (No Mmeber)

oly_assistance_doc: Assistance Document
oly_assistance_doc_info: Assistance Document (Can be added from Medias Library)

#Usabila
usabila_event_1 : 'Event 1'
usabila_event_2 : 'Event 2'
usabila_event_3 : 'Event 3'
usabila_event_4 : 'Event 4'
usabila_event_5 : 'Event 5'
usabila_event_6 : 'Event 6'
usabila_event_7 : 'Event 7'
usabila_event_8 : 'Event 8'
usabila_menu : 'Usabila'
# Payment Method
payment_method: Payment Method
paymentmethod_menu: Payment Method

biometrics : Biometrics
biometrics_menu : Biometrics

SECTION-DS_ASSISTANCE: DS ASSISTANCE

play_storeurl: Store URL
play_activation: Activation
# Brand Univers
brand_univers_menu : Brand univers
brand_univers_content : Brand univers
brand_univers_categories : Brand univers Categories
brand_univers_categories_new_title : Add new category
new_categorie : New category
categories_list_empty : Empty list categories

valet_cgu_url: Terms and Conditions Url

#manage update
manage_update:
  os_version_min: OS version minimum
  mym_version_min: MyM version minimum 
  mym_version_online: MyM version in line
  start_date_nr: Date of launch NR
  end_date_certif: Certificate expiry date
  dialog_title: Delete update
  dialog_body: Are you sure you want to delete this update?
  version: Type
  list: UPDATE LIST
  ajout: ADD UPDATE
  title: EDIT UPDATE
  menu: Manage Update
# Ev Routing
ev_routing: Ev Routing
EV_ROUTINGS: Ev Routing
ev_routing_new: Add
ev_routing_update: Update
ev_routing_delete: Are you sure you want to delete this setting ?
ev_routing_label: label
ev_routing_lcdv: LCDV
ev_routing_rpo: RPO
ev_routing_engine_type: Engine TYPE
ev_routing_csciph: Constant speed consumption Kwh/100Km
ev_routing_max_charging: Max charging 
ev_routing_max_speed: Max speed
ev_routing_weight: weight
ev_routing_axle_weight: Axle weight
ev_routing_length: length
ev_routing_width: Width
ev_routing_height: Height
ev_routing_acceleration_efficiency: Acceleration efficiency
ev_routing_deceleration_efficiency: Deceleration efficiency
ev_routing_uphill_efficiency: UpHill efficiency
ev_routing_downhill_efficiency: DownHill efficiency
ev_routing_charging_curve_array: Curve array
ev_routing_planner : Ev Routing Planner
ev_routing_planner_menu : Ev Routing Planner
ev_routing_configuration_updated : Ev Routing Updated Successfully 
# DeepLinking
deep_linking: 
  title: Deeplinking
  list: Deeplinking pages list
  url: URL
  full_url: deeplinking URL
  desktopurl: Desktop URL
  menu: Deeplinking
deep_linking_page:
  dialog_title: Delete of page
  dialog_body: Are you sure you want to delete this page ?
  title: Page update
  page: Page
  menu: Page
  list: Pages list 
  delete_error: This version cannot be deleted because it is used by other functionnalities.
update_url: Page update
plp:
  section_llp: Lion Loyalty Program
  section_pillar: Pillar %order%
  section_vehicle_courtesy: Vehicle Courtesy
  section_accessories: Accessories
  llp_menu: Slider
  llp_parent_menu: Lion Loyalty Program
  llp_tcs_menu: "T&C’s Subscription"
  form:
    llp:
      title: Title (Not Yet Member + Member)
      description_member: Description (Member)
      description_no_member: Description (Not Yet Member)
      image_member: Image (Member)
      image_no_member: Image (Not Yet Member)
      vc_cgu_url: Terms and Conditions Url
llp_tcs_title: Termes et conditions

electric_range: Electric range
global_vehicle_settings: Vehicle Global Settings
local_vehicle_settings: Vehicle Local Settings
bta_disable_electric: Disable BTA for Electric Vehicles
smart_apps_disable_thermic: Disable SMART APPS for thermic Vehicles
smart_apps_disable_electric: Disable SMART APPS for electric Vehicles
cea_disable_electric: Disable CEA for electric Vehicles
versions_3m2_ivi: 3M2 IVI versions
ivi_range: IVI range

SECTION-DS_ACCESSORIES: DS ACCESSORIES
loyalty_accessory:
  title: Title Accessory
  description: Description
  cta: URL CTA Accessory
  delete_accessory: Remove this accessory
  add_accessory: Add one more accessory
  store_url: Accessories store url (no members)
  info: The addition, modification or deletion of an accessory will only take effect when the form is saved
  labelCta: Label CTA

vehicle_owners_manual:
  activation: Activation
  webview_url: Webview Url
  section: Vehicle Owners's Manual
  menu: Vehicle Owners's Manual

#third_part_eligibilities
third_part_eligibilities: Third Party Eligibilities
eligibilities_start_menu: START
eligibilities_scan_menu: SCAN
eligibilities_scan2_menu: SCAN2
eligibilities_cam_menu: CAM

bta: 
  connect_pack: 
    menu : Connect pack
    activation: Activation
    list: vins List
    name: vin
    ajout: vins Add
    title: Edit vin
    dialog_title: Deleting the connect pack vin
    dialog_body: Are you sure you want to delete this connect pack vin ?
  eligibility:
    menu: Bta eligibility

SECTION-content: Content
SECTION-publication: Publication
SECTION-seo: SEO
SECTION-published: Published Versions

content:
  bo_title: BO Title
  fo_title: FO Title
  targeting: Targeting
  stylesheet:
    title: StyleSheet
    light: Light
    dark: Dark
  category: Category
  introduction: Introduction
  visual: Visual
  add_media: Add a media
  delete_media: Delete a media
  add_paragraph: Add a paragraph
  delete_paragraph: Delete Paragraph
  publication_date: Date of publication displayed	
  start_date: Display date begin
  end_date: Display end date
  publish: Publier
  put_draft: Put As Draft
  push_online: Put Online
  push_offline: Put Offline
  deleted: Content Succesfully deleted
  delete_error: Error Occured while deleting content
  content_text: Text
  sub_title: Subtitle
  url: URL
  paragraph:
    text_identifier: Text N°
    text:  Text
    legend: Visual legend
    visual: Visual
    isLink: Link to the map
  cta:
    template:
      title: Template
      details: Details
      internal: Internal Link
      external: External Link
    label: CTA Label
    url: URL
    details:
      label: CTA Label
      url: URL
      opening: CTA opening
    redirect:
      self: CTA Internal
      blank: CTA External
  seo:
    url: Clear URL
    canonical_url: Canonical Url (Meta)
    rewrites: Redirects (code 301)
    priority: priority
    robots:
      label: Robots
      empty: Empty
      all: All
      no: No
      follow: noindex, follow
      index: index, nofollow
  visuel_voyant:
    title : Visuel voyant
    VOYANT_FEUX_DE_POSITION : FEUX DE POSITION
    VOYANT_FEUX_DE_CROISEMENT : FEUX DE CROISEMENT
    VOYANT_FEUX_DE_ROUTE : FEUX DE ROUTE
    VOYANT_PROJECTEURS_ANTIBROUILLARD_AVANT : PROJECTEURS ANTIBROUILLARD AVANT
    VOYANT_FEUX_ANTIBROUILLARD_ARRIERE : FEUX ANTIBROUILLARD ARRIÈRE
    VOYANT_PRECHAUFFAGE_MOTEUR_DIESEL : PRÉCHAUFFAGE MOTEUR DIESEL
    VOYANT_FREIN_DE_STATIONNEMENT : FREIN DE STATIONNEMENT
    VOYANT_STOP_AND_START : STOP &amp; START
    VOYANT_SYSTEME_AIRBAG_PASSAGER_ACTIVE : SYSTÈME D’AIRBAG PASSAGER ACTIVÉ
    VOYANT_PIED_SUR_LE_FREIN : PIED SUR LE FREIN
    VOYANT_ESSUYAGE_AUTOMATIQUE : ESSUYAGE AUTOMATIQUE
    VOYANT_SYSTEME_AIRBAG_PASSAGER_DESACTIVE : SYSTÈME D’AIRBAG PASSAGER DÉSACTIVÉ
    VOYANT_ESP_ASR : ESP/ASR
    VOYANT_STOP : STOP
    VOYANT_SERVICE : SERVICE
    VOYANT_FREINAGE : FREINAGE
    VOYANT_ABS : ANTIBLOCAGE DES ROUES (ABS)
    VOYANT_SYSTEME_AUTODIAGNOSTIC_MOTEUR : SYSTÈME D’AUTODIAGNOSTIC MOTEUR
    VOYANT_NIVEAU_MINIMUM_CARBURANT : NIVEAU MINIMUM DE CARBURANT
    VOYANT_TEMPERATURE_MAX_LIQUIDE_REFROIDISSEMENT : TEMPÉRATURE MAXIMUM DU LIQUIDE DE REFROIDISSEMENT
    VOYANT_PRESSION_HUILE_MOTEUR : PRESSION D’HUILE MOTEUR
    VOYANT_CHARGE_BATTERIE : CHARGE BATTERIE
    VOYANT_PORTES_OUVERTES : PORTE(S) OUVERTE(S)
    VOYANT_AIRBAGS : AIRBAGS
    VOYANT_CEINTURE_NONBOUCLEE : CEINTURE NON-BOUCLÉE/DÉCOUCLÉE
    VOYANT_DIRECTION_ASSISTEE : DIRECTION ASSISTÉE
  voyant_assistance: 
    menu : Voyants assistance
    title : Voyants assistance
    add_cta: Add voyant assistance
    edit: Update voyant assistance
    new: Add new voyant assistance
    delete_text: Are you sure you want to delete this content ?
  prefere_total:
    menu : Préfère Total
    title : Préfère Total
    add_cta: Add préfère total
    edit: Update préfère total
    new: Add préfère total
    delete_text: Are you sure you want to delete this content ?
    logo: Préfère Total Logo
  info_carburant:
    menu : Information carburant
    title : Information carburant
    add_cta: Add information carburant
    edit: Update information carburant
    new: Add information carburant
    delete_text: Are you sure you want to delete this content ?
  brand_universe:
    title: Brand Universe
    add_cta: Brand Universe Content
    edit: Update Brand Universe Content
    new: Add Brand Universe Content
    delete_text: Are you sure you want to delete this content ?
    menu: Brand Universe
    folder_menu: Brand Universe
  club:
    title: Club
    add_cta: Club Content
    add_events: Events Content
    add_benefits: Benefits Content
    edit: Update Content
    new_event: Add Event Content
    new_benefit: Add Benefits Content
    delete_text: Are you sure you want to delete this content ?
    menu: Club
    folder_menu: Club
    events: Events
    benefits: Benefits
    club_membership_charter_menu: Membership Charter
    club_memebership_charter_title: Membership Charter
    events_fields:
      eventDate: Event date
      nbParticipants: Participants max   
      booking : Booking
      reservationStartDate: Opening date
      reservationEndDate: Closing date
      isComplete: Full
      location: Location
      city: City 
      country: Country
      latitude: Latitude
      longitude: Longitude
    benefits_fields:
      type: Type
      validity: Validity
      nbParticipants: Participants max.
      code: Coupon code
      label: CTA label
      url: CTA url
      booking: Booking
      direct: Direct
      mail: Mail
  o2x_boutique:
    title: Boutique
    add_cta: Boutique Content
    edit: Update Boutique Content
    new: Add Boutique Content
    delete_text: Are you sure you want to delete this content ?
    menu: Boutique
    folder_menu: Boutique
  o2x_services:
    title: Service
    add_cta: Service Content
    edit: Update Service Content
    new: Add Service Content
    delete_text: Are you sure you want to delete this content ?
    menu: Services
    folder_menu: Services        
  categories:
    deleted: Category Succesfully deleted
    delete_error: Error Occured while deleting category
    menu:
      brand_universe: Brand Universe Categories
      filter_1: Filter 1
      filter_2: Filter 2
    label: Category
    add_cta:
      brand_universe: Brand Universe Category
      clubF1 : Filter 1 Category
      clubF2 : Filter 2 Category
    delete_text: Are you sure you want to delete this category ?
    new:
      brand_universe: New Brand Universe Category
      clubF1: New Filter 1 Category
      clubF2 : New Filter 2 Category
    edit:
      brand_universe: Update Brand Universe Category
      clubF1: Update Filter 1 Category
      clubF2 : Update Filter 2 Category
    list:
      empty: No Available categories
    title:
      brand_universe: Brand Universe Categories
      clubF1 : Filter 1 Categories
      clubF2 : Filter 2 Categories

  offers:
    title: Offers
    add_cta: content offer
    edit: edit content offer
    new: add content offer
    delete_text: Are you sure you want to delete this content ?
    menu: Offers
    folder_menu: Offers
    short_text: Terms and conditions
  only_you:
    menu : ONLY YOU
    presentation_menu: Presentation
    title : ONLY YOU
    add_cta: Add only you
    edit: Update only you
    new: Add only you
    delete_text: Are you sure you want to delete this content ?
  list:
    title: Title
    category: Category
    online: Online
    date_begin: Diplay date begin
    date_end: Diplay date begin
    publish_date: Publish Date
    update_date: Update Date
    state: State
    empty: No Available Content
  o2x_entretien:
    title: Maintenance
    add_cta: Maintenance Content
    edit: Update Maintenance Content
    new: Add Maintenance Content
    delete_text: Are you sure you want to delete this content ?
    menu: Maintenance
    folder_menu: Maintenance 

state:
  draft: Draft
  published: Published
# Contenu\Ciblage
contenu_menu: Contenus
ciblage_content: Ciblage

#Reset Button
reset_button: Reset Button
resetbutton_menu: Reset Button

# Availibility Settings
informative_complete: Full informative
informative_partial: Informative partial
blocking: Blocking
non_blocking: No Blocking
android: Android
ios: IOS
mymarque: Mymarque
all: All
availability_empty: 'The availability list is empty'
'Etes-vous sûr(e) de vouloir supprimer cette availability settings': 'Are you sure you want to delete this availability settings?'
availibility_type: Type
availibility_start_date: Start date
availibility_end_date: End date
availibility_start_date_label: Launch date
availibility_end_date_label: Stop Date
availibility_os: OS
availibility_settings_menu: Preventive maintenance

#prdv operations
admin_prdv_packages_family_menu: Packages Family
admin_prdv_interventions_menu: Interventions
prdv_operations: Operations
admin_prdv_premium_services_menu: Premium services
premium_services: Premium services
service_id: Service ID
internal_code: Internal code
group: Group
label: Label
creation_date: Created at
updated: Updated at
# C-Buddy
cbuddy_title: 'C-Buddy'
cbuddy_menu: 'C-Buddy'
eligibility_cbuddy_menu: CBuddy eligibility
cbuddy_image_title: CBuddy Icon
cbuddy_image_success: Image successfully added
cbuddy_image_no_selected: No selected image
cbuddy_image_delete_confirmation: Are you sure you want to delete the image?
cbuddy_image_deleted: Image successfully deleted
cbuddy_icon_label: Icon

#SAMS WSParameter
sams_marketing_ids: Configure SAMS marketingSheetIds (for contracts)
sams_marketing_ids-navco: "navco"
sams_marketing_ids-zar: "zar"
sams_marketing_ids-navcozar: "navcozar"
sams_marketing_ids-dimbo: "dimbo"
sams_marketing_ids-tmts: "tmts"
sams_marketing_ids-remotelev: "remotelev"
sams_marketing_ids-remotelev_bev: "bev"
sams_marketing_ids-remotelev_phev: "phev"
sams_marketing_ids-dscp: "dscp"
sams_marketing_ids-raccess: "raccess"
sams_marketing_ids-connectedalarm: "connectedalarm"

# User Communication
user_communication_menu: User communication
user_communication_update: Update 
user_communications: User communications
user_communication_new : Add
user_communication_updated: Element updated !
user_communication_added: Element added !
user_communication_delete: Are you sure you want to delete ?
userCommunication:
  form:
    title: Title
    short_description : Short description
    description : Description
    image : Image
ev_routing_configuration_added: the ev_routing config was added successfully

REMOTE_DEMO : Remote DEMO

#WEBRADIO
webradio_parameters_menu : Webradio
webradio_title: WEBRADIO

# Encryption key
file_uploaded_sucess: File successfully added
file_upload_error: Upload error
files_list_empty: The Encryption key files list is empty
file_label_key: Key file
file_label_target: Target
file_form_label: Encryption key management

# Minimum charge level
minimum_charge_level:
  activation: Activation
  title: Minimum charge level for precondition A/C
  hybrid_label: Min. level hybrid vehicle.
  electric_phase1_label: Min. level full electric veh. phase 1
  electric_phase2_label: Min. level full electric veh. phase 2
  menu: Minimum Charge Level A/C
  hybrid_label_title: Veh PHEV
  electric_phase1_label_title: DXD=04 & D7K<>03
  electric_phase2_label_title: DXD=04 & D7K=03
#Performed Maintenance
performed_maintenances:
  menu: Performed Maintenances
  title: Performed Maintenances
  id: Id
  brand: Brand
  type: Type
  severity: Severity
  age: Age
  distance: Distance
  performed_date: Performed Date
  created: Creation date
  updated: Update date
  mileage: Mileage
  cost: Cost
  comments: Comments
  source: Source
  reference: Reference
#mobility pass
mobility_pass_label: Mobility pass
mobility_pass_app_label: Mobility pass
mobility_pass_menu: Mobility pass
mobility_pass_url: Url

# map update
map_update_activation: MAP UPDATE
url_help: URL HELP
map_update: Map update
# mileage update
mileage_update:
  enabled: Mileage update
#sps generic
sps_generic:
  menu: Sps Generic
  with_and_without_sb: Active with and without a Silver Box
  without_sb: Active without a Silver Box
  with_sb: Active with a Silver Box
  status: Activation status
  inactive: Inactive
  deeplink: Deeplink
  space_app_deeplink: Space Deeplink

# homepage promotion
homepage_promotion:
 title : Homepage Promotion
 activation : Homepage Promotion Activation
 add : Add Homepage Promotion
 update : Update Homepage Promotion
 menu : Homepage Promotion
 deleted: Homepage Promotion was deleted sucessfully
 added: Homepage Promotion was added sucessfully
 updated: Homepage Promotion was updated sucessfully
 
# Rlev Push Notif
rlev_push_notif: RLEV Push Notif
rlevpushnotif_menu: RLEV Push Notif

# hub app
hub_app:
 title: Hub Apps
 activation: hub App Activation
 add: Add hub App
 update: Update hub App
 deleted: hub App was deleted sucessfully
 delete : Are you sure you want to delete ?
 added: hub App was added sucessfully
 updated: hub App was updated sucessfully
 settings_local_index: Hub Apps local
 settings_global: Hub Apps global
 settings_op_index: OP 
 settings_ac_index: AC 
 settings_ap_index: AP 
 settings_vx_index: VX 
 settings_ds_index: DS 
prdv_onlyyou: Prdv only you
prdv_onlyyou-lcdvs: LCDVS

exve1_label: Exve1
exve1_app_label: Exve1
exve1_menu: Exve1
# o2x
o2x_settings_local: Local O2X Configuration
configuration_global_o2x: Global O2X Configuration
o2c_global_configuration: Global AMI(O2C) Configuration
o2ov_global_configuration: Global OMU(O2OV) Configuration
o2x: 
  phone_sos: Phone SOS
  o2ov_title: OMU(O2OV)
  o2c_title: AMI(O2C)
  o2x_eligibility_title : Eligibility
  o2x_eligibility_lcdv : LCDV
  o2x_licence_title: Licence
  o2x_licence_code: Code
  o2x_indicateur_valet_code: Code
  o2x_indicateur_valet_title:  Indicateur Valet
  o2x_annuaire_title: Annuaire
  o2x_annuaire_licence_ri: Licence RI
  o2x_annuaire_licence_ercs: Licence ERCS
  o2x_version_minimale_title: Minimal version
  o2x_version_minimal_app: Minimum app version
  ws_mister_auto: MISTER AUTO
  ws_mister_auto-url: URL
  activation_controle_vehicule: Disable technical control for vehicles
  o2ov_fonctionnel_global_menu: OMU(O2OV) global
  ami_fonctionnel_global_menu: AMI(O2C) global
  o2ov_technique_global_menu: Activation Maintenance OMU(O2OV) global
  ami_technique_global_menu: Activation Maintenance AMI(O2C) global
  omu_fonctionnel_local: OMU(O2OV)

flexi_lease_app_label: Flexi Leasing
flexi_lease_web_label: Flexi Leasing
flexi_lease_rent_label: URL Rent
flexi_lease_activation: Activation
flexi_lease_menu: Flexi Leasing
pass_mobility_menu: Mobility pass

# ws parameters bta info
bta_info: WS BTA INFO
bta_info-url: URL
bta_info-checkbox_geoloc: GEOLOC
bta_info-checkbox_trajet: Check reliable trip data
bta_info-checkbox_verifie_distance: Check distance
bta_info-checkbox_distance_data: Reliable data by distance
bta_info-old_journeys: Retrieve old trips
bta_info-max_trips: Max trips

# EV-Routing
evrouting_menu: 'EV-Routing icon'
evrouting_image_title: EV-Routing Icone
evrouting_image_success: Image successfully added
evrouting_image_no_selected: No selected image
evrouting_image_delete_confirmation: Are you sure you want to delete the image?
evrouting_image_deleted: Image successfully deleted
evrouting_icon_label: Icon
# Ev routing app
SAMS_EV_ROUTING_APP: EV routing app
android_url: Android URL
ios_url: IOS URL

# sams stolen vehicle
SAMS_SECURITY: Stolen Vehicle

# Basic Consent
basic_consent : Basic consent
basic_consent_menu : Basic consent

#services_additionnels
services_additionnels:
  menu: Additional services
  deeplink: deeplink
# elq charging
elq_charging_title: ELQ Charging
elq_charging_menu: ELQ Charging
# App smile
app_smile: WS AppSmile
app_smile-app_id_android: APP ID - ANDROID
app_smile-app_id_ios: APP ID - IOS
app_smile-partner_id: Partner ID
app_smile-partner_secret: Partner Secret
app_smile_url: WS AppSmile URL
app_smile_url-url : URL

#pickup & delivery
pickup_delivery_enabled: 'Pickup & Delivery: Activation'
pickup_delivery: WS Pickup & Delivery
pickup_delivery-url: 'URL'
pickup_delivery-access_key_id: 'Access Key ID'
pickup_delivery-secret_access_key: 'Secret Access Key'

# functionnalities list
functionnalities:
  title_menu: Functionnalities List
  functionnalities_title : Functionnalities List 
  legends:
    functionnality_active: Activated Functionnality
    functionnality_disabled: Disabled Functionnality
    functionnality_active_prdv_solution_central: Active functionality in PRDV for Central solution
    functionnality_active_prdv_solution_local: Active functionality in PRDV for Locale solution
    early_adapter: Early adapter
  ASSISTANCE_CONTACT: Assistance Contact
  info_carburant: Informations carburant
  ASSISTANCE_DEALER: Dealer Assistance
  BIOMETRICS: Biometrics
  BTA: BTA
  cam: Cam
  CBUDDY: CBUDDY
  DEALER: DEALER
  DIMBO: DIMBO
  EV_ROUTING_PLANNER: EV ROUTING PLANNER

#Email Update
emailupdate: Email update
email_update_menu: Email update

# send2nav
send2nav: Send 2 Nav
send2nav_menu: Send 2 Nav

#search
search: Search...

# SmartApps last_trips
last_trips: Get Last Trip
last_trips_menu: Get Last Trip SmartApps

# WS api dealer checkbox
api_dealer-checkbox: Use v2

# RAIA (Regulation Authorities Information Access)
raia : RAIA
raia_menu : RAIA
raia_codes: Codes

# shop
shop:
  parameters_menu: Shop
  subscription_url: Url Subscription 
  url_tmts: URL TMTS
  activation_tmts: Activation TMTS 
  url_zar: Url ZAR 
  activation_zar: Activation ZAR 
  url_nav: Url NAV 
  activation_nav: Activation NAV 
  activation_navco: Activation NAVCO Activation
  activation_connected_packs: Activation Connected Packs 
  url_eboutique: Url Eboutique 
  activation: Activation

sams_url:
  url: Url
  activation: Activation
  menu: Sams urls

prod: Production
preprod: Pre-Production
rfrec: Recette
integ: Integration

#Personal Information Usage
personal_information_usage_title: Personal Information Usage
personal_information_usage_menu: Personal Information Usage

#Location Information Usage
location_information_usage_title: Location Information Usage
location_information_usage_menu: Location Information Usage


# setPartialCharge
set_partial_charge: Set Partial Charge
set_partial_charge_menu: Set Partial Charge

# Sunset 
sunset:
  level: Level
  redirect_uri_android: Android store
  redirect_uri_ios: Apple store
  title: Tilte
  body: Body
  media: Media
  menu: Sunset
  label: Sunset configuration
  disabled: Disabled
  warning: Warning
  blocking: Blocking



SAMS_EMERGENCY : Private eCall vehicle 
SAMS_GRIPMANAGEMENT: Advanced Traction Control 
SAMS_HEATEDSTEERINGWHEEL: Heated Steering Wheel
