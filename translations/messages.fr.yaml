#Menu Left-bar
dashboard: Dashboard
administration: Administration
site_creation: 'Création de site'
fo_label: 'Gestion des libellés référents'
ws_parameters: 'Paramètres WebSservices'
functionalities: Fonctionnalités
social_networks: 'Réseaux sociaux'
ds_range: 'Gestion gamme DS'
locale_translation: 'Traductions locales'
offers: Offres
mediatheque: Médiathèque
profiles_directories: 'Gestion des profils'
site_parameters: 'Paramétrage de site'
'Paramètres WS Local': 'Paramètres WS Local'
PRDV: PRDV
CGU: CGU
export_features_menu: 'Export applications'
road_assistance_menu: 'Assitance routière'
marketing_consent_menu: 'Consentement marketing'
encryption_key_menu: 'Clé de cryptage'

#Header
Utilisateur: Utilisateur
Pays: Pays
Environnement: Environnement
'Se déconnecter': Se déconnecter

#Page Select: Choisissez votre site
'Choisissez votre site': Choisissez votre site

#Page Accueil
Bienvenu: 'Bienvenue sur'
'Vous êtes authentifié comme': 'Vous êtes authentifié(e) en tant que'
Rôle: Rôle

#Page Gestion Gamme DS
'Date de création': 'Date de création'
'Date de mise à jour': 'Date de mise à jour'
'Retourner à la liste': 'Retourner à la liste'
'êtes-vous sûr de vouloir le supprimer': 'Êtes-vous sûr(s) de vouloir supprimer ce LCDV ?'
'Suppression du LCDV': 'Suppression du LCDV'

#Page Réseaux Sociaux
'Gestion des Réseaux Sociaux': 'Gestion des Réseaux Sociaux'
'Modification des Réseaux Sociaux': 'Modification des Réseaux Sociaux'
'La liste des réseaux sociaux est vide': 'La liste des réseaux sociaux est vide'
Ajouter: Ajouter
Recherche: Recherche
Modifier: Modifier
Activé: Activé
Activer: Activer
Désactivé: Désactivé
Désactiver: Désactiver
Supprimer: Supprimer
Annuler: Annuler
'Etes-vous sûr(e) de vouloir supprimer ce réseau social': 'Êtes-vous sûr(e) de vouloir supprimer ce réseau social ?'
'Etes-vous sûr(e) de vouloir désactiver ce réseau social': 'Êtes-vous sûr(e) de vouloir désactiver ce réseau social ?'
'Etes-vous sûr(e) de vouloir activer ce réseau social': 'Êtes-vous sûr(e) de vouloir activer ce réseau social ?'
'Chargement en cours': 'Chargement en cours'
'La modification a été effectuée avec succès !': 'La modification a été effectuée avec succès !'
'La supression a été effectuée avec succès !': 'La suppression a été effectuée avec succès !'

#Page FO Label
'Nouveau libellé': 'Nouveau libellé'
'add_key_referent': 'Ajouter un nouveau libellé de Référence'
'edit_key_referent': 'Modifier un libellé de Référence'
'Clé': 'Nom du libellé'
Libellé: Libellé
'Sprint de création du libellé': 'Sprint de création du libellé'
'Nom de la fonctionnalité associée au libellé': 'Nom de la fonctionnalité associée au libellé'
'Propagation marques': 'Propagation marques'
'Support': 'Support'
'Libellé référent français': 'Libellé référent français'
'Libellé référent anglais': 'Libellé référent anglais'
'Valeur du paramètre': 'Valeur du paramètre'
'Date de maj': 'Date de mise à jour'
'Fonctionnalité': 'Fonctionnalité'
Brands: Brands
Device: Device
'Clé de libellé': 'Clé de libellé'
'Libellé référent': 'Libellé référent'
'Aucun enregistrement trouvé': 'Aucun enregistrement trouvé'
'Liste des': 'Liste des'
"La valeur support n'est pas autorisée" : "La valeur %support% n'est pas autorisée"
"La valeur brand n'est pas autorisée" : "La valeur %brand% n'est pas autorisée"
Exporter: Exporter

#Locale translation
'Liste des traductions locales': 'Liste des traductions locales'
'Traduction Pays': 'Traduction Pays'
'Traductions Locales': 'Traductions Locales'
Fichier: Fichier
"file_not_allowed": "Ce type de fichier n'est pas autorisé."
'no records found': 'Aucun enregistrement trouvé'
'Choisir un fichier': 'Choisir un fichier'

#WS Parameters Customer@
'customer_at': 'WS Customer@'
'customer_at-url': 'URL'
'customer_at_clientid': 'Client ID'
'customer_at_secret': 'SECRET'

#WS Parameters BrandID
'brand_id': 'WS BrandID'
'brand_id-url': 'URL'
'brand_id_url2': 'URL'
'brand_id_url_api': 'URI BRANDID API'
'brand_id_secret': 'SECRET'

#WS Parameters APIConnect
'api_connect': 'WS API Connect'
'api_connect_url': 'URL'

#WS Parameters Corvet
'corvet': 'WS Corvet'
'corvet-url': 'URL'
'corvet-clientid': 'Client ID'

#WS Parameters visual3D
'visual3d': 'WS Visual3d'
'visual3d-url': 'URL'
'visual3d-width': 'Width'
'visual3d-height': 'Height'
#WS Parameters LVH
'lvh': 'LVH'
'lvh-checkbox': 'Activation'
'lvh-url': 'URL'
'lvh-cache': 'Durée de vie Cache'

#WS Parameters ApiMe
'api_me': 'API ME'
'api_me-url': 'URL'

#WS Parameters ApiCar
'api_car': 'API CAR'
'api_car-url': 'URL'

#WS Parameters ApiShop
'api_shop': 'API SHOP'
'api_shop-url': 'URL'

#WS Parameters ApiDealer
'api_dealer': 'API Dealer'
'api_dealer_url': 'URL'
'api_dealer_pdv_max': 'Nombre max. de PDV affichés'
'api_dealer_rayon_max': 'Rayon max. de recherche en Km'
'api_dealer-consumer': 'consumer'
'api_dealer-brand_activity': 'Brand Activity'
'api_dealer_local-code_service_valet_arcard_ac': 'Code Service Valet Arcad AC'
'api_dealer_local-code_service_valet_ds7': 'Code Service Valet DS7'
'api_dealer_local-lcdv_ds' : 'LCDV DS'
'api_dealer_local': 'API Dealer Local'

#WS Parameters PRDV ERCS
'prdv_ercs': 'PRDV ERCS'
'prdv_ercs-url': 'URL'

#WS Parameters ApiMiddleware
'api_middleware': 'API Middleware APP'
'api_middleware-url': 'URL'

#WS Parameters GoogleMap
'google_map': 'GoogleMap'
'google_map-url': 'URL'
'google_map-key': 'Clé'

#WS Parameters CVS
'cvs': 'CVS'
'cvs-url': 'URL'
'cvs-clientid': 'Client ID'

#WS Parameters APIC
'apic': 'APIC'
'apic-url': 'URL'
'apic-clientid': 'Client ID'

#Site parameters
site_name: 'Nom'
site_code: 'Code'
online_application: 'Application en ligne'
timezone: 'Fuseau Horaire'
brand: 'Marque'
site_general: Général
save: 'Enregistrer'
languages: Langues
site_languages: Langues du site
selected_values: Valeurs sélectionnées
unselected_values: Valeurs disponibles
reference_language: Langue de référence
site_settings: Paramètres Nationaux
distance_unit: Unité de distance
consumption_unit: Unité de consommation
cost: Coût
volume_unit: Unité de volume
date_format: Format de date
hour_format: Format d'heure
consumption: Consommation
distance: Distance
volume: Volume
dates: Dates
online_site: Site en ligne
ldap_code: Code LDAP 
default_civility:  Civilités Customer@ activé 
name_order: Ordre d'affichage Nom/Prénom
complementary_currencies: Monnaies complémentaires
currencies: Monnaies
domtom: Afficher le message de selection des pays pour les DOM
first_last_name: "Prénom + NOM"
last_first_name: "NOM + Prénom"

#Label filters
label_updated: Libellé à jour
label_to_update: Libellé à mettre à jour
label_to_translate: Nouveau libellé à traduire
filter: Filtrer

#Directory profiles
profil: profil
site: site
bo_access: accès BO
ws_parameters_app: Liste des WS Parameters APP
ws_parameters_web: Liste des WS Parameters WEB

#PRDV parameters
prdv_parameters_app: PRDV APP
prdv_parameters_web: PRDV WEB
prdv_activation: Activation
prdv_perimeter: Solution
prdv_url: URL
prdv_central: Centrale
prdv_local: Locale
prdv_label: Prendre un rendez-vous
quote_label: Effectuer un devis
prdv_app_label: Prise de rendez-vous (Disponible pour l'app)

#MAJ CARTO&SOFT
pc_update_url: URL de Téléchargement PC
mac_update_url: URL de Téléchargement MAC
brand_update_label: Brand Update (NAC/RCC)
nac_parameters_app: MàJ Carto & Soft APP
nac_parameters_web: MàJ Carto & Soft WEB
nac_activation: Activation
nac_carto_label: Cartographie
nac_soft_label: Logiciel (soft)
nac_url: URL
nac_menu_label: Mise à jour Système
nac_label: Mise à jour NAC
rcc_label: Mise à jour RCC
aio_label: Mise à jour AIO
smeg_label: Mise à jour SMEG
smeg_activation: Activation
smeg_carto_label: Cartographie
help_manual_url: Notice d'aide

#Eligibility vehicles
eligibility_empty: 'La liste des véhicules est vide'
'Eligibilité boitier NAC': 'Eligibilité boitier NAC'
'Eligibilité boitier RCC': 'Eligibilité boitier RCC'
'Eligibilité boitier véhicule': 'Eligibilité boitier véhicule'
'Nouveau modèle de véhicule': 'Nouveau modèle de véhicule'

#WS MAPCARE
api_mapcare: WS MAPCARE
api_mapcare-url: URL
api_mapcare-app_id: App ID
api_mapcare-secret: Secret

#WS MAJESTIC
api_majestic: WS MAJESTIC
api_majestic-url: URL
api_majestic-nac_soft_firmware: NAC software type firmware
api_majestic-nac_soft_carto: NAC software type carto
api_majestic-rcc_soft_firmware: RCC software type firmware
api_majestic-aio_soft_firmware: AIO software type firmware

#WS SAMS
api_sams: SAMS
api_sams-url_catalog: URL Catalog
api_sams-url_contrib: URL Contrib
api_sams-url_contract: URL Contract
Services connectés: Services
sams_payment_method : SAMS Payment Method
sams_payment_method-url_method_payment: URL Payment Method
#Medias
medias_list_empty: 'La liste des medias est vide'
Nom: Nom
Download: 'Téléchargé le'
'Edit image': "Modifier l'image"
Taille: Taille
confirmation_delete_image: "Êtes-vous sûr(e) de vouloir supprimer cette image ?"

#vehicle label
lcdv_model_new: Ajouter un nouveau modèle véhicule
lcdv_model_update: Modifier un modèle véhicule
lcdv_model_label: Libellé
lcdv_model_lcdv: LCDV
rpo_model_rpo: RPO
vehicle_labels: Modèles véhicules
LCDV example: Exemple LCDV 

short_label: Libellé court
lcdv_model_delete: Êtes-vous sûr(e) de vouloir supprimer ce libellé véhicule ?
lcdv_label_deleted: Libellé véhicule supprimé avec succès
lcdv_label_added: Libellé véhicule ajouté avec succès
lcdv_label_not_deleted: Erreur lors de la suppression du libellé véhicule
lcdv_label_updated: Libellé mis à jour avec succès
local_lcdv_updated: Libellés mis à jour avec succès
local_label_error: Libellés invalides
local_lcdv_update: Modifier les libellés
local_vehicle_labels: Modèles véhicules locaux

#Site creation
site_create: Créer le site
site_created_success: Site créé avec succès
site_updated_success: Site mis à jour avec succès
site_created_error: Au moins un champ obligatoire est manquant

#privacy policy
'privacy_policy': 'Gestion Politique de confidentialité'
'Politique de confidentialité': 'Politique de confidentialité'

#Registration document
regis_doc_image_success: Image ajoutée avec succès
regis_doc_image_no_selected: Aucune image selectionnée
regis_doc_image_deleted: Image supprimée avec succès
regis_doc_image_header: Visuel d'aide à la saisie du VIN
regis_doc_image_delete_confirmation: Êtes-vous sûr(e) de vouloir supprimer l'image ?
regis_doc_image_select_header: Sélection d'image
regis_doc_help_menu: Visuel Ajout Véhicule
regis_doc_help_label: <strong>Pour l'APP, ces paramètres sont pris en compte à la publication de l'application.</strong>

#Smart Banner
smart_banner_title: Smart Banner
smart_banner_menu: Smart Banner
app_icon: Icone de l'app
store_app_name: Nom de l'app
play_store_url: URL Play Store
play_store_store_id: Store iD Play Store
play_store_app_id: App iD Play Store
app_store_url: URL App Store
app_store_store_id: Store iD Apple Store
app_store_app_id: App iD Apple Store
smartbanner_home: Homepage
smartbanner_car: Page Véhicule
smartbanner_maintenance: Page Entretien
smartbanner_workshop: Page Atelier
smartbanner_connexion: Page de connexion
#favorite dealer
favorite_dealer_menu: Point de Vente Favori
favorite_dealer_label: Point de Vente Favori

legal_menu: Juridique
accessibility_menu: Accessibilité

#DEALER
dealer_settings_menu: Points de vente
dealer_radius: Rayon de recherche (km)
dealer_max: Nombre de résultats maximum
dealer_settings_near_title: Points de ventre à proximité
dealer_settings_filters_title: Filtres des points de vente
filter_services: Filtre des services ( 5 Max )
near_dealers: Nombre de résultats maximum (5 Max.)
#BUSINESS LIST
business_list_menu: Business List
business_List: Business List

#PROMOTE APP
promote_app_title: Bloc coaching & statistiques
promote_app_menu: Mise en avant APP

#SAMS
SAMS_NAVCO: NAVCO
SAMS_ZAR: ZAR
SAMS_NAVCOZAR: Bundle NAVCO /ZAR
SAMS_TMTS: TMTS
SAMS_LEV: Remote LEV
Early_Adopters : Early adopters
Sams_Webview : Sams Webview
BOUTIQUE : Boutique
connected_services : Services connectés
services : Services
apv_services: Services après-vente
SAMS_BOUTIQUE : Sams Boutique
SAMS_FULLDIGITAL: TMTS Full Digital Notifications
SAMS_CONNECTEDALARM: Connected Alarm
SAMS_DIGITALKEY: Digital Key
SAMS_AE_CALL: AE CALL
SAMS_PARTNERSERVICE: Partner Service
SAMS_TripsintheCloud: Trips in the Cloud
marketing_consent : Consentement marketing
consent_type: Type de consentement
consent_enabled: Activation
text_introduction: Texte d'introduction
consent_added:  Consentement ajouté avec succés
consent_deleted: Consentement supprimé avec succés
consent_not_deleted: Consentement non supprimé

#ASSISTANCE
assistance_title: Relation clientèle
assistance_menu: Assistance
faq_url: FAQ
contact_form_url: Formulaire contact client
form_type: Type Formulaire
assistance_phone: Télephone
assistance_brand_title: "Assistance %brand%"
assistance_dealer_title: Point de vente favori

#LogIncident
log_incident_menu: INCIDENTS
api_log_incident: LOG INCIDENTS
api_log_incident-url: URL

#SSO URLS
sso_urls_title: Liste des urls SSO
change_sso_base_url: Changer l'url de base SSO
sso_url: Url SSO
sso_pages_menu: Pages SSO
rlc_assistance_menu: Relation Client
#History logs
history_menu: Historique

#General Parameters APP
general_parameters_menu: APP general setting
findmycar_label: Find My Car
label_activation: Activation
new_app_publication_required: "nécessite une nouvelle publication de l'APP"
scanvin_label: Scan Véhicule

#Eligibility B9E
eligibility_b9e_menu: B9E Kuantic

#API MS user data
api_ms_user_data: MS USER DATA
api_ms_user_data-url: URL

#Eligibility Smartapps
eligibility_smartapps_menu: Eligibility Smartapps
eligibility_smartapps_altran_menu: ALTRAN
eligibility_smartapps_cea_menu: CEA

#Eligibility Boitier
eligibility_boitier_menu: Eligibility Boitier
eligibility_boitier_b9e_menu: B9E
eligibility_boitier_nac_menu: NAC
eligibility_boitier_rcc_menu: RCC
eligibility_boitier_aio_menu: AIO

#export 
schedule_export: 'Nouvelle demande d''export'
export_list: 'Liste des exports'
trans_files_export: 'Export des fichiers'
export_request_success: 'La demande d''export a bien été enregistrée'
requestedAt: 'date de la demande'
export: Exporter

#videos
title: Titre
videos_title: Gestion des PlayLists et Vidéos
videos_id: ID Youtube
videos_types: Type
priority: Priorité
video_modal_delete: "Êtes-vous sûr(e) de vouloir le supprimer ?"
video_updated: "Modifié avec succes"
video_added: "Ajouté avec succes"
video_deleted: "Supprimé avec succes"
video_not_deleted: "Erreur lors de la suppression"
videos_new:  Ajouter une PlayList (ou Vidéo)
videos_update: Modifier une PlayList (ou Vidéo)
videos_lcdv: Liste des LCDV
video_tutorial_menu: Vidéo Tutorielles
promote_videos_new:  Mettre en avant une vidéo
promote_videos_update: Modifier une Vidéo mise en avant
promote_videos_title: Gestion des vidéos mises en avant
promote_videos_menu: Mise en avant vidéo
api_youtube: API YOUTUBE V3
api_youtube-url: URL
api_youtube-clientid: Client ID
api_youtube-tags_prefix: Préfixe des tags
playlist_or_video: PlayList/Vidéo


#Whiteliste
vehicle_whitelist_empty: 'Aucune VIN trouvée'
Nouveau véhicule White list : 'Nouveau véhicule dans la White list'
Modifier le véhicule : 'Modifier le véhicule  '
Vin : 'VIN'
Marque : 'Marque'

#road assistance
provider: Fournisseur
phone: N° Téléphone

#custom messages
connected_services_message: Services connectés
welcome_message: Message de Bienvenue
custom_messages_title: Message Personnalisés
before: Avant
after: Après
position_in_time: Position dans le temps
display_weight: Poids d'affichage
days_number: Nombre de jours
add_new_message: Ajouter un nouveau message
custom_message_menu: Messages personnalisés
maintenance_reminder_message: Rappel entretien
favorite_dealer_message: Point de vente favori
my_services_message: Mes services
dimbo_discover_message: Dimbo - Discover
dimbo_lead_message: Dimbo - Lead
reinsurance_message: Message de réassurance

# ws parameters salesforce marketing cloud
salesforce_marketing_cloud: Salesforce Marketing Cloud
salesforce_marketing_cloud-url: Marketing Cloud Server URL
salesforce_marketing_cloud-id: ID
salesforce_marketing_cloud-token: Token
salesforce_marketing_cloud-mid: MID
salesforce_marketing_cloud-checkbox_etanalytics: Etanalytics
salesforce_marketing_cloud-checkbox_pianalytics: Pianalytics
salesforce_marketing_cloud-checkbox_location: Location
salesforce_marketing_cloud-checkbox_inbox: Inbox

# maitenance parameters
maitenance_config_title: Paramètres de la maintenance
activation: Activation
docsoa_esa_type: DOCSOA/ESA
dimbo: DIMBO
severe: Sévère
normal: Normal
maintenane_menu: Maintenance
esa: FCS ESA
esa-url: URL
esa-clientid: Client ID
forfait: WS Forfait
forfait-url: URL
forfait-clientid: Client ID
forfait-certname: Cert Name
forfait-passphrase: Pass Phrase
forfait_maintenance: WS Forfait Entretien
forfait_maintenance-url: URL
forfait_maintenance-clientid: Client ID
forfait_maintenance-certname: Cert Name
forfait_maintenance-passphrase: Pass Phrase

#API MS user data
api_sys_dimbr_data: SYS DIMBR DATA
api_sys_dimbr_data-url: URL

promote_video: Mise en avant
maintenance_use_condition: Condition d'utilisation

precontrol_activation: Pré-controle technique
precontrol_title: Controle technique
operations_label: Libellé Opération systématique

repetitions: Répétitions
interval: Intervale
interval_unit: Unité d'intervale
alert: Alerte
alert_unit: Unité d'alerte
month: Mois
year: Année(s)

control_modal_delete: Êtes-vous sûr(e) de vouloir supprimer ce control ?
technical_controls_title: Contrôles techniques
technical_controls_title_add: Ajouter un Contrôle technique
technical_controls_title_edit: Modifier un Contrôle technique
maintenance: Maintenance
maintenance-steps: Steps
maintenance_precontrol_menu: Controle Technique

#eligibilities
eligibilities_o2c_title: AMI(O2C)
eligibilities_ami_menu: AMI(O2C)
eligibilities_o2c_menu: AMI(O2C)
eligibilities_o2c_list: Liste d'éligibilités O2C
eligibilities_o2c_add: Ajouter un nouveau modèle O2C
eligibilities_o2c_edit: Modifier un modèle O2C

use_middleware_v2: Utilisation de la maintenance V2
middleware_maintenance_v2_menu: Maintenance V2

#model vehicl default image
vehicle_default_image_label: Visuel véhicule par defaut
vehicle_default_image_success: Le visuel par défaut du véhicule a été mis à jour avec succès
vehicle_default_image_select_valid_media: Veuillez ajouter une image valide

#advisor settings
advisor_menu: Advisor
ws_advisor: Advisor
ws_advisor-url: URL
ws_advisor-app_id: App ID
ws_advisor-client_id: Client ID
ws_advisor-certificate: Certificat
ws_advisor-certificate_key: Clé du certificat
ws_advisor-certificate_pass_phrase: Passe phrase du certificat

#ereca settings
recall_campaign_menu: Compagnes de rappel
recall_campaign: Compagnes de rappel
ws_ereca: eRECA
ws_ereca-certificate: Certificat
ws_ereca-client_id: Client ID
ws_ereca-auth_http_login: Auth HTTP Login
ws_ereca-auth_http_password: Auth HTTP Password
ws_ereca-certificate_pass_phrase: Passe phrase du certificat
ws_ereca-url: URL

#form invalid message
form_invalid_global_message: Des entrées sont invalides

priority_information_message: 1 à 10 signifie une priorité élevée à une priorité moindre
choose_brand_title: Marque
choose_country_title: Pays
choose_profile_title: Profile
choose_brand: Choisissez une marque
choose_country: Choisissez un Pays
choose_profile: Choisissez votre profile

#Consent Marketing
consent_type_enabled: Activation du consentement
consent_type_new: Ajouter un type de consentement
consent_type_title: Type de consentement
consent_type_text: Libellé
consent_type_customerat: Champ d'embasement dans customer@
help_marketing_consent: Aide à la saisie
type_modal_delete: "Êtes-vous sûr(e) de vouloir le supprimer ?"
type_updated: "Modifié avec succes"
type_added: "Ajouté avec succes"
type_deleted: "Supprimé avec succes"
type_not_deleted: "Erreur lors de la suppression"

#CGU
cgu_confirm_delete: Etes-vous sûr de vouloir supprimer cette version de CGU ?
update_date: Dernière mise à jour
enabled: Activé
new_cgu_version: Nouvelle Version CGU

# ws parameters
v1_bo: BO V1
v1_bo-url: URL
#lcv_stellantis
lcv_stellantis: Lcv stellantis

#CFirst parameters
api_customer_first-url: 'URL'
api_customer_first-user: 'USER'
api_customer_first-source: 'Source'
api_customer_first-secret: 'Secret'
api_customer_first-certname: 'Certificat'
api_customer_first: CFirst
api_sys_customer_first: API SYS CFirst

#Documentation
documentation: Documentation
scan_my_car: Scan My Car
scan_my_car_title: Scanner Ma Voiture

#ws IMA
ws_ima: IMA
ws_ima-url: URL
ws_ima-id: ID
ws_ima-secret: SECRET

#OLB Agenda
ws_agenda: Agenda
ws_agenda-url: URL
ws_agenda-client_id: Client ID
ws_agenda-pass_phrase: Certificate Pass Phrase
ws_agenda-cert_name: Certificat (.cer & .key)

#OLB PRDV
ws_prdv: PRDV
ws_prdv-url: URL

# CFirst System parameters
api_sys_customer_first-url: URL
api_sys_customer_first-months: Mois

#SettingsConfig
settings_configuration: Settings Configuration

trace_connection_menu: Logs connections
connection_trace_title: Logs connections


#Charging tips faq
charging_tips_faq : Charging tips FAQ
charging_empty : La liste est vide 
charging_tips_faq_add : Ajouter un charging tips FAQ
charging_modal_delete :  Êtes-vous sûr(e) de vouloir le supprimer ?
charging_tips_faq_edit : Modifier un charging tips FAQ 

#My account sams
myaccount_sams : MyAccount SAMS
myaccount_sams-url: URL

# SSO
api_sso : SSO
api_sso-url: URL

# Remote Acces
SAMS_RACCESS : Remote ACCES 
#API SYS Corvet
api_sys_corvet: 'API SYS Corvet'
api_sys_corvet-url: 'URL'

#DS ONLY YOU
ds_only_you_menu: DS ONLY YOU
oly_title: Titre
oly_description: Description
oly_assistance_doc_url: Url Doc d'assistance
oly_assistance_phone: Télephone d'assistance
oly_image: Image

# Remote Acces
SAMS_ACCES : Remote ACCES 

SAMS_DSCP: PRIVILEGE

SECTION-DS_ONLY_YOU: DS ONLY YOU
SECTION-SAMS_DSCP: DS PRIVILEGE
SECTION-DS_VALET: DS VALET

delete_version_dialog_title: Suppression de la version
delete_version_dialog_body: Êtes-vous sûr(s) de vouloir supprimer cette version ?
update_version_title: Modifier la version
version_app: 
app_version_menu: Versions de l'app
app_version_list: Liste des versions de l'app
app_version_ajout: Ajout des versions de l'app
published: publié
version_delete_error: Cette version ne peut pas être supprimée car utilisée par d'autres fonctionnalités
published_date: Date de publication
hivebrite: HIVEBRITE
hivebrite-url: SSO URL

SECTION-DS_RENT: DS RENT
rent_ftm_url: URL FreeToMove
rent_ftm_url_member: URL FreeToMove (Membre)
rent_ftm_url_no_member: URL FreeToMove (Pas Membre)

oly_assistance_doc: Document d'assistance
oly_assistance_doc_info: Document d'assistance (Configurable depuis la médiatheque)

#Usabila
usabila_event_1 : 'Event 1'
usabila_event_2 : 'Event 2'
usabila_event_3 : 'Event 3'
usabila_event_4 : 'Event 4'
usabila_event_5 : 'Event 5'
usabila_event_6 : 'Event 6'
usabila_event_7 : 'Event 7'
usabila_event_8 : 'Event 8'
usabila_menu : 'Usabila'

# Brand Univers Categories
brand_univers_menu : Univers de marque
brand_univers_content : Univers de marque
brand_univers_categories : Catégories univers de marque
brand_univers_categories_new_title : Ajouter une nouvelle categorie
new_categorie : Nouvelle categorie
categories_list_empty : la liste des categories est vide
confirmation_delete_categorie : Êtes-vous sûr(e) de vouloir supprimer cette catégorie Univers de marque ?

# Payment Method
payment_method: Méthode paiement
paymentmethod_menu: Méthode paiement


play_storeurl: Store URL
play_activation: Activation


#GDPR
#Unsubscribe
unsubscribe_menu : Unsubscribe
unsubscribe_title : Unsubscribe
unsubscribe_parag1 : Ces paramètres ne seront pris en compte qu'à la publication de l'application.
unsubscribe_parag2 : Ce libellé doit expliquer à l'utilisateur que la désinscription entraine uniquement une suppression des données stockées dans l'application. Cette mention est détaillée dans les Conditions générales d'utilisation de l'application.
unsubscribe_parag3 : Libellé à faire valider avec le juridique de la filiale.
#Cookie policy
cookie_policy_menu: Cookie policy
cookie_policy_title: Cookie policy
#Privacy policy
privacy_policy_menu: Privacy policy
privacy_policy_title: Privacy policy

#manage update
manage_update:
  os_version_min: OS version minimum
  mym_version_min: MyM version minimum 
  mym_version_online: MyM version en ligne
  start_date_nr: Date lancement NR
  end_date_certif: Date d'expiration certif
  dialog_title: Suppression de l'update
  dialog_body: Êtes-vous sûr(s) de vouloir supprimer cette update ?
  version: Type
  list: LISTE UPDATES 
  ajout: AJOUT UPDATE 
  title: MODIFIER UPDATE 
  menu: Manage Update

# Ev Routing
ev_routing: Ev Routing
ev_routings: Ev Routing
ev_routing_new: Ajouter
ev_routing_update: Modifier
ev_routing_delete: Êtes-vous sûr(e) de vouloir supprimer ce paramétrage ?
ev_routing_label: libellé
ev_routing_lcdv: LCDV
ev_routing_rpo: RPO
ev_routing_engine_type: Engine TYPE
ev_routing_csciphRg1a: Constant speed consumption Kwh/100Km
ev_routing_max_charging: Max charging 
ev_routing_max_speed: Max speed
ev_routing_weight: weight
ev_routing_axle_weight: Axle weight
ev_routing_length: length
ev_routing_width: Width
ev_routing_height: Height
ev_routing_acceleration_efficiency: Acceleration efficiency
ev_routing_deceleration_efficiency: Deceleration efficiency
ev_routing_uphill_efficiency: UpHill efficiency
ev_routing_downhill_efficiency: DownHill efficiency
ev_routing_charging_curve_array: Curve array
ev_routing_planner : Ev Routing Planner
ev_routing_planner_menu : Ev Routing Planner
ev_routing_configuration_updated : Ev Routing a bien été modifé

# DeepLinking
deep_linking: 
  title: Deeplinking
  list: Liste des pages deeplinking
  url: URL
  full_url: URL deeplinking
  desktopurl: URL desktop
  menu: Deeplinking
deep_linking_page:
  name: Page
  dialog_title: Supression de la page
  dialog_body: Êtes-vous sûr(e) de vouloir supprimer cette page ?
  title: Modifier la page
  page: Page
  menu: Deeplinking Page
  list: Liste des pages  
  delete_error: Cette version ne peut pas être supprimée car elle est utilisée par d'autres fonctionnalités
update_url: Modification de la page
plp:
  section_llp: Lion Loyalty Program
  section_pillar: Pillar %order%
  section_vehicle_courtesy: Vehicle Courtesy
  section_accessories: Accessoires
  llp_menu: Slider
  llp_parent_menu: Lion Loyalty Program
  llp_tcs_menu: "T&C’s Subscription"
  form:
    llp:
      title: Titre (Pas encore membre + membre)
      description_member: Description (Membre)
      description_no_member: Description (Pas encore membre)
      image_member: Image (Membre)
      image_no_member: Image (Pas encore membre)
      vc_cgu_url: Url des conditions générales
llp_tcs_title: Termes et conditions

electric_range: Gamme electrique
global_vehicle_settings: Configuration globale du vehicule
local_vehicle_settings: Configuration local du vehicule
bta_disable_electric: Désactiver BTA pour les vehicules électriques
smart_apps_disable_thermic: Désactiver SMART APPS pour les vehicules thermique
smart_apps_disable_electric: Désactiver SMART APPS pour les vehicules électriques
cea_disable_electric: Désactiver CEA pour les vehicules électriques
versions_3m2_ivi: Versions 3M2 IVI
ivi_range: Gamme IVI

SECTION-DS_ACCESSORIES: DS ACCESSORIES
loyalty_accessory:
  title: Titre de l'accessoire
  description: Description
  cta: URL CTA de l'accessoire
  delete_accessory: Enlever cet accessoire
  add_accessory: Ajouter un accessoire
  store_url: Url Store de l'accessoire (non membres)
  info: L'ajout, modification ou suppression d'un accessoire ne prendra effet qu'à l'enregistrement du formulaire
  labelCta: Libellé CTA

vehicle_owners_manual:
  activation: Activation
  webview_url: Url Webview
  section: Vehicle Owners's Manual
  menu: Vehicle Owners's Manual

#third_part_eligibilities
third_part_eligibilities: Eligibilité Application Tièrces
eligibilities_start_menu: START
eligibilities_scan_menu: SCAN
eligibilities_scan2_menu: SCAN2
eligibilities_cam_menu: CAM

bta: 
  connect_pack: 
    menu : Connect pack
    activation: Activation
    list: List vins
    name: vin
    ajout: Ajout vins
    title: Edit vin
    dialog_title: Suppression du vin connect pack
    dialog_body: Êtes-vous sûr(s) de vouloir supprimer ce vin ?
  eligibility:
    menu: Bta eligibility

content:
  bo_title: Titre BO
  fo_title: Titre FO
  targeting: Ciblage
  stylesheet:
    title: Feuille de Style
    light: Claire
    dark: Sombre
  category: Categorie
  introduction: Introduction
  visual: Visuel
  add_media: Ajouter un média
  delete_media: Supprimer un média
  add_paragraph: Ajouter un paragraphe
  delete_paragraph: Supprimer un paragraphe
  publication_date: Date de publication affichée	
  start_date: Date de début affichée
  end_date: Date de fin affichée
  publish: Publier
  put_draft: Mettre en tant que Brouillon
  push_online: Mettre en ligne
  push_offline: Mettre hors ligne
  deleted: Contenu supprimé avec succes
  delete_error: Erreur lors de la suppression du contenu
  content_text: Texte
  sub_title: Sous-titre
  url: URL
  paragraph:
    text_identifier: Texte N°
    text:  Texte
    legend: Légende du visuel
    visual: Visuel
    isLink: Lier à la carte
  cta:
    template:
      title: Template
      details: Détails
      internal: Lien interne
      external: Lien externe
    label: Label du CTA
    url: URL
    details:
      label: CTA Label
      url: URL
      opening: Mode d'ouverture CTA
    redirect:
      self: CTA Interne
      blank: CTA Externe
  seo:
    url: Clear URL
    canonical_url: Url canonique (Meta)
    rewrites: Redirects (code 301)
    priority: priorité
    robots:
      label: Robots
      empty: Empty
      all: All
      no: No
      follow: noindex, follow
      index: index, nofollow
  visuel_voyant:
    title : Visuel voyant
    VOYANT_FEUX_DE_POSITION : FEUX DE POSITION
    VOYANT_FEUX_DE_CROISEMENT : FEUX DE CROISEMENT
    VOYANT_FEUX_DE_ROUTE : FEUX DE ROUTE
    VOYANT_PROJECTEURS_ANTIBROUILLARD_AVANT : PROJECTEURS ANTIBROUILLARD AVANT
    VOYANT_FEUX_ANTIBROUILLARD_ARRIERE : FEUX ANTIBROUILLARD ARRIÈRE
    VOYANT_PRECHAUFFAGE_MOTEUR_DIESEL : PRÉCHAUFFAGE MOTEUR DIESEL
    VOYANT_FREIN_DE_STATIONNEMENT : FREIN DE STATIONNEMENT
    VOYANT_STOP_AND_START : STOP &amp; START
    VOYANT_SYSTEME_AIRBAG_PASSAGER_ACTIVE : SYSTÈME D’AIRBAG PASSAGER ACTIVÉ
    VOYANT_PIED_SUR_LE_FREIN : PIED SUR LE FREIN
    VOYANT_ESSUYAGE_AUTOMATIQUE : ESSUYAGE AUTOMATIQUE
    VOYANT_SYSTEME_AIRBAG_PASSAGER_DESACTIVE : SYSTÈME D’AIRBAG PASSAGER DÉSACTIVÉ
    VOYANT_ESP_ASR : ESP/ASR
    VOYANT_STOP : STOP
    VOYANT_SERVICE : SERVICE
    VOYANT_FREINAGE : FREINAGE
    VOYANT_ABS : ANTIBLOCAGE DES ROUES (ABS)
    VOYANT_SYSTEME_AUTODIAGNOSTIC_MOTEUR : SYSTÈME D’AUTODIAGNOSTIC MOTEUR
    VOYANT_NIVEAU_MINIMUM_CARBURANT : NIVEAU MINIMUM DE CARBURANT
    VOYANT_TEMPERATURE_MAX_LIQUIDE_REFROIDISSEMENT : TEMPÉRATURE MAXIMUM DU LIQUIDE DE REFROIDISSEMENT
    VOYANT_PRESSION_HUILE_MOTEUR : PRESSION D’HUILE MOTEUR
    VOYANT_CHARGE_BATTERIE : CHARGE BATTERIE
    VOYANT_PORTES_OUVERTES : PORTE(S) OUVERTE(S)
    VOYANT_AIRBAGS : AIRBAGS
    VOYANT_CEINTURE_NONBOUCLEE : CEINTURE NON-BOUCLÉE/DÉCOUCLÉE
    VOYANT_DIRECTION_ASSISTEE : DIRECTION ASSISTÉE
  voyant_assistance: 
    menu : Voyants assistance
    title : Voyants assistance
    add_cta: Ajout voyant assistance
    edit: Modifier un voyant assistance
    new: Ajouter un voyant assistance
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
  prefere_total:
    menu : Préfère Total
    title : Préfère Total
    add_cta: Ajout préfère total
    edit: Modifier un préfère total
    new: Ajouter un préfère total
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    logo: Préfère Total Logo
  info_carburant:
    menu : Information carburant
    title : Information carburant
    add_cta: Ajout information carburant
    edit: Modifier une information carburant
    new: Ajouter une information carburant
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
  brand_universe:
    title: Univers de marque
    add_cta: Contenu Univers de marque
    edit: Modifier un contenu Univers de marque
    new: Ajouter un Contenu Univers de marque
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Univers de marque
    folder_menu: Univers de marque
  club:
    title: Club
    add_cta: Contenu Club
    add_events: Contenu Évènements
    add_benefits: Contenu Privilèges
    edit: Modifier un contenu
    new_event: Ajouter un Contenu Évènements
    new_benefit: Ajouter un Contenu Privilèges
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Club
    folder_menu: Club
    events: Évènements
    benefits: Privilèges
    club_membership_charter_menu: Charte d'adhésion
    club_memebership_charter_title: Charte d'adhésion
    events_fields:
      eventDate: Date de l'évènement
      nbParticipants: Participants max   
      booking : Réservation
      reservationStartDate: Date de début
      reservationEndDate: Date de fin
      isComplete: Complet
      location: Localisation
      city: Ville  
      country: Pays 
      latitude: Latitude
      longitude: Longitude
    benefits_fields:
      type: Type
      validity: Validité
      nbParticipants: Participants max.
      code: Code coupon
      label: libelle CTA 
      url: url CTA 
      booking: Réservation
      direct: Coupon direct
      mail: Coupon par mail   
  o2x_boutique:
    title: Boutique
    add_cta: Contenu Boutique
    edit: Modifier un contenu Boutique
    new: Ajouter un Contenu Boutique
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Boutique
    folder_menu: Boutique   
  o2x_services:
    title: Service
    add_cta: Contenu Service
    edit: Modifier un contenu Service
    new: Ajouter un Contenu Service
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Services
    folder_menu: Services      
  categories:
    deleted: Catégorie supprimé avec succes
    delete_error: Erreur lors de la suppression du catégorie
    menu:
      brand_universe: Catégories Univers de marque
      filter_1: Filtre 1
      filter_2: Filtre 2
    label: Catégorie
    add_cta:
      brand_universe: Catégorie Univers de marque
      clubF1: Catégorie Filtre 1
      clubF2: Catégorie Filtre 2
    delete_text: Êtes-vous sûr de vouloir supprimer cette catégorie ?
    new:
      brand_universe: Nouvelle Catégorie Univers de marque
      clubF1: Nouvelle Catégorie Filtre 1
      clubF2: Nouvelle Catégorie Filtre 2
    edit:
      brand_universe: Modifier une Catégorie Univers de marque
      clubF1: Modifier une Catégorie Filtre 1
      clubF2: Modifier une Catégorie Filtre 2
    list:
      empty: Pas de catégories disponibles
    title:
      brand_universe: Catégories Univers de marque
      clubF1: Catégories Filtre 1
      clubF2: Catégories Filtre 2
  offers:
    title: Offres
    add_cta: Contenu offre
    edit: Modifier un contenu offre
    new: Ajouter un Contenu Offre
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Offres
    folder_menu: Offres
    short_text: Conditions générales
  only_you:
    menu : ONLY YOU
    presentation_menu: Présentation
    title : ONLY YOU
    add_cta: Add only you
    edit: Update only you
    new: Add only you
    delete_text: Are you sure you want to delete this content ?
  list:
    title: Titre
    category: Categorie
    online: En ligne
    date_begin: Date Début
    date_end: Date Fin
    publish_date: Date de publication
    update_date: Date de mise à jour
    state: Status
    empty: Pas de contenu disponible
    visuel_voyant : Visuel voyant
  o2x_entretien:
    title: Entretien
    add_cta: Contenu Entretien
    edit: Modifier un contenu Entretien
    new: Ajouter un Contenu Entretien
    delete_text: Êtes-vous sûr de vouloir supprimer ce contenu ?
    menu: Entretien
    folder_menu: Entretien 

state:
  draft: Brouillon
  published: Publié

#Consent declaration
consent_declaration_menu: Consent declaration
consent_declaration_title: Consent declaration
#Consent withdrawal
consent_withdrawal_menu: Consent withdrawal
consent_withdrawal_title: Consent withdrawal
#Cookies univers de marque
cookie_univers_menu: Cookies univers de marque
cookie_univers_title: Cookies univers de marque
#CGU - RemoteLev
removelev_cgu_menu: CGU - RemoteLev
removelev_cgu_title: CGU - RemoteLev
biometrics : Biométrie
biometrics_menu : Biométrie

SECTION-DS_ASSISTANCE: DS ASSISTANCE
valet_cgu_url: Url des conditions générales

# Contenu\Ciblage
contenu_menu: Contenus
ciblage_content: Ciblage
new_ciblage : Nouveau ciblage
ciblages_list_empty : la liste des ciblages est vide
confirmation_delete_ciblage : Êtes-vous sûr(e) de vouloir supprimer ce ciblage ?
ciblage_new_title : Ajouter un nouveau ciblage
ciblage_edit_title : Modifier un ciblage

# Brand Univers
new_brand_univer : Nouveau Brand Univer
univers_list_empty : la liste des Brand Univers est vide
brand_univer_title : Titre
brand_univer_category : Catégorie
brand_univer_published : En ligne
brand_univer_start_show : Date début d'affichage
brand_univer_end_show : Date de fin d'affichage
brand_univer_published_date : Date de publication
brand_univer_maj_date : Date de mise à jour
brand_univer_status : Etat
confirmation_delete_univers : Êtes-vous sûr(e) de vouloir supprimer ce Brand Univers ?
brand_univers_new_title : Ajouter un nouveau Brand Univer

#Reset Button
reset_button: Bouton de Réinitialisation
resetbutton_menu: Bouton de Réinitialisation

# Availibility Settings
informative_complete: Informative complete
informative_partial: Informative partiel
blocking: blocage
non_blocking: Non blocage
android: Android
ios: IOS
mymarque: Mymarque
all: Tout
availability_empty: 'La liste des paramètres de disponibilité est vide'
'Etes-vous sûr(e) de vouloir supprimer cette availability settings': 'Etes-vous sûr(e) de vouloir supprimer ce paramètre de disponibilité?'
availibility_type: Type
availibility_start_date: Date de debut
availibility_end_date: Date de fin
availibility_start_date_label: Date Lancement
availibility_end_date_label: Date Arrêt
availibility_os: OS
availibility_settings_menu: Maintenance Préventive

#prdv operations
admin_prdv_packages_family_menu: Famille de forfaits
admin_prdv_interventions_menu: Interventions
prdv_operations: Operations
admin_prdv_premium_services_menu: Services premiums
premium_services: Services premiums
service_id: ID Service
internal_code: Code interne
group: Groupe
label: Libelle
creation_date: Date de creation
updated: Mise à jour

# C-Buddy
cbuddy_title: 'C-Buddy'
cbuddy_menu: 'C-Buddy'
eligibility_cbuddy_menu: CBuddy eligibility
cbuddy_image_title: CBuddy Icone
cbuddy_image_success: Image ajoutée avec succès
cbuddy_image_no_selected: Aucune image selectionnée
cbuddy_image_delete_confirmation: Êtes-vous sûr(e) de vouloir supprimer l'image ?
cbuddy_image_deleted: Image supprimée avec succès
cbuddy_icon_label: Icone

#SAMS WSParameter
sams_marketing_ids: Configurer les marketingSheetIds de SAMS (pour les contrats)
sams_marketing_ids-navco: "navco"
sams_marketing_ids-zar: "zar"
sams_marketing_ids-navcozar: "navcozar"
sams_marketing_ids-dimbo: "dimbo"
sams_marketing_ids-tmts: "tmts"
sams_marketing_ids-remotelev: "remotelev"
sams_marketing_ids-remotelev_bev: "bev"
sams_marketing_ids-remotelev_phev: "phev"
sams_marketing_ids-dscp: "dscp"
sams_marketing_ids-raccess: "raccess"
sams_marketing_ids-connectedalarm: "connectedalarm"

# User Communication
user_communication_menu: User communication
user_communication_update: Modifier 
user_communications: User communications
user_communication_new : Ajouter
user_communication_updated: Element modifié !
user_communication_added: Element ajouté !
user_communication_delete: Êtes-vous sûr(e) de vouloir le supprimer ?
userCommunication:
  form:
    title: Titre
    short_description : Short description
    description : Description
    image : Image
ev_routing_configuration_added: config ev_routing a bien été ajoutée

REMOTE_DEMO : Remote DEMO

#WEBRADIO
webradio_parameters_menu : Webradio
webradio_title: WEBRADIO

# Encryption key
file_uploaded_sucess: Fichier uploadé avec succès
file_upload_error: Erreur d'upload
files_list_empty: La liste des fichiers de Encryption key est vide
file_label_key: Fichier de clé
file_label_target: Target
file_form_label: Gestion clés de cryptage

#Performed Maintenance
performed_maintenances:
  menu: Maintenances Réalisées
  title: Maintenances Réalisées
  id: Id
  brand: Marque
  type: Type
  severity: Sévérité
  age: Age
  distance: Distance
  performed_date: Date de maintenance
  created: Date de création
  updated: Date de modification
  mileage: mileage
  cost: cout
  comments: commentaires
  source: source
  reference: référence
#mobility pass
mobility_pass_label: Mobility pass
mobility_pass_app_label: Mobility pass
mobility_pass_menu: Mobility pass
mobility_pass_url: Url

# Minimum charge level
minimum_charge_level:
  activation: Activation
  title: Niveau de charge minimal pour la condition préalable A/C
  hybrid_label: Niveau Min. hybrid véhicule.
  electric_phase1_label: Niveau Min. véh. entièrement électrique phase 1
  electric_phase2_label: Niveau Min. véh. entièrement électrique phase 2
  menu: Niveau de charge minimum A/C
  hybrid_label_title: Veh PHEV
  electric_phase1_label_title: DXD=04 & D7K<>03
  electric_phase2_label_title: DXD=04 & D7K=03
# map update
map_update_activation: MAP UPDATE
url_help: URL HELP
map_update: Map update
# mileage update
mileage_update:
  enabled: Mileage update

# homepage promotion

homepage_promotion:
 title : Homepage Promotion
 activation : Activation Homepage Promotion 
 add : Ajouter Homepage Promotion
 update : Modifier Homepage Promotion
 menu : Homepage Promotion
 deleted: Homepage Promotion a bien été suprimé
 added: Homepage Promotion a bien été qjouté
 updated: Homepage Promotion a bien été modifié

 
#sps generic
sps_generic:
  menu: Sps Generic
  with_and_without_sb: Active with and without a Silver Box
  without_sb: Active without a Silver Box
  with_sb: Active with a Silver Box
  status: Status d'activation
  inactive: Inactive
  deeplink: Deeplink
  space_app_deeplink: Space Deeplink



# hub app

hub_app:
 title : Hub Apps
 activation : Activation App Hub
 add : Ajouter App Hub 
 update : Modifier App Hub
 deleted: App Hub a bien été suprimé
 delete : Êtes-vous sûr(e) de vouloir le supprimer ?
 added: App Hub a bien été ajouté
 updated: App Hub a bien été modifié
 settings_local_index: Hub Apps local
 settings_global: Hub Apps global
 settings_op_index: OP 
 settings_ac_index: AC 
 settings_ap_index: AP 
 settings_vx_index: VX 
 settings_ds_index: DS 
prdv_onlyyou: Prdv only you
prdv_onlyyou-lcdvs: LCDVS

exve1_label: Exve1
exve1_app_label: Exve1
exve1_menu: Exve1
# O2X
o2x_settings_local: Configuration O2X Local
configuration_global_o2x: Configuration O2X Global
o2c_global_configuration: Configuration Global AMI(O2C)
o2ov_global_configuration: Configuration Global OMU(O2OV)
o2x:
  phone_sos: Numéro SOS
  o2ov_title: OMU(O2OV)
  o2c_title: AMI(O2C)
  o2x_eligibility_title: Eligibilité
  o2x_eligibility_lcdv: LCDV
  o2x_licence_title: Licence
  o2x_licence_code: Code
  o2x_indicateur_valet_code: Code
  o2x_indicateur_valet_title:  Indicateur Valet
  o2x_annuaire_title: Annuaire
  o2x_annuaire_licence_ri: Licence RI
  o2x_annuaire_licence_ercs: Licence ERCS
  o2x_version_minimale_title: Version minimal
  o2x_version_minimal_app: Version minimal d'app
  mister_auto: MISTER AUTO
  mister_auto-url: URL
  activation_controle_vehicule: Désactiver contrôle technique pour les véhicules
  o2ov_fonctionnel_global_menu: OMU(O2OV) global
  ami_fonctionnel_global_menu: AMI(O2C) global
  o2ov_technique_global_menu: Activation Maintenance OMU(O2OV) global
  ami_technique_global_menu: Activation Maintenance AMI(O2C) global
  omu_fonctionnel_local: OMU(O2OV)

flexi_lease_app_label: Flexi Leasing
flexi_lease_web_label: Flexi Leasing
flexi_lease_rent_label: URL Rent
flexi_lease_activation: Activation
flexi_lease_menu: Flexi Leasing
pass_mobility_menu: Pass Mobilité

# ws parameters bta info
bta_info: WS BTA INFO
bta_info-url: URL
bta_info-checkbox_geoloc: GEOLOC
bta_info-checkbox_trajet: Vérifier les données fiables des trajets
bta_info-checkbox_verifie_distance: Vérifier la distance
bta_info-checkbox_distance_data: Données fiables par distance
bta_info-old_journeys: Récupérer les anciens trajets
bta_info-max_trips: Max trips

evrouting_menu: 'EV-Routing icon'
evrouting_image_title: EV-Routing Icone
evrouting_image_success: Image ajoutée avec succès
evrouting_image_no_selected: Aucune image selectionnée
evrouting_image_delete_confirmation: Êtes-vous sûr(e) de vouloir supprimer l'image ?
evrouting_image_deleted: Image supprimée avec succès
evrouting_icon_label: Icone
# Ev routing app
SAMS_EV_ROUTING_APP: EV routing app
android_url: Android URL
ios_url: IOS URL
# Rlev Push Notif
rlev_push_notif: RLEV Push Notif
rlevpushnotif_menu: RLEV Push Notif

# sams stolen vehicle
SAMS_SECURITY: Stolen Vehicle
# Basic Consent
basic_consent : Basic consent
basic_consent_menu : Basic consent

#services_additionnels
services_additionnels:
  menu: Services additionnels
  deeplink: deeplink
# elq charging
elq_charging_title: Chargement ELQ
elq_charging_menu: Chargement ELQ

# App smile
app_smile: WS AppSmile
app_smile-app_id_android: APP ID - ANDROID
app_smile-app_id_ios: APP ID - IOS
app_smile-partner_id: Partner ID
app_smile-partner_secret: Partner Secret
app_smile_url: WS AppSmile URL
app_smile_url-url : URL

#pickup & delivery
pickup_delivery_enabled: 'Cueillette et livraison : activation'
pickup_delivery: WS Cueillette et livraison
pickup_delivery-url: URL
pickup_delivery-access_key_id: ID de clé d'accès
pickup_delivery-secret_access_key: Clé d'accès secrète

# functionnalities list
functionnalities:
  title_menu: Liste des fonctionnalités
  functionnalities_title : Liste des fonctionnalités
  legends:
    functionnality_active: Fonctionnalité Active
    functionnality_disabled: Fonctionnalité Désactive
    functionnality_active_prdv_solution_central: Fonctionnalité Active en PRDV pour solution Centrale
    functionnality_active_prdv_solution_local: Fonctionnalité Active en PRDV pour solution Locale
    early_adapter: Early adapter
  ASSISTANCE_CONTACT: Assistance Contact
  info_carburant: Informations carburant
  ASSISTANCE_DEALER: Assistance Dealer
  BIOMETRICS: Biometrics
  BTA: BTA
  cam: Cam
  CBUDDY: CBUDDY
  DEALER: DEALER
  DIMBO: DIMBO
  EV_ROUTING_PLANNER: EV ROUTING PLANNER

#Email Update
emailupdate: Mise à jour e-mail
email_update_menu: Email update

# send2nav
send2nav: Send 2 Nav
send2nav_menu: Send 2 Nav

#search
search: Rechercher...

# SmartApps last_trips
last_trips: Get Last Trip
last_trips_menu: Get Last Trip SmartApps

# WS api dealer checkbox
api_dealer-checkbox: Use v2

# RAIA (Regulation Authorities Information Access)
raia : RAIA
raia_menu : RAIA
raia_codes: Codes

# shop
shop:
  parameters_menu: Shop
  subscription_url: Url Souscription 
  url_tmts: URL TMTS
  activation_tmts: Activation TMTS 
  url_zar: Url ZAR 
  activation_zar: Activation ZAR 
  url_nav: Url NAV 
  activation_nav: Activation NAV 
  activation_navco: Activation NAVCO Activation
  activation_connected_packs: Activation Connected Packs 
  url_eboutique: Url Eboutique 
  activation: Activation

sams_url:
  url: Url
  activation: Activation
  menu: Sams urls

prod: Production
preprod: Pré-Production
rfrec: Recette
integ: Integration

#Personal Information Usage
personal_information_usage_title: Utilisation des informations personnelles
personal_information_usage_menu: Personal Information Usage

#location Information Usage
location_information_usage_title: Utilisation des informations de localisation
location_information_usage_menu: Location Information Usage


# setPartialCharge
set_partial_charge: Set Partial Charge
set_partial_charge_menu: Set Partial Charge

# Sunset 
sunset:
  level: Niveau
  redirect_uri_android: Android store
  redirect_uri_ios: Apple store
  title: Titre
  body: Body
  media: Media
  menu: Sunset
  label: Sunset configuration
  disabled: Désactiver
  warning: Alerte
  blocking: Bloquer


SAMS_EMERGENCY : Private eCall vehicle 
SAMS_GRIPMANAGEMENT: Advanced Traction Control 
SAMS_HEATEDSTEERINGWHEEL: Heated Steering Wheel
