up:
	 docker compose  -f _init/docker/docker-compose.yml up -d --build --remove-orphans
	 docker ps

down:
	 docker compose -f _init/docker/docker-compose.yml down

restart: down up

clean:
	 docker system prune -f

restart_db:
	 docker compose -f _init/docker/docker-compose.yml restart db

php_shell:
	 docker exec -ti -w /var/www/html space-bo_app /bin/zsh

prepare_test:
	php bin/console cache:clear --env=test
	php bin/console doctrine:database:drop --force --env=test
	php bin/console doctrine:database:create --env=test
	php bin/console doctrine:schema:create --env=test
	php bin/console doctrine:migrations:migrate --env=test --no-interaction
	## load fixtures
	php bin/console hautelook:fixtures:load --env=test --purge-with-truncate --no-interaction

composer_reinstall:
	rm -rf vendor
	composer install
	php bin/console cache:clear --env=dev

regenerate_dev:composer_reinstall console_regenerate_db_test

reset_running_branch: regenerate_dev console_regenerate_db_test

# to use from into the php container
cc:
	php bin/console cache:clear

# php-cs-fixer
php-cs-fix:
	vendor/bin/php-cs-fixer fix
	
php-cs-fix-dry:
	vendor/bin/php-cs-fixer fix --dry-run -v

php-check-commented-code:
	vendor/bin/easy-ci check-commented-code src
	vendor/bin/easy-ci check-commented-code tests

# Define the name of the text file containing file paths
FILE_PATHS_TXT = branch-files.list

# Create a text file containing files list of files changed in the last commit
create_files_list:
	git diff --name-only HEAD..develop > $(FILE_PATHS_TXT)
	bat $(FILE_PATHS_TXT)

# Target to run PHP-CS-Fixer for all files listed in the text file
php-cs-fix-files-in-list:
	while IFS= read -r file; do \
	    vendor/bin/php-cs-fixer fix "$$file" --rules=@Symfony --using-cache=no; \
	done < $(FILE_PATHS_TXT)

console_regenerate_db_dev:
	php bin/console doctrine:cache:clear-metadata --env=dev
	php bin/console cache:clear --env=dev
	php bin/console doctrine:database:drop --env=dev --force
	php bin/console doctrine:database:create --env=dev
	php bin/console doctrine:migrations:migrate --env=dev
	
console_regenerate_db_test:
	php bin/console doctrine:cache:clear-metadata --env=test
	php bin/console cache:clear --env=test
	php bin/console doctrine:database:drop --env=test --force
	php bin/console doctrine:database:create --env=test
	php bin/console doctrine:migrations:migrate --env=test --no-interaction
	## load fixtures
	php bin/console hautelook:fixtures:load --env=test --purge-with-truncate --no-interaction

install_tools:
	phive install -g phpmd
	phive install -g php-cs-fixer
	phive install -g psalm
	phive install -g phpstan
	
remove_tools:
	phive remove phpmd
	phive remove php-cs-fixer
	phive remove psalm
	phive remove phpstan

# stop workers
#Not for space BO
mss_stop_worker:
	php bin/console messenger:stop-workers

mss_consume:
	php bin/console messenger:consume mop_sqs_queue -vv --queues=queue-api-gateway

enqueue_consume_one:
# php bin/console messenger:consume mop_sqs_queue -vv --queues=queue-api-gateway --limit=1
# APP_ENV=dev php bin/console enqueue:consume --message-limit=1 -vvv --env=dev   
	php ./bin/console enqueue:consume --client="sqs" -vvv --message-limit=1

db_rebuild:
	php bin/console docache:clear
	php bin/console dodoctrine:database:drop --force
	php bin/console dodoctrine:database:create
	php bin/console dodoctrine:migrations:migrate
	php bin/console doctrine:schema:validate


phpstan_analyse_src:
	vendor/bin/phpstan analyse -l 6 -c phpstan.neon --memory-limit=1G --no-progress --error-format=table --no-interaction --ansi src 

coverage-html:
	XDEBUG_MODE=coverage php bin/phpunit --coverage-html var/reports

coverage-text:
	XDEBUG_MODE=coverage php bin/phpunit --coverage-text