node () {
    def projectName = env.<PERSON><PERSON>CH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    def directory = pwd();
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date());
    def targetEnvironment = 'test';
    def appName = 'mymBack'

    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);

    timestamps {
    try {
            // Checkout
            stageCheckout()

            //load common jenkinsfile
            def rootDir = pwd()
            def functions = load "${rootDir}/Jenkinsfile_common"

            //Initializate environment
            functions.stageInit()

            //Install
            functions.stageInstall(currentDate, projectName, targetEnvironment, appName)

            // Push Image
            functions.stagePushImage(currentDate, targetEnvironment, appName)

            // DEPLOY
            functions.stageDeploy(currentDate, targetEnvironment, appName)

            // Clean Container
            functions.stageCleanContainer(currentDate)


        } catch(e) {
            throw e;
        } finally {
            try{
                //check
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}


////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
        build job: '/INFRA/BATCH/MYM_BATCH_INTEG/master', wait: false, propagate: false
    }
}
