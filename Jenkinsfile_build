node () {
    def projectName = env.<PERSON><PERSON>CH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    def directory = pwd();
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    def targetEnvironment = 'build';

    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);

    timestamps {

        try {
            // BUILD
            build(currentDate, projectName, targetEnvironment)
            // Push Image
            stagePushImage(currentDate, targetEnvironment)
        } catch(e) {
            throw e;
        } finally {
            try{
                //     sh " ls"
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}
///////////////////////////////////
//  - GIT PULL
//  - Install dependencies
//  - PUSH
//  - DEPLOY
///////////////////////////////////


def build(String currentDate, String projectName, String targetEnvironment) {
    stageCheckout()
    stageInstall(currentDate, projectName, targetEnvironment)
    // TODO : tests
}

////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
    }
}

/////////////////////////////////////
// BUILD DE L'IMAGE
/////////////////////////////////////
def stageInstall(String currentDate, String projectName, String targetEnvironment) {
    stage('Install') {
        sh "sed -i='' 's/CMD/#/' _init/docker/image/Dockerfile"
        sh "/usr/local/bin/docker-compose --project-name=${projectName} --file _init/docker/docker-compose_build.yml up --build -d"
    }
}

////////////////////////////////////
// PUSH IMAGE IN ECR (ECS SERVICE AWS)
///////////////////////////////////
def stagePushImage(String currentDate, String targetEnvironment) {
    stage('Push') {
        sh "bash ./scripts/push.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate}"
    }
}
