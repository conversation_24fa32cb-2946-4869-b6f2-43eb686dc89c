<?php
namespace App\Controller\Bo\Admin;

use App\Entity\PsaProfile;
use App\Entity\{VehicleLabel, LocalVehicleModel};
use App\Form\LocalLcdvLabelsType;
use App\Repository\{VehicleLabelRepository, LocalVehicleModelRepository, VehicleDefaultImageRepository};
use App\Service\VehicleLabelFormManager;
use App\Service\{VehicleLabelService, WSParametersService, VehicleVisualService};
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("admin/profile/{profile}/local_model_label", name="local_model_label_")
 */
class LocalModelLabelController extends AbstractController
{
    const V3D_SECTION = 'visual3d';
    const SOURCE_APP= 'APP';
    /**
     * @Route("", name="index")
     * @param PsaProfile $profile
     * @param VehicleLabelService $service
     * @param VehicleDefaultImageRepository $vehicleDefaultImageRepository
     * @return Response
     */
    public function index(PsaProfile $profile, VehicleLabelService $service, VehicleDefaultImageRepository $vehicleDefaultImageRepository) {
        $site = $profile->getSite();
        $language = $site->getPreferedLanguage();
        if ($site->getLanguages()->count() == 1) {
            $language = $site->getLanguages()->get(0);
        }
        $vehicleLabels = $service->normalizeData($site, $language);
        return $this->render('admin/local_model_label/index.html.twig', [
            'profile'        => $profile,
            'vehicle_labels' => $vehicleLabels,
            'brand'=>$site->getBrand(),
            'defaultImage' => $vehicleDefaultImageRepository->findOneByBrand($site->getBrand())
        ]);
    }

    /**
     * @Route("/edit/{label}", name="edit", methods={"GET","POST"})
     * @param PsaProfile $profile 
     * @param VehicleLabel $label 
     * @param VehicleLabelFormManager $manager 
     * @param Request $request 
     * @param WSParametersService $wSParametersService 
     * @param LocalVehicleModelRepository $localModelReository 
     * @param VehicleVisualService $visualVehicleService 
     * @return Response
     */
    public function edit(PsaProfile $profile, VehicleLabel $label, VehicleLabelFormManager $manager, Request $request, WSParametersService $wSParametersService, LocalVehicleModelRepository $localModelReository, VehicleVisualService $visualVehicleService) {
        $site = $profile->getSite();
        $labelTranslations = $manager->getLocalFormData($profile->getSite(), $label); 
        $localModel = $localModelReository->findOneBy(['site'=> $profile->getSite(), 'globalVehicleModel'=>$label]);

        $em = $this->getDoctrine()->getManager();

        if (!$localModel) {
            $localModel = new LocalVehicleModel();
            $localModel->setGlobalVehicleModel($label)
                ->setSite($profile->getSite());
            $em->persist($localModel);
            $em->flush();
        }

        foreach ($labelTranslations['labels'] as $labelTranslation) {
            $localModel->addLabel($labelTranslation);
        }
        $em->flush();
        
        #prepare V3D URL
        $v3dResponse = $visualVehicleService->getView3D(self::SOURCE_APP, $label->getBrand(), $localModel->getGlobalVehicleModel()->getLcdvExample(), null, $localModel);

        $form = $this->createForm(LocalLcdvLabelsType::class, $localModel);

        $form->handleRequest($request);

        if ($form->isSubmitted()) {

            $form = $manager->manageUpdateLocalLabelsForm($form, $localModel);
            if ($form->getErrors(true)->count() == 0) {
                $this->addFlash('success', 'local_lcdv_updated');
            } else {
                $this->addFlash('error', 'local_label_error');
            }

            return  $this->redirectToRoute('local_model_label_edit', [
                                'profile' => $profile->getId(),
                                'label'   => $label->getId()
                            ]);
        }
        return $this->render('admin/local_model_label/edit.html.twig', [
            'form'        => $form->createView(),
            'profile'     => $profile,
            'brand'       => $site->getBrand(),
            'label'       => $label,
            'model'       => $localModel,
            'medias'      => $profile->getSite()->getMedias(),
            'defaultView' => $v3dResponse['view'],
            'v3d_url'     => $v3dResponse['url'],
        ]);
    }
}
