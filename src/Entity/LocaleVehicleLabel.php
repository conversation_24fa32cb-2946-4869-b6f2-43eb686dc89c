<?php

namespace App\Entity;

use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ORM\Entity(repositoryClass="App\Repository\LocaleVehicleLabelRepository")
 * @ORM\HasLifecycleCallbacks
 */
class LocaleVehicleLabel
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="datetime", nullable=true, name="creation_date")
     */
    private $creationDate;

    /**
     * @ORM\ManyToOne(targetEntity=VehicleLabel::class)
     * @ORM\JoinColumn(name="vehicle_label_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $vehicleLabel;

    /**
     * @ORM\ManyToOne(targetEntity=PsaSite::class, inversedBy="translations")
     */
    private $site;

    /**
     * @ORM\ManyToOne(targetEntity=PsaLanguage::class)
     * @ORM\JoinColumn(name="langue_id", referencedColumnName="langue_id")
     */
    private $language;

    /**
     * @ORM\Column(type="string", length=255, nullable=true, name="local_label")
     * @Assert\NotBlank(message="lcdvlabel_not_blank")
     */
    private $localLabel;

    /**
     * @ORM\ManyToOne(targetEntity="App\Entity\LocalVehicleModel", inversedBy="label")
     */
    private $localeVehicleModel;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreationDate(): ?DateTimeInterface
    {
        return $this->creationDate;
    }

    public function setCreationDate(?DateTimeInterface $creationDate): self
    {
        $this->creationDate  = $creationDate;

        return $this;
    }

    /**
     * @return VehicleLabel
     */
    public function getVehicleLabel()
    {
        return $this->vehicleLabel;
    }

    /**
     * @param VehicleLabel $vehicleLabel
     * @return LocaleVehicleLabel
     */
    public function setVehicleLabel(VehicleLabel $vehicleLabel): self
    {
        $this->vehicleLabel = $vehicleLabel;
        return $this;
    }

    /**
     * @return PsaSite
     */
    public function getSite(): PsaSite
    {
        return $this->site;
    }

    /**
     * @param PsaSite $site
     *
     * @return LocaleVehicleLabel
     */
    public function setSite(PsaSite $site): self
    {
        $this->site = $site;

        return $this;
    }

    /**
     * @return PsaLanguage
     */
    public function getLanguage(): PsaLanguage
    {
        return $this->language;
    }

    /**
     * @param  PsaLanguage  $language
     *
     * @return $this
     */
    public function setLanguage(PsaLanguage $language): self
    {
        $this->language = $language;

        return $this;
    }

    /**
     * @ORM\PrePersist
     */
    public function updatedTimestamps(): void
    {
        $this->setCreationDate(new \DateTime('now'));
    }

    /**
     * @return string
     */
    public function getLocalLabel()
    {
        return $this->localLabel;
    }

    /**
     * @param string $localLabel
     * @return LocaleVehicleLabel
     */
    public function setLocalLabel(?string $localLabel): self
    {
        $this->localLabel = $localLabel;

        return $this;
    }

    public function getLocaleVehicleModel(): ?LocalVehicleModel
    {
        return $this->localeVehicleModel;
    }

    public function setLocaleVehicleModel(?LocalVehicleModel $localeVehicleModel): self
    {
        $this->localeVehicleModel = $localeVehicleModel;

        return $this;
    }

}
