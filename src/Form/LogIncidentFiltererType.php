<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class LogIncidentFiltererType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('vin', TextType::class, [
                'label'    => 'VIN',
                'required' => false,
            ])
            ->add('email', TextType::class, [
                'label'    => 'Email',
                'required' => false,
            ])
            ->add('date', TextType::class, [
                'label'    => 'Date debut',
                'required' => false,
            ])

            ->add('end_date', TextType::class, [
                'label'    => 'Date fin',
                'required' => false,
            ])
        ;
    }

}
