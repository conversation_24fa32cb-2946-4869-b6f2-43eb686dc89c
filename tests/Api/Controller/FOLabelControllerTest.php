<?php

namespace Tests\Api\Controller;

use Symfony\Component\HttpFoundation\Response;
use Tests\Fixtures\{FOLabelFixture, PsaLanguageFixture, PsaSiteFixture};

/**
 * Test unitaire => Api/FOLabelController
 */
class FOLabelControllerTest extends TestCase
{
    /**
     * @var \Symfony\Bundle\FrameworkBundle\KernelBrowser
     */
    private $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();

        static::createSchemasWithFixtures($this->client, [
            new PsaLanguageFixture,
            new PsaSiteFixture,
            new FOLabelFixture,
        ]);
    }

    /** @test */
    public function it_can_get_all_fo_label_by_brand_and_country()
    {
        $this->client->request('GET', '/v1/folabels', ['brand' => 'AP', 'country' => 'FR', 'language' => 'fr']);
        $response = $this->client->getResponse();

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);

        static::assertSuccessResponse($content);

        foreach ($content['data'] as $data) {
            static::assertFoLabelItem($data);
        }

        static::assertCount(2, $content['data']);
    }

    /** @test */
    public function it_can_get_all_fo_label_by_brand_and_country_on_lowercase()
    {
        $this->client->request('GET', '/v1/folabels', ['brand' => 'ac', 'country' => 'fr', 'language' => 'fr']);
        $response = $this->client->getResponse();

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);

        static::assertSuccessResponse($content);

        static::assertCount(2, $content['data']);

        foreach ($content['data'] as $data) {
            static::assertFoLabelItem($data);
        }
    }

    /**
     * @test
     *
     * @dataProvider getLanguageDataProvider
     *
     * @param  string  $language
     * @param  array  $expected
     */
    public function it_can_get_all_fo_label_by_brand_and_country_on_lowercase_with_specified_lang(string $language, array $expected)
    {
        $this->client->request('GET', '/v1/folabels', ['brand' => 'ac', 'country' => 'fr', 'language' => $language]);
        $response = $this->client->getResponse();

        if ( ! $response->isOk())
            dd($response->getContent());

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);

        static::assertSuccessResponse($content);

        static::assertCount(2, $content['data']);

        foreach ($content['data'] as $data) {
            static::assertFoLabelItem($data);
        }

        static::assertEquals($expected, $content['data']);
    }

    public function getLanguageDataProvider(): array
    {
        return [
            [
                'en', [
                    [
                        'keyLabel'        => 'key_3',
                        'translationPays' => 'key_3_translation',
                    ],
                    [
                        'keyLabel'        => 'key_4',
                        'translationPays' => 'Key 4',
                    ],
                ],
            ],
            [
                'fr', [
                    [
                        'keyLabel'        => 'key_3',
                        'translationPays' => 'cle_3_traduction',
                    ],
                    [
                        'keyLabel'        => 'key_4',
                        'translationPays' => 'cle_4_traduction',
                    ],
                ],
            ],
        ];
    }

    /** @test */
    public function it_must_fail_when_brand_and_country_missing()
    {
        $this->client->request('GET', '/v1/folabels');
        $response = $this->client->getResponse();

        static::assertFalse($response->isOk());
        static::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        static::assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);
        $expected = [
            'success'     => 'validation_failed',
            'status'   => Response::HTTP_UNPROCESSABLE_ENTITY,
            'messages' => [
                'brand'   => 'Cette valeur doit être l\'un des choix proposés.',
                'country' => 'Cette valeur ne doit pas être vide.',
                'language' => 'Cette valeur ne doit pas être vide.',
            ],
        ];

        static::assertEquals($expected, $content);
    }

    /**
     * Assert success response structure.
     *
     * @param  array  $items
     */
    protected static function assertSuccessResponse(array $items): void
    {
        $expected = ['success', 'status', 'data'];

        static::assertEquals($expected, array_keys($items));
    }

    /**
     * Assert social network structure.
     *
     * @param  array  $item
     */
    protected static function assertFoLabelItem(array $item): void
    {
        $expected = ['keyLabel', 'translationPays'];

        static::assertEquals($expected, array_keys($item));
    }
}
