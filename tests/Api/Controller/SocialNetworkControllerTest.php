<?php

namespace Tests\Api\Controller;

use Symfony\Component\HttpFoundation\Response;
use Tests\Fixtures\PsaFixtures;

/**
 * Test unitaire => Api/SocialNetworkController
 */
class SocialNetworkControllerTest extends TestCase
{
    /**
     * @var \Symfony\Bundle\FrameworkBundle\KernelBrowser
     */
    private $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();

        static::createSchemasWithFixtures($this->client, [
            new PsaFixtures,
        ]);
    }

    /** @test */
    public function it_can_get_all_social_networks_by_brand_and_country()
    {
        $this->client->request('GET', '/v1/socials', ['brand' => 'AP', 'country' => 'FR']);
        $response = $this->client->getResponse();

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $items = json_decode($response->getContent(), true);

        static::assertCount(2, $items);
        foreach ($items as $item) {
            static::assertSocialNetworkItem($item);
        }
    }

    /** @test */
    public function it_can_get_all_social_networks_by_brand_and_country_on_lowercase()
    {
        $this->client->request('GET', '/v1/socials', ['brand' => 'ac', 'country' => 'fr']);
        $response = $this->client->getResponse();

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $items = json_decode($response->getContent(), true);

        static::assertCount(2, $items);
        foreach ($items as $item) {
            static::assertSocialNetworkItem($item);
        }
    }

    /** @test */
    public function it_can_get_all_social_networks_by_brand_and_country_with_correct_order()
    {
        $this->client->request('GET', '/v1/socials', ['brand' => 'AP', 'country' => 'ES']);
        $response = $this->client->getResponse();

        static::assertTrue($response->isOk());
        static::assertJson($response->getContent());

        $items = json_decode($response->getContent(), true);

        static::assertCount(3, $items);
        foreach ($items as $item) {
            static::assertSocialNetworkItem($item);
        }

        static::assertSame([
            [
                'socialtype' => 'twitter',
                'socialLink' => 'http://twitter.com/ap/es',
            ], [
                'socialtype' => 'linkedin',
                'socialLink' => 'http://linkedin.com/ap/es',
            ], [
                'socialtype' => 'facebook',
                'socialLink' => 'http://facebook.com/ap/es',
            ],
        ], $items);
    }

    /** @test */
    public function it_must_fail_when_brand_and_country_missing()
    {
        $this->client->request('GET', '/v1/socials');
        $response = $this->client->getResponse();

        static::assertFalse($response->isOk());
        static::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        static::assertJson($response->getContent());

        $data = json_decode($response->getContent(), true);
        $expected = [
            'success'     => 'validation_failed',
            'status'   => Response::HTTP_UNPROCESSABLE_ENTITY,
            'messages' => [
                'brand'   => 'Cette valeur doit être l\'un des choix proposés.',
                'country' => 'Cette valeur ne doit pas être vide.',
            ],
        ];

        static::assertEquals($expected, $data);
    }

    /**
     * Assert social network structure.
     *
     * @param  array  $item
     */
    protected static function assertSocialNetworkItem(array $item)
    {
        $expectedKeys = ['socialtype', 'socialLink'];

        static::assertEquals($expectedKeys, array_keys($item));
    }
}
