<?php

namespace Tests\Api\Controller;

use Doctrine\Common\DataFixtures\Executor\ORMExecutor;
use Doctrine\Common\DataFixtures\Purger\ORMPurger;
use Doctrine\ORM\Tools\SchemaTool;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

abstract class TestCase extends WebTestCase
{
    /**
     * Create schemas and load the fixtures.
     *
     * @param  \Symfony\Bundle\FrameworkBundle\KernelBrowser  $client
     * @param  array                                          $fixtures
     */
    protected static function createSchemasWithFixtures($client, array $fixtures): void
    {
        /** @var   \Doctrine\ORM\EntityManagerInterface $em */
        $em = $client->getContainer()->get('doctrine')->getManager();

        // Create db schemas
        (new SchemaTool($em))->createSchema(
            $em->getMetadataFactory()->getAllMetadata()
        );

        // Load fixtures
        (new ORMExecutor($em, new ORMPurger($em)))->execute($fixtures);
    }
}
