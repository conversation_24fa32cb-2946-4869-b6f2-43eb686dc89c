<?php

namespace Tests\Api\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

/**
 * Test unitaire => Api/DSLcdvController
 */
class DSLcdvControllerTest extends WebTestCase
{
    public function testAction()
    {
        $client = static::createClient();
        $client->request('GET', '/v1/checklcdv_ds/LCDV');
        //$response = $client->getResponse();

        $this->assertTrue(true);
        //$this->assertTrue($response->isOk());
        //$this->assertArrayHasKey('content', json_decode($response->getContent(), true));
    }
}
