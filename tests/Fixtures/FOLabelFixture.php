<?php

namespace Tests\Fixtures;

use App\Entity\{FoLabel, LocaleTranslation, PsaLanguage, PsaSite};
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;

class FOLabelFixture extends AbstractFixture implements DependentFixtureInterface
{
    /**
     * This method must return an array of fixtures classes
     * on which the implementing class depends on
     *
     * @return array
     */
    public function getDependencies(): array
    {
        return [
            PsaSiteFixture::class,
        ];
    }

    /**
     * Load data fixtures with the passed EntityManager
     *
     * @param  ObjectManager  $manager
     */
    public function load(ObjectManager $manager)
    {
        foreach (static::getData() as $labelData) {
            $foLabel = (new FoLabel)
                ->setKeyLabel($labelData['key'])
                ->setSprintNumber($labelData['sprint'])
                ->setFrLabel($labelData['label_fr'])
                ->setEnLabel($labelData['label_en'])
                ->setParameterValue($labelData['parameter'])
                ->setFunctionalityName($labelData['functionality']);

            foreach ($labelData['brands'] as $brand) {
                /** @var \App\Entity\FoLabel $foLabel */
                $foLabel = $foLabel->clone()->setBrands($brand);

                foreach ($labelData['sources'] as $source) {
                    $manager->persist(
                        $foLabel = $foLabel->clone()->setSupport($source)
                    );

                    $this->loadTranslations($manager, $foLabel, $labelData['translations']);
                }
            }
        }

        $manager->flush();
    }

    /**
     * Load the translations.
     *
     * @param  ObjectManager  $manager
     * @param  FoLabel        $label
     * @param  array          $translations
     */
    private function loadTranslations(ObjectManager $manager, FoLabel $label, array $translations): void
    {
        foreach ($translations as $translationData) {
            $manager->persist(
                (new LocaleTranslation)
                    ->setDefaultTranslation($translationData['default_translation'])
                    ->setFoLabel($label)
                    ->setSite(
                        $manager->getRepository(PsaSite::class)->findOneBy([
                            'brand' => $label->getBrands()
                        ])
                    )
                    ->setLanguage(
                        $manager->getRepository(PsaLanguage::class)->findOneBy([
                            'code' => $translationData['language']
                        ])
                    )
            );
        }
    }

    /**
     * Get the data for the tests.
     *
     * @return array
     */
    private static function getData(): array
    {
        return [
            [
                'key'           => 'key_1',
                'sprint'        => 1,
                'brands'        => ['AP'],
                'sources'       => ['WEB'],
                'label_fr'      => 'Clé 1',
                'label_en'      => 'Key 1',
                'parameter'     => null,
                'functionality' => 'Func',
                'translations'   => [
                    [
                        'default_translation' => 'cle_1',
                        'language'            => 'fr',
                    ],
                ],
            ],
            [
                'key'           => 'key_2',
                'sprint'        => 1,
                'brands'        => ['AP'],
                'sources'       => ['WEB'],
                'label_fr'      => 'Clé 2',
                'label_en'      => 'Key 2',
                'parameter'     => null,
                'functionality' => 'Func',
                'translations'   => [
                    [
                        'default_translation' => 'cle_2',
                        'language'            => 'fr',
                    ],
                ],
            ],
            [
                'key'           => 'key_3',
                'sprint'        => 1,
                'brands'        => ['AC'],
                'sources'       => ['WEB'],
                'label_fr'      => 'Clé 3',
                'label_en'      => 'Key 3',
                'parameter'     => null,
                'functionality' => 'Func',
                'translations'   => [
                    [
                        'default_translation' => 'cle_3_traduction',
                        'language'            => 'fr',
                    ],
                    [
                        'default_translation' => 'key_3_translation',
                        'language'            => 'en',
                    ],
                ],
            ],
            [
                'key'           => 'key_4',
                'sprint'        => 1,
                'brands'        => ['AC'],
                'sources'       => ['WEB'],
                'label_fr'      => 'Clé 4',
                'label_en'      => 'Key 4',
                'parameter'     => null,
                'functionality' => 'Func',
                'translations'   => [
                    [
                        'default_translation' => 'cle_4_traduction',
                        'language'            => 'fr',
                    ],
                ],
            ],
        ];
    }
}
