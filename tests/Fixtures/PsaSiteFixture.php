<?php

declare(strict_types=1);

namespace Tests\Fixtures;

use App\Entity\{PsaLanguage, PsaSite};
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Persistence\ObjectManager;

/**
 * Class PsaSiteFixture
 * @package Tests\Fixtures
 */
class PsaSiteFixture extends AbstractFixture
{
    /**
     * Load data fixtures with the passed EntityManager
     *
     * @param ObjectManager $manager
     */
    public function load(ObjectManager $manager)
    {
        $languages = $manager->getRepository(PsaLanguage::class)->findAll();

        foreach (static::getData() as $siteData) {
            $site = (new PsaSite)
                ->setLabel($siteData['label'])
                ->setOnlineApp($siteData['online_app'])
                ->setTimezone($siteData['timezone'])
                ->setBrand($siteData['brand'])
                ->setPreferedLanguage(static::getPreferredLanguage($languages, $siteData['preferred_language']))
                ->setCountry($siteData['country'])
                ->setIsAdmin(false)
                ->setLanguages(static::getLanguagesByKeys($languages, $siteData['languages']));

            $manager->persist($site);
        }

        $manager->flush();
    }

    /**
     * Get the preferred language.
     *
     * @param  array|PsaLanguage[]  $languages
     * @param  string               $code
     *
     * @return PsaLanguage|null
     */
    private static function getPreferredLanguage(array $languages, string $code): ?PsaLanguage
    {
        foreach ($languages as $language) {
            if ($language->getCode() === $code)
                return $language;
        }

        return null;
    }

    /**
     * Get the languages by codes.
     *
     * @param  array  $languages
     * @param  array  $codes
     *
     * @return ArrayCollection
     */
    private static function getLanguagesByKeys(array $languages, array $codes): ArrayCollection
    {
        return new ArrayCollection(
            array_filter($languages, function (PsaLanguage $language) use ($codes) {
                return in_array($language->getCode(), $codes);
            })
        );
    }

    /**
     * Get
     * @return array
     */
    private static function getData(): array
    {
        return [
            [
                'label'              => 'FRANCE AP',
                'timezone'           => 'Europe/Paris',
                'online_app'         => true,
                'country'            => 'FR',
                'brand'              => 'AP',
                'preferred_language' => 'fr',
                'languages'          => ['fr']
            ],
            [
                'label'              => 'FRANCE AC',
                'timezone'           => 'Europe/Paris',
                'online_app'         => true,
                'country'            => 'FR',
                'brand'              => 'AC',
                'preferred_language' => 'en',
                'languages'          => ['fr', 'es']
            ]
        ];
    }
}
