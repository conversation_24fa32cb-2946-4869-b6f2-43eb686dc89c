<?php

declare(strict_types=1);

namespace Tests\Fixtures;

use App\Entity\PsaLanguage;
use Doctrine\Common\Persistence\ObjectManager;

/**
 * Class PsaLanguageFixture
 * @package Tests\Fixtures
 */
class PsaLanguageFixture extends AbstractFixture
{
    /**
     * Load data fixtures with the passed EntityManager
     *
     * @param ObjectManager $manager
     */
    public function load(ObjectManager $manager)
    {
        foreach (static::getData() as $languageData) {
            $manager->persist(
                (new PsaLanguage)
                    ->setCode($languageData['code'])
                    ->setLabel($languageData['label'])
                    ->setTranslate($languageData['translate'])
            );
        }

        $manager->flush();
    }

    /**
     * Get the data.
     *
     * @return array
     */
    private static function getData(): array
    {
        return [
            [
                'code'      => 'fr',
                'label'     => 'Français',
                'translate' => 'Français',
            ],
            [
                'code'      => 'en',
                'label'     => 'Anglais',
                'translate' => 'English',
            ],
            [
                'code'      => 'es',
                'label'     => 'Espagnol',
                'translate' => 'Español ,castellano',
            ],
        ];
    }
}
