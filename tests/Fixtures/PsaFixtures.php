<?php

namespace Tests\Fixtures;

use App\Entity\{Psa<PERSON>anguage, PsaProfile, PsaRole, PsaSite, SocialNetwork};
use Doctrine\Common\Persistence\ObjectManager;

class PsaFixtures extends AbstractFixture
{
    /**
     * Load data fixtures depuis un fichier Yaml.
     *
     * @param ObjectManager $manager
     */
    public function load(ObjectManager $manager)
    {
        $this->loadSites($manager, static::getData());

        $manager->flush();
    }

    /**
     * Load the sites.
     *
     * @param ObjectManager $manager
     * @param array $sites
     */
    private function loadSites(ObjectManager $manager, $sites)
    {
        $language = (new PsaLanguage)
            ->setCode('CD')
            ->setTranslate('translate')
            ->setLabel('label');

        $manager->persist($language);

        $role = new PsaRole();
        $role->setLabel("ADMINISTRATOR");
        $manager->persist($role);
        foreach ($sites as $siteData) {
            $site = (new PsaSite)
                ->setLabel($siteData['label'])
                ->setOnlineApp($siteData['online_app'])
                ->setTimezone($siteData['timezone'])
                ->setBrand($siteData['brand'])
                ->setPreferedLanguage($language)
                ->setCountry($siteData['country'])
                ->setIsAdmin(false);
            $manager->persist($site);

            $this->loadProfiles($manager, $site, $siteData['site']['profiles'], $role);
            $this->loadSocialNetworks($manager, $site, $siteData['site']['social_networks']);
        }
    }

    /**
     * Load the profiles.
     *
     * @param ObjectManager $manager
     * @param PsaSite $site
     * @param array $profiles
     */
    private function loadProfiles(ObjectManager $manager, PsaSite $site, array $profiles, PsaRole $role)
    {
        foreach ($profiles as $profileData) {
            $profile = (new PsaProfile)
                ->setRole($role)
                ->setSite($site)
                ->setProfileAdmin(false);

            $manager->persist($profile);
        }
    }

    /**
     * Load the social networks.
     *
     * @param ObjectManager $manager
     * @param PsaSite $site
     * @param array $socialNetworks
     */
    private function loadSocialNetworks(ObjectManager $manager, PsaSite $site, array $socialNetworks)
    {
        foreach ($socialNetworks as $socialNetworkData) {
            $sn = (new SocialNetwork)
                ->setType($socialNetworkData['type'])
                ->setUrl($socialNetworkData['url'])
                ->setIsEnabled((bool) $socialNetworkData['is_enabled'])
                ->setOrderNumber($socialNetworkData['order_number'])
                ->setSite($site);

            $manager->persist($sn);
        }
    }

    /**
     * Get the data for the tests.
     *
     * @return array
     */
    private static function getData(): array
    {
        return [
            [
                'label'      => 'FRANCE AC',
                'timezone'   => 'Europe/Paris',
                'online_app' => true,
                'country'    => 'FR',
                'brand'      => 'AC',
                'site'       => [
                    'profiles' => [
                        [
                            'label' => 'Administateur',
                        ],
                        [
                            'label' => 'Webmaster',
                        ],
                        [
                            'label' => 'Contributeur',
                        ],
                    ],
                    'social_networks' => [
                        [
                            'type' => 'facebook',
                            'url' => "http://facebook.com/ac/fr",
                            'is_enabled' => true,
                            'order_number' => 2,
                        ],
                        [
                            'type' => 'twitter',
                            'url' => "http://twitter.com/ac/fr",
                            'is_enabled' => true,
                            'order_number' => 1,
                        ],
                    ],
                ],
            ],
            [
                'label'      => 'FRANCE AP',
                'timezone'   => 'Europe/Paris',
                'online_app' => true,
                'country'    => 'FR',
                'brand'      => 'AP',
                'site'       => [
                    'profiles' => [
                        [
                            'label' => 'Administateur',
                        ],
                        [
                            'label' => 'Webmaster',
                        ],
                        [
                            'label' => 'Contributeur',
                        ],
                    ],
                    'social_networks' => [
                        [
                            'type' => 'facebook',
                            'url' => "http://facebook.com/ap/fr",
                            'is_enabled' => true,
                            'order_number' => 2,
                        ],
                        [
                            'type' => 'twitter',
                            'url' => "http://twitter.com/ap/fr",
                            'is_enabled' => true,
                            'order_number' => 1,
                        ],
                    ],
                ],
            ],
            [
                'label'      => 'ESPAGNE AP',
                'timezone'   => 'Europe/Madrid',
                'online_app' => true,
                'country'    => 'ES',
                'brand'      => 'AP',
                'site'       => [
                    'profiles'        => [
                        [
                            'label' => 'Administateur',
                        ],
                        [
                            'label' => 'Webmaster',
                        ],
                        [
                            'label' => 'Contributeur',
                        ],
                    ],
                    'social_networks' => [
                        [
                            'type'         => 'facebook',
                            'url'          => 'http://facebook.com/ap/es',
                            'is_enabled'   => true,
                            'order_number' => 3,
                        ],
                        [
                            'type'         => 'twitter',
                            'url'          => 'http://twitter.com/ap/es',
                            'is_enabled'   => true,
                            'order_number' => 1,
                        ],
                        [
                            'type'         => 'linkedin',
                            'url'          => 'http://linkedin.com/ap/es',
                            'is_enabled'   => true,
                            'order_number' => 2,
                        ],
                    ],
                ],
            ],
        ];
    }
}
