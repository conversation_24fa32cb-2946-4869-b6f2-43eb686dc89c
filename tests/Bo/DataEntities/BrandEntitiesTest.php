<?php

namespace Tests\Bo\DataEntities;

use App\DataEntities\BrandEntities;
use Symfony\Component\Validator\Constraints as Assert;
use Tests\Api\Controller\TestCase;

class BrandEntitiesTest extends TestCase
{
    /** @test */
    public function it_can_get_all_brands()
    {
        $expected = ['AP', 'AC', 'DS', 'OP', 'VX'];

        static::assertEquals($expected, BrandEntities::all());
    }

    /** @test */
    public function it_can_get_constraint_for_all_brands()
    {
        $constraints = BrandEntities::getConstraintsForAll();

        // Not blank constraint
        static::assertInstanceOf(Assert\NotBlank::class, $constraints[0]);

        // Choice constraint
        static::assertInstanceOf(Assert\Choice::class, $constraints[1]);

        $expected = ['AP', 'AC', 'DS', 'OP', 'VX'];
        static::assertEquals($expected, $constraints[1]->choices);
    }
}
