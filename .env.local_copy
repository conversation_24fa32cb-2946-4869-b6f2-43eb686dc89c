# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration
DATABASE_URL=mysql://dbback:dbback@db_space_back:3306/sdbback
APP_ENV=dev

###> MEDIA ###
MEDIA_BASE_URL=https://media-develop.space.awsmpsa.com
###< MEDIA ###


###> AWS PARAMETERS ###
# USED_IAM_INSTANCE_PROFILE=false old settings
# AWS_ACCESS_KEY=********************
# AWS_SECRET_KEY=4Ho/lf6GhXBUK5YaP+wAVjnRuxdqxFvfPOZVSWWm
AWS_ROLE_ARN=arn:aws:iam::186546832638:role/Role_MYM_Dev_Access_For_groupePSA
# AWS_ROLE_ARN=arn:aws:s3:::media-integ.mym.awsmpsa.com:role/Role_MYM_Dev_Access_For_groupePSA
# AWS_ROLE_ARN=arn:aws:iam::186546832638:role/Role_MYM_Dev_Access_For_groupePSA
# AWS_ROLE_ARN=arn:aws:iam::xxxxxx:role/xxxx
MEDIA_S3_ID=media-develop.space.awsmpsa.com
DATA_EXPORT_ID=mym-data-export-dev-eu-west-1-186546832638
APP_DATA_BUCKET="application.data"
EXPORT_BASE_URL=""
###< AWS PARAMETERS ###

###> MongoAtlas ###
MONGO_ATLAS_BASE_URL=https://eu-west-2.aws.data.mongodb-api.com
MONGO_ATLAS_API_KEY=a2aeA5fc2fdjM8Ef80wPoAiMdmovBiNjdzJicGyFoSVF8On75OTvcNs9WCYr21KX
MONGO_APP=data-kvfzp
MONGO_DATABASE=spaceDb
MONGO_DATASOURCE=Space
###< MongoAtlas ###

USED_IAM_INSTANCE_PROFILE=false
AWS_ACCESS_KEY=********************
AWS_SECRET_KEY=G/W3ZPCHURxvbayPIbvPAG0bL423PDdOMryLXA8S
S3_SETTINGS_BUCKET=space-settings-develop.space.awsmpsa.com
S3_SETTINGS_BUCKET_URL=https://space-settings-develop.space.awsmpsa.com

MAX_MEDIA_SIZE=200000000


# APP_DEBUG=0