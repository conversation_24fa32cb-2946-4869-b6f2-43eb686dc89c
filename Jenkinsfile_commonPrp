/////////////////////////////////////
	// CREATE GIT TAG
	/////////////////////////////////////
   def stageTagGitCreate() {
	   stage('TagGitCreate') {
          sh "bash ./scripts/tag.sh  ${version} ${justification}"
	   }
	}

	/////////////////////////////////////
	// ENVIRONMENT INITIALIZATION
	/////////////////////////////////////
   def stageInit() {
	   stage('EnvInitialization') {
		   script { folderName = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date()); }
		   sh "<NAME_EMAIL>:D4UDigitalPlatform/mymarque-aws-infra.git /tmp/${folderName}"
		   sh "cp -r  /tmp/${folderName}/config/aws/_init ."
		   sh "cp -r /tmp/${folderName}/deployment/scripts ."
		   sh "rm -rf /tmp/${folderName}"
	   }
	}

	/////////////////////////////////////
	// INSTALL BY USER : COMPOSER INSTALL - BUILD APP
	/////////////////////////////////////
   def stageInstall(String currentDate, String projectName, String targetEnvironment, String appName) {
	   stage('Install') {
		   GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'(%h) %cd'", returnStdout: true)
		   sh "rm -rf ./.git"
		    sh "bash ./scripts/aws_login_docker.sh"
		   sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file _init/docker/docker-compose_aws.yml build --no-cache"
                   sh "CONTAINER_NAME=${currentDate} /usr/local/bin/docker-compose --project-name=${currentDate} --file _init/docker/docker-compose_aws.yml up -d"

		   sh "docker exec ${currentDate} bash -c 'bash /var/www/tmp/scripts/install.sh ${targetEnvironment} ${appName} --gitcommit \"${GIT_COMMIT_HASH}\"'"
	   }
	}

   ////////////////////////////////////
   // PUSH   IN ECR (ECS SERVICE AWS)
   ///////////////////////////////////
   def stagePushImage(String currentDate, String targetEnvironment, String appName) {
	   stage('Push') {
		   sh "bash ./scripts/push.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	   }
	}

   ////////////////////////////////////
   // DEPLOY IMAGE
   ///////////////////////////////////
   def stageDeploy(String currentDate, String targetEnvironment, String appName) {
	   stage('Deploy') {
		   sh "bash ./scripts/deploy/deploy.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	   }
	}

	////////////////////////////////////

	/////////////////////////////////////
    //  SDI PROCESSING
    /////////////////////////////////////
       def stageTaaS(String appName, String containerName) {
    	   stage('TaaS') {
    		   sh "docker exec ${containerName} bash -c 'bash /var/build/scripts/taasPrp.sh  ${version} ${justification} ${appName} '"
    	   }
    	}

    ////////////////////////////////////

    /////////////////////////////////////
    // CREATE Rollback Version
    /////////////////////////////////////
    def stageRollbackVersion(String apptag, String appName, String targetEnvironment) {
    	   stage('RollbackVersion') {
              sh "bash ./scripts/rollback.sh  ${apptag} ${appName} ${targetEnvironment} "
    	   }
    	}
    ////////////////////////////////////

   ////////////////////////////////////
   // Clean Container
   ///////////////////////////////////
   def stageCleanContainer(String currentDate) {
	   stage('ContainerClean') {
		   sh "bash ./scripts/container.sh ${currentDate}"
	   }
	}

   ////////////////////////////////////


   return this
