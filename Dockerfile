# ./Dockerfile
FROM php:7.4-cli

# Install dependencies
RUN apt-get update && \
    apt-get install -y git unzip zip curl libzip-dev && \
    docker-php-ext-install zip

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php && \
    mv composer.phar /usr/local/bin/composer

# Set working directory
WORKDIR /app

# Copy everything (Docker mount will override during runtime)
COPY . /app
