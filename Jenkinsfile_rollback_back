properties([parameters([gitParameter(branch: '', branchFilter: '.*', defaultValue: 'origin/master', description: 'list of tags', name: 'apptag', quickFilterEnabled: false, selectedValue: 'NONE', sortMode: 'NONE', tagFilter: '*', type: 'PT_TAG')])])

node () {
    def projectName = env.BRANCH_NAME.toLowerCase().replaceAll(/[^a-z0-9]/, "");
    def directory = pwd();
    String currentDate = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date());
    def targetEnvironment = 'prod';
    def appName = 'mymBack'

    printf('current branch %1s', [env.BRANCH_NAME]);
    printf('current target environment %1s', [targetEnvironment]);

    timestamps {
    try {
            // Checkout
            stageCheckout()

            //load common jenkinsfile
            def rootDir = pwd()
            def functions = load "${rootDir}/Jenkinsfile_common"

            //Initializate environment
            functions.stageInit()

            //rollback  version
            functions.stageRollbackVersion(params.apptag, appName, targetEnvironment)

        } catch(e) {
            throw e;
        } finally {
            try{
                //check
            } catch(ex) {
                println(ex.toString());
            }
        }
    }
}


////////////////////////////////////
// GIT PULL FOR JENKINS
///////////////////////////////////
def stageCheckout() {
    stage('Checkout') {
        deleteDir()
        checkout scm
    }
}



