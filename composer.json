{"type": "project", "license": "proprietary", "require": {"php": "^7.1.3", "ext-ctype": "*", "ext-iconv": "*", "aws/aws-sdk-php": "^3.133", "doctrine/annotations": "^1.7", "enqueue/sqs": "^0.10.4", "friendsofsymfony/ckeditor-bundle": "^2.2", "friendsofsymfony/rest-bundle": "^2.5", "hslavich/oneloginsaml-bundle": "^1.4", "jms/serializer-bundle": "^3.4", "nelmio/api-doc-bundle": "^3.4", "predis/predis": "^1.1", "sensio/framework-extra-bundle": "^5.1", "sroze/messenger-enqueue-transport": "^0.5.0", "symfony/asset": "4.3.*", "symfony/console": "4.3.*", "symfony/dom-crawler": "4.3.*", "symfony/dotenv": "4.3.*", "symfony/expression-language": "4.3.*", "symfony/flex": "^1.3.1", "symfony/form": "4.3.*", "symfony/framework-bundle": "4.3.*", "symfony/http-client": "4.3.*", "symfony/intl": "4.3.*", "symfony/messenger": "4.3.*", "symfony/monolog-bundle": "^3.5", "symfony/orm-pack": "*", "symfony/phpunit-bridge": "^4.3", "symfony/process": "4.3.*", "symfony/security-bundle": "4.3.*", "symfony/serializer-pack": "*", "symfony/swiftmailer-bundle": "^3.1", "symfony/translation": "4.3.*", "symfony/twig-bundle": "4.3.*", "symfony/validator": "4.3.*", "symfony/web-link": "4.3.*", "symfony/yaml": "4.3.*"}, "require-dev": {"ext-json": "*", "doctrine/doctrine-fixtures-bundle": "^3.2", "symfony/browser-kit": "4.3.*", "symfony/css-selector": "4.3.*", "symfony/debug-pack": "*", "symfony/profiler-pack": "*", "symfony/test-pack": "*", "symfony/web-server-bundle": "4.3.*", "symfony/maker-bundle": "^1.23"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "4.3.*"}}}