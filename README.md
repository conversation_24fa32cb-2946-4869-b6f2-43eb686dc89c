MYM_BACK
========================

Back office of Mymarque-Web.

* PRD code : MYM

aws-sdk-php example (integ env)
------------


```
namespace App\Controller\Bo;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Service\AwsSdkService;

class DemoController extends AbstractController
{
    /**
     * @Route("/aws", name="aws")
     */
    public function index(AwsSdkService $awsSdkService)
    {
        //send file to S3
        $s3 = $awsSdkService->getS3Client();
        $media_s3_id = $this->getParameter('media_s3_id');
        $s3->putObject([
            'Bucket' => $media_s3_id,
            'Key' => 'directory_name/file_name.jpg',
            'Body' => fopen('/tmp/file_name.jpg', 'r'),
        ]);

        //dispaly file :
        // https://media-integ.mym.awsmpsa.com/directory_name/file_name.jpg
    }
```

